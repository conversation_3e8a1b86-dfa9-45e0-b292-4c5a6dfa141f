import {
  bootstrapLazy,
  setNonce
} from "./chunk-SKBP5EI5.js";
import "./chunk-Y4T55RDF.js";

// node_modules/@bci-web-core/web-components/dist/esm/polyfills/index.js
function applyPolyfills() {
  var promises = [];
  if (typeof window !== "undefined") {
    var win = window;
    if (!win.customElements || win.Element && (!win.Element.prototype.closest || !win.Element.prototype.matches || !win.Element.prototype.remove || !win.Element.prototype.getRootNode)) {
      promises.push(import(
        /* webpackChunkName: "polyfills-dom" */
        "./dom-OVXWH2WP.js"
      ));
    }
    var checkIfURLIsSupported = function() {
      try {
        var u = new URL("b", "http://a");
        u.pathname = "c%20d";
        return u.href === "http://a/c%20d" && u.searchParams;
      } catch (e) {
        return false;
      }
    };
    if ("function" !== typeof Object.assign || !Object.entries || !Array.prototype.find || !Array.prototype.includes || !String.prototype.startsWith || !String.prototype.endsWith || win.NodeList && !win.NodeList.prototype.forEach || !win.fetch || !checkIfURLIsSupported() || typeof WeakMap == "undefined") {
      promises.push(import(
        /* webpackChunkName: "polyfills-core-js" */
        "./core-js-OKWQJ7OQ.js"
      ));
    }
  }
  return Promise.all(promises);
}

// node_modules/@bci-web-core/web-components/dist/esm/loader.js
var defineCustomElements = (win, options) => {
  if (typeof window === "undefined") return void 0;
  return bootstrapLazy(JSON.parse('[["bci-table-card_3",[[1,"bci-table-card",{"selected":[4],"disabled":[4],"indicatorColor":[1,"indicator-color"],"isMultipleSelection":[4,"is-multiple-selection"]}],[1,"bci-table-card-action",{"icon":[1],"tooltip":[1],"action":[1]}],[1,"bci-table-card-entry",{"entryTitle":[1,"entry-title"],"value":[8],"disabled":[4],"indicatorColor":[1,"indicator-color"]}]]],["bci-card_4",[[1,"bci-card",{"headerColor":[1,"header-color"],"color":[32]},null,{"headerColor":["watchHeaderColorHandler"]}],[1,"bci-card-icon",{"icon":[1],"iconState":[32]},null,{"icon":["watchTypeHandler"]}],[1,"bci-card-text",{"isTitle":[4,"is-title"],"largeText":[4,"large-text"],"label":[1],"value":[1],"tileText":[32]},null,{"isTitle":["watchIsTitleHandler"],"label":["watchLabelHandler"],"value":["watchValueHandler"],"largeText":["watchLargeTextHandler"]}],[1,"bci-card-view"]]],["bci-toolbar_3",[[1,"bci-toolbar"],[1,"bci-toolbar-item",{"value":[1],"stateValue":[32]},null,{"value":["watchValueHandler"]}],[1,"bci-toolbar-spacing"]]],["bci-modal-dialog",[[1,"bci-modal-dialog",{"dialogTitle":[1,"dialog-title"],"config":[16],"open":[64],"close":[64]},null,{"config":["onConfigChange"]}]]],["bci-paginator",[[1,"bci-paginator",{"pageSize":[2,"page-size"],"pageSizeOptions":[1,"page-size-options"],"length":[2],"pageIndex":[2,"page-index"],"showPageSizeSelector":[4,"show-page-size-selector"],"pageIndexCalculationOff":[4,"page-index-calculation-off"],"insideDialogContainer":[4,"inside-dialog-container"],"paginatorInput":[32],"pages":[32]},[[17,"mousedown","clickHandler"]],{"length":["watchLengthHandler"],"pageSizeOptions":["watchPageSizeOptionsHandler"],"pageIndex":["watchPageIndex"],"showPageSizeSelector":["watchShowPageSizeSelector"],"pageSize":["watchPageSize"]}]]],["bci-portal-header-maximized-burger-icon",[[0,"bci-portal-header-maximized-burger-icon"]]],["bci-context-menu_15",[[1,"bci-header",{"mode":[513],"showSettingsIcon":[516,"show-settings-icon"],"breadcrumbArray":[520,"breadcrumb-array"],"headerTitle":[513,"header-title"],"title":[32],"settingsIcon":[32],"breadcrumbLinkList":[32],"styles":[32]},null,{"headerTitle":["onTitleChange"],"showSettingsIcon":["onSettingsIconChange"],"breadcrumbArray":["onBreadcrumbChange"],"mode":["onModeChange"]}],[1,"bci-portal-header-message-icon",{"dateFormat":[1,"date-format"],"localStorageKey":[1,"local-storage-key"],"text":[1025],"messages":[32]},[[8,"bci.portal.message.created","onMessageCreated"],[8,"bci.portal.message.updated","loadMessages"],[8,"bci.portal.header.menu.close","closeMenuPopUp"]]],[4,"bci-portal-header-user-icon",{"text":[1025],"userInfo":[1040],"searchableItems":[1040],"hasMenuItems":[32],"showAccountMenu":[32]},[[8,"bci.portal.header.menu.close","closeMenuPopUp"]],{"searchableItems":["onSearchableItemsChanged"]}],[1,"bci-portal-messages",{"dateFormat":[1,"date-format"],"timeFormat":[1,"time-format"],"localStorageKey":[1,"local-storage-key"],"messages":[32],"searchString":[32],"filterArray":[32],"sortOrder":[32],"showMessgeFilterOptions":[32]},[[8,"bci.portal.message.created","onMessageCreated"],[8,"bci.portal.message.updated","loadMessages"]]],[0,"bci-context-menu",{"showMenu":[32],"actionList":[32],"contextParamValue":[32]},[[8,"bci.portal.showContextActions","showContextMenu"],[8,"bci.portal.closeContextMenu","closeContextMenu"],[1,"wheel","preventWheelDefault"]]],[1,"bci-portal-expand-toggle",{"expanded":[32]}],[1,"bci-portal-footer",{"copyrightLabel":[1,"copyright-label"],"showFooterToggle":[32],"collapseFooter":[32]},[[9,"resize","handleResize"]]],[1,"bci-portal-header-banner",{"multiBanners":[32],"showBanner":[32]},[[8,"bci.portal.banner.show","showBannerMessage"],[8,"bci.portal.banner.hide","hideBanner"]]],[4,"bci-portal-header-icon",{"iconName":[1,"icon-name"],"url":[1],"text":[1025],"menuOpen":[32],"hasMenuItems":[32]},[[8,"bci.portal.header.menu.close","closeMenuPopUp"]]],[0,"bci-portal-header-icon-link",{"url":[1],"text":[1],"external":[4]}],[1,"bci-portal-link",{"url":[1],"text":[1],"external":[4]}],[1,"bci-portal-page-info",{"event":[32]},[[8,"bci.portal.page-info.created","onErrorOccurred"],[8,"bci.portal.page-info.resolved","onErrorResolved"]]],[1,"bci-portal-toaster",null,[[8,"bci.portal.message.created","onMessageCreated"]]],[0,"bci-portal-badge",{"value":[1032],"redBackground":[1028,"red-background"]}],[1,"bci-portal-header-search-icon",{"navigationItems":[1040],"showSearchInput":[32],"searchString":[32],"searchResults":[32]},[[8,"bci.portal.viewsLoaded","componentWillLoad"],[0,"keydown","handleKeyDown"]]]]],["bci-portal-navigation_3",[[1,"bci-portal-navigation",{"hashNavigation":[4,"hash-navigation"],"closed":[4],"mainTitle":[1,"main-title"],"navigationItems":[1040],"threeLevels":[4,"three-levels"],"useLogo":[4,"use-logo"],"emitItemClickOnStartup":[4,"emit-item-click-on-startup"],"selectedNavItem":[32],"expandedNavItem":[32],"toggleNavigationAnimationActive":[32]},[[0,"bci.portal.navigation.itemClicked","handleClickNavItemEvent"],[0,"bci.portal.navigation.itemExpanded","handleExpandedNavItemEvent"],[8,"bci.portal.navigation.toggle","handleToggleNavigationEvent"]],{"navigationItems":["loadNavItem"]}],[4,"bci-portal-navigation-item",{"hashNavigation":[4,"hash-navigation"],"item":[1040],"expanderIcon":[1,"expander-icon"],"navigationClosed":[1028,"navigation-closed"],"expanded":[32]},[[8,"hashchange","handlePopStateEvent"],[8,"popstate","handlePopStateEvent"],[8,"bci.portal.changedUrl","handlePopStateEvent"]],{"navigationClosed":["watchNavigationClosed"],"item":["watchItem"]}],[4,"bci-portal-navigation-submenu",{"expandedNavItem":[16]}]]],["bci-treelist_2",[[1,"bci-treelist",{"listItems":[16]}],[1,"bci-treelist-item",{"item":[16],"itemTitle":[1,"item-title"],"hasChildren":[4,"has-children"],"itemIconName":[1,"item-icon-name"],"show":[32]}]]],["bci-overlay",[[1,"bci-overlay",{"config":[16],"host":[16],"dialog":[16],"show":[64],"update":[64],"hide":[64]},null,{"config":["onConfigChange"]}]]],["bci-loading-indicator",[[1,"bci-loading-indicator",{"isLoading":[4,"is-loading"]}]]],["bci-column-list_5",[[1,"bci-facility-selector",{"checkedItems":[16],"darkBackground":[4,"dark-background"],"enableMultiSelection":[4,"enable-multi-selection"],"displayRecentSearches":[4,"display-recent-searches"],"facilitySelectorController":[16],"recentSearchController":[16],"activeLevel":[32],"columns":[32],"columnsExpanded":[32],"recentSearchesExpanded":[32],"searchString":[32],"searchResults":[32]},null,{"checkedItems":["onCheckedItemsSet"]}],[0,"bci-column-list",{"list":[16],"selectedId":[1025,"selected-id"],"facilitySelectorController":[16],"markCheckedItems":[64]}],[0,"bci-facility-search-result",{"searchString":[1,"search-string"],"searchResults":[16],"facilitySelectorController":[16],"markCheckedItems":[64]}],[0,"bci-facility-recent-search",{"expanded":[1028],"recentSearchController":[16],"recentSearches":[32]}],[0,"bci-facility-search-input",{"searchString":[1025,"search-string"],"facilitySelectorController":[16],"isColumnExpanded":[4,"is-column-expanded"],"darkBackground":[4,"dark-background"],"focussed":[32],"checkedFacilities":[32]}]]],["bci-icon",[[1,"bci-icon",{"name":[1],"clickable":[4]}]]],["bci-checkbox-collection_2",[[1,"bci-checkbox-collection",{"type":[2],"list":[16],"reset":[64],"setDefaultSelection":[64]}],[4,"checkbox-input",{"checkboxId":[1,"checkbox-id"],"checked":[4],"indeterminate":[4],"indication":[1],"preventDefaultOnClickAction":[4,"prevent-default-on-click-action"]},null,{"indication":["onStateChange"]}]]],["bci-multi-selector_8",[[1,"bci-multi-selector",{"darkBackground":[4,"dark-background"],"dataSource":[16],"selectionMode":[1,"selection-mode"],"initialSelection":[16],"singleColumnMode":[4,"single-column-mode"],"preventFullscreen":[4,"prevent-fullscreen"],"required":[4],"focused":[32],"selectedDataSource":[32],"searchValue":[32],"activeLevels":[32],"isLoading":[32],"enforceOverlayViewport":[32],"currentOverlayConfig":[32]},[[16,"click","hideDialog"]],{"dataSource":["onDataSourceUpdate"],"selectionMode":["onSelectionModeChange"],"searchValue":["onSearchChange"],"initialSelection":["onInitialSelectionChange"]}],[1,"bci-multi-selector-overlay",{"headerLabel":[1,"header-label"],"filterText":[1,"filter-text"],"showSave":[4,"show-save"],"enforceViewport":[1,"enforce-viewport"],"activeLevels":[16],"resetSelectionButtonEnabled":[4,"reset-selection-button-enabled"],"isLoading":[4,"is-loading"]},[[0,"nodeSelectionChanged","handleNodeSelectionChange"],[0,"nodeActiveChanged","handleNodeActiveChange"]],{"enforceViewport":["onEnforceViewportChange"]}],[1,"bci-multi-selector-input",{"searchValue":[1,"search-value"],"searchPlaceholder":[1,"search-placeholder"],"dataSources":[16],"selectedDataSource":[16],"required":[4],"selections":[16],"readonly":[4],"setFocusOnInputField":[64]},null,{"searchValue":["onSearchValueChange"]}],[0,"bci-multi-selector-tree",{"filterText":[1,"filter-text"],"activeLevels":[16],"isLoading":[4,"is-loading"]},null,{"activeLevels":["handleActiveLevelsChange"]}],[0,"bci-multi-selector-search-filter",{"searchTypes":[16],"defaultSearchType":[16],"selectedSearchType":[32],"open":[32],"closing":[32]},null,{"searchTypes":["handleNewSearchTypes"],"defaultSearchType":["handleNewDefaultSearchType"]}],[0,"bci-multi-selector-search-label",{"selections":[16],"breadcrumbTrails":[32]},null,{"selections":["computeBreadcrumbs"]}],[0,"bci-multi-selector-level",{"level":[16],"backButtonLabel":[1,"back-button-label"],"highlight":[1]},null,{"level":["onLevelChange"]}],[0,"bci-multi-selector-level-section",{"section":[16],"highlight":[1]}]]],["bci-datepicker_9",[[1,"bci-datetime-picker",{"label":[1],"placeholder":[1],"dateTimePickerId":[1,"date-time-picker-id"],"formatDate":[1,"format-date"],"maxDate":[2,"max-date"],"minDate":[2,"min-date"],"hide":[1],"templatesList":[16],"templatesDisabled":[4,"templates-disabled"],"sundayFirst":[4,"sunday-first"],"rangeDateTime":[4,"range-date-time"],"disabled":[4],"darkBackground":[4,"dark-background"],"timeFormat":[1025,"time-format"],"timeZone":[1025,"time-zone"],"dateTime":[1040],"timeStamp":[1040],"calendarWeekViewMode":[1,"calendar-week-view-mode"],"required":[4],"dateTimePickerInputObj":[32],"formatTime":[32],"inputPlaceholder":[32],"datePickerConfig":[32],"timePickerConfig":[32],"hideSeconds":[32],"overlayVisible":[32],"toggle":[64],"resetSelection":[64]},[[4,"OverlayOpenedEvent","updatePlaceholderOnOverlayOpenedEvent"],[5,"mousedown","handleMouseDown"],[5,"touchstart","handleTouchStart"],[4,"keydown","handleEscape"],[0,"keydown","handleTab"],[0,"keydown","handleEnter"],[0,"show","showPickerContainer"]],{"dateTime":["onDateTimeChanged"],"timeStamp":["onTimeStampChanged"],"placeholder":["onPlaceholderChanged"],"calendarWeekViewMode":["onCalendarWeekViewModeChanged"]}],[1,"bci-datetime-dialog",{"dateTimePickerId":[1,"date-time-picker-id"],"datePickerInputObj":[16],"templatesList":[16],"templatesDisabled":[4,"templates-disabled"],"hide":[1],"datePickerConfig":[16],"timePickerConfig":[16],"formatService":[16],"dateService":[16],"toggleDialogHandler":[16],"showTemplates":[32],"yearMonthSelectionStep":[32],"yearSelectionPageOffset":[32],"dateObj":[32],"datesObj":[32],"close":[64],"closeYearMonthSelection":[64]},[[0,"templateUpdate","onTemplateUpdate"],[0,"updatedTime","timeSelectedHandler"],[0,"showTimeRanges","showTimeRange"],[0,"toggleShowYearMonthSelection","toggleShowYearMonthSelectionHandler"],[0,"arrowClicked","arrowClickedHandler"],[0,"yearMonthSelected","yearMonthSelectedHandler"],[0,"singleDateSelected","singleDateSelectedHandler"],[0,"rangeDateSelected","rangeDateSelectedHandler"]],{"datePickerInputObj":["userInputDate"]}],[0,"bci-datepicker",{"minDate":[2,"min-date"],"maxDate":[2,"max-date"],"rangeDate":[4,"range-date"],"sundayFirst":[4,"sunday-first"],"bciDatepickerId":[8,"bci-datepicker-id"],"formatDate":[1,"format-date"],"datePickerInputObj":[8,"date-picker-input-obj"],"timeZone":[1,"time-zone"],"today":[1],"selectedDate":[1,"selected-date"],"selectedMonth":[2,"selected-month"],"selectedYear":[2,"selected-year"],"datesObj":[16]}],[0,"bci-month-header",{"year":[2],"month":[2],"yearMonthSelectionStep":[8,"year-month-selection-step"],"displayedMonth":[32],"displayedYear":[32]},null,{"month":["handleMonthChange"],"year":["handleYearChange"]}],[0,"bci-template",{"templatesList":[16],"visible":[4],"selectedTemplateId":[32],"reset":[64]}],[0,"bci-timepicker",{"timeInput":[1,"time-input"],"timeFormat":[1,"time-format"],"bciTimepickerId":[1,"bci-timepicker-id"],"rangeTimePicker":[4,"range-time-picker"],"timeTitle":[1,"time-title"],"hideSeconds":[32],"showMeridiem":[32],"hour":[32],"minutes":[32],"seconds":[32],"meridiem":[32]},null,{"timeInput":["handleTimeInput"]}],[0,"bci-year-month",{"selectionStep":[1,"selection-step"],"yearSelectionPageOffset":[2,"year-selection-page-offset"],"dateObj":[16],"minDate":[2,"min-date"],"maxDate":[2,"max-date"],"displayedYears":[32]},null,{"selectionStep":["handleSelectionStepChange"],"yearSelectionPageOffset":["handleSelectionPageChange"]}],[0,"bci-week-header",{"sundayFirst":[4,"sunday-first"]}],[0,"bci-weekdays",{"datesObj":[16],"daysInMonth":[2,"days-in-month"],"lastDayOfMonth":[16],"month":[2],"rangeDate":[4,"range-date"],"dateRestrictionObj":[8,"date-restriction-obj"],"offset":[2],"selectedDate":[1,"selected-date"],"year":[2],"todaysDate":[1,"todays-date"],"formatDate":[1,"format-date"],"timeZone":[1,"time-zone"]}]]]]'), options);
};

// node_modules/@bci-web-core/web-components/loader/index.js
(function() {
  if ("undefined" !== typeof window && void 0 !== window.Reflect && void 0 !== window.customElements) {
    var a = HTMLElement;
    window.HTMLElement = function() {
      return Reflect.construct(a, [], this.constructor);
    };
    HTMLElement.prototype = a.prototype;
    HTMLElement.prototype.constructor = HTMLElement;
    Object.setPrototypeOf(HTMLElement, a);
  }
})();
export {
  applyPolyfills,
  defineCustomElements,
  setNonce
};
//# sourceMappingURL=@bci-web-core_web-components_loader.js.map
