{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/utils-636fa948.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { g as getRenderingRef, f as forceUpdate } from './index-93dc8059.js';\nfunction toInteger(dirtyNumber) {\n  if (dirtyNumber === null || dirtyNumber === true || dirtyNumber === false) {\n    return NaN;\n  }\n  var number = Number(dirtyNumber);\n  if (isNaN(number)) {\n    return number;\n  }\n  return number < 0 ? Math.ceil(number) : Math.floor(number);\n}\nfunction requiredArgs(required, args) {\n  if (args.length < required) {\n    throw new TypeError(required + ' argument' + (required > 1 ? 's' : '') + ' required, but only ' + args.length + ' present');\n  }\n}\n\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @param {Date|Number} argument - the value to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\n\nfunction toDate(argument) {\n  requiredArgs(1, arguments);\n  var argStr = Object.prototype.toString.call(argument); // Clone the date\n\n  if (argument instanceof Date || typeof argument === 'object' && argStr === '[object Date]') {\n    // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n    return new Date(argument.getTime());\n  } else if (typeof argument === 'number' || argStr === '[object Number]') {\n    return new Date(argument);\n  } else {\n    if ((typeof argument === 'string' || argStr === '[object String]') && typeof console !== 'undefined') {\n      // eslint-disable-next-line no-console\n      console.warn(\"Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule\"); // eslint-disable-next-line no-console\n\n      console.warn(new Error().stack);\n    }\n    return new Date(NaN);\n  }\n}\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} - the new date with the days added\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\n\nfunction addDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) {\n    return new Date(NaN);\n  }\n  if (!amount) {\n    // If 0 days, no-op to avoid changing times in the hour before end of DST\n    return date;\n  }\n  date.setDate(date.getDate() + amount);\n  return date;\n}\n\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of milliseconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the milliseconds added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\n\nfunction addMilliseconds(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var timestamp = toDate(dirtyDate).getTime();\n  var amount = toInteger(dirtyAmount);\n  return new Date(timestamp + amount);\n}\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nfunction getTimezoneOffsetInMilliseconds(date) {\n  var utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\n  utcDate.setUTCFullYear(date.getFullYear());\n  return date.getTime() - utcDate.getTime();\n}\n\n/**\n * @name startOfDay\n * @category Day Helpers\n * @summary Return the start of a day for the given date.\n *\n * @description\n * Return the start of a day for the given date.\n * The result will be in the local timezone.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a day\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a day for 2 September 2014 11:55:00:\n * const result = startOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 00:00:00\n */\n\nfunction startOfDay(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\nvar MILLISECONDS_IN_DAY$1 = 86400000;\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\n\nfunction differenceInCalendarDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfDayLeft = startOfDay(dirtyDateLeft);\n  var startOfDayRight = startOfDay(dirtyDateRight);\n  var timestampLeft = startOfDayLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfDayLeft);\n  var timestampRight = startOfDayRight.getTime() - getTimezoneOffsetInMilliseconds(startOfDayRight); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a day is not constant\n  // (e.g. it's different in the day of the daylight saving time clock shift)\n\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_DAY$1);\n}\n\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same day (and year and month)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * var result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n * \n * @example\n * // Are 4 September and 4 October in the same day?\n * var result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n * \n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * var result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\n\nfunction isSameDay(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfDay = startOfDay(dirtyDateLeft);\n  var dateRightStartOfDay = startOfDay(dirtyDateRight);\n  return dateLeftStartOfDay.getTime() === dateRightStartOfDay.getTime();\n}\n\n/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {*} value - the value to check\n * @returns {boolean} true if the given value is a date\n * @throws {TypeError} 1 arguments required\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\n\nfunction isDate(value) {\n  requiredArgs(1, arguments);\n  return value instanceof Date || typeof value === 'object' && Object.prototype.toString.call(value) === '[object Date]';\n}\n\n/**\n * @name isValid\n * @category Common Helpers\n * @summary Is the given date valid?\n *\n * @description\n * Returns false if argument is Invalid Date and true otherwise.\n * Argument is converted to Date using `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n * Invalid Date is a Date, whose time value is NaN.\n *\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - Now `isValid` doesn't throw an exception\n *   if the first argument is not an instance of Date.\n *   Instead, argument is converted beforehand using `toDate`.\n *\n *   Examples:\n *\n *   | `isValid` argument        | Before v2.0.0 | v2.0.0 onward |\n *   |---------------------------|---------------|---------------|\n *   | `new Date()`              | `true`        | `true`        |\n *   | `new Date('2016-01-01')`  | `true`        | `true`        |\n *   | `new Date('')`            | `false`       | `false`       |\n *   | `new Date(1488370835081)` | `true`        | `true`        |\n *   | `new Date(NaN)`           | `false`       | `false`       |\n *   | `'2016-01-01'`            | `TypeError`   | `false`       |\n *   | `''`                      | `TypeError`   | `false`       |\n *   | `1488370835081`           | `TypeError`   | `true`        |\n *   | `NaN`                     | `TypeError`   | `false`       |\n *\n *   We introduce this change to make *date-fns* consistent with ECMAScript behavior\n *   that try to coerce arguments to the expected type\n *   (which is also the case with other *date-fns* functions).\n *\n * @param {*} date - the date to check\n * @returns {Boolean} the date is valid\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // For the valid date:\n * const result = isValid(new Date(2014, 1, 31))\n * //=> true\n *\n * @example\n * // For the value, convertable into a date:\n * const result = isValid(1393804800000)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isValid(new Date(''))\n * //=> false\n */\n\nfunction isValid(dirtyDate) {\n  requiredArgs(1, arguments);\n  if (!isDate(dirtyDate) && typeof dirtyDate !== 'number') {\n    return false;\n  }\n  var date = toDate(dirtyDate);\n  return !isNaN(Number(date));\n}\n\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\n\nfunction compareLocalAsc(dateLeft, dateRight) {\n  var diff = dateLeft.getFullYear() - dateRight.getFullYear() || dateLeft.getMonth() - dateRight.getMonth() || dateLeft.getDate() - dateRight.getDate() || dateLeft.getHours() - dateRight.getHours() || dateLeft.getMinutes() - dateRight.getMinutes() || dateLeft.getSeconds() - dateRight.getSeconds() || dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1; // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full days according to the local timezone\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n//=> 92\n */\n\nfunction differenceInDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareLocalAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarDays(dateLeft, dateRight));\n  dateLeft.setDate(dateLeft.getDate() - sign * difference); // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n\n  var isLastDayNotFull = Number(compareLocalAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastDayNotFull); // Prevent negative zero\n\n  return result === 0 ? 0 : result;\n}\nvar formatDistanceLocale$9 = {\n  lessThanXSeconds: {\n    one: 'less than a second',\n    other: 'less than {{count}} seconds'\n  },\n  xSeconds: {\n    one: '1 second',\n    other: '{{count}} seconds'\n  },\n  halfAMinute: 'half a minute',\n  lessThanXMinutes: {\n    one: 'less than a minute',\n    other: 'less than {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'about 1 hour',\n    other: 'about {{count}} hours'\n  },\n  xHours: {\n    one: '1 hour',\n    other: '{{count}} hours'\n  },\n  xDays: {\n    one: '1 day',\n    other: '{{count}} days'\n  },\n  aboutXWeeks: {\n    one: 'about 1 week',\n    other: 'about {{count}} weeks'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weeks'\n  },\n  aboutXMonths: {\n    one: 'about 1 month',\n    other: 'about {{count}} months'\n  },\n  xMonths: {\n    one: '1 month',\n    other: '{{count}} months'\n  },\n  aboutXYears: {\n    one: 'about 1 year',\n    other: 'about {{count}} years'\n  },\n  xYears: {\n    one: '1 year',\n    other: '{{count}} years'\n  },\n  overXYears: {\n    one: 'over 1 year',\n    other: 'over {{count}} years'\n  },\n  almostXYears: {\n    one: 'almost 1 year',\n    other: 'almost {{count}} years'\n  }\n};\nvar formatDistance$9 = function (token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale$9[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return result + ' ago';\n    }\n  }\n  return result;\n};\nfunction buildFormatLongFn(args) {\n  return function () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // TODO: Remove String()\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\nvar dateFormats$9 = {\n  full: 'EEEE, MMMM do, y',\n  long: 'MMMM do, y',\n  medium: 'MMM d, y',\n  short: 'MM/dd/yyyy'\n};\nvar timeFormats$9 = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats$9 = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$9 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$9,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$9,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$9,\n    defaultWidth: 'full'\n  })\n};\nvar formatRelativeLocale$9 = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: 'P'\n};\nvar formatRelative$9 = function (token, _date, _baseDate, _options) {\n  return formatRelativeLocale$9[token];\n};\nfunction buildLocalizeFn(args) {\n  return function (dirtyIndex, dirtyOptions) {\n    var options = dirtyOptions || {};\n    var context = options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex; // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n\n    return valuesArray[index];\n  };\n}\nvar eraValues$9 = {\n  narrow: ['B', 'A'],\n  abbreviated: ['BC', 'AD'],\n  wide: ['Before Christ', 'Anno Domini']\n};\nvar quarterValues$9 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1st quarter', '2nd quarter', '3rd quarter', '4th quarter']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues$9 = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n  wide: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']\n};\nvar dayValues$9 = {\n  narrow: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],\n  short: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],\n  abbreviated: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n  wide: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']\n};\nvar dayPeriodValues$9 = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mi',\n    noon: 'n',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'morning',\n    afternoon: 'afternoon',\n    evening: 'evening',\n    night: 'night'\n  }\n};\nvar formattingDayPeriodValues$7 = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mi',\n    noon: 'n',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnight',\n    noon: 'noon',\n    morning: 'in the morning',\n    afternoon: 'in the afternoon',\n    evening: 'in the evening',\n    night: 'at night'\n  }\n};\nvar ordinalNumber$9 = function (dirtyNumber, _options) {\n  var number = Number(dirtyNumber); // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  var rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + 'st';\n      case 2:\n        return number + 'nd';\n      case 3:\n        return number + 'rd';\n    }\n  }\n  return number + 'th';\n};\nvar localize$9 = {\n  ordinalNumber: ordinalNumber$9,\n  era: buildLocalizeFn({\n    values: eraValues$9,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$9,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$9,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$9,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$9,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$7,\n    defaultFormattingWidth: 'wide'\n  })\n};\nfunction buildMatchFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    }) : findKey(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    });\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (object.hasOwnProperty(key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction buildMatchPatternFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\nvar matchOrdinalNumberPattern$9 = /^(\\d+)(th|st|nd|rd)?/i;\nvar parseOrdinalNumberPattern$9 = /\\d+/i;\nvar matchEraPatterns$9 = {\n  narrow: /^(b|a)/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  wide: /^(before christ|before common era|anno domini|common era)/i\n};\nvar parseEraPatterns$9 = {\n  any: [/^b/i, /^(a|c)/i]\n};\nvar matchQuarterPatterns$9 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](th|st|nd|rd)? quarter/i\n};\nvar parseQuarterPatterns$9 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$9 = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,\n  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i\n};\nvar parseMonthPatterns$9 = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ap/i, /^may/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns$9 = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|mo|tu|we|th|fr|sa)/i,\n  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,\n  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i\n};\nvar parseDayPatterns$9 = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],\n  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]\n};\nvar matchDayPeriodPatterns$9 = {\n  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,\n  any: /^([ap]\\.?\\s?m\\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i\n};\nvar parseDayPeriodPatterns$9 = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mi/i,\n    noon: /^no/i,\n    morning: /morning/i,\n    afternoon: /afternoon/i,\n    evening: /evening/i,\n    night: /night/i\n  }\n};\nvar match$9 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$9,\n    parsePattern: parseOrdinalNumberPattern$9,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$9,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$9,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$9,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$9,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$9,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$9,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$9,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$9,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$9,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$9,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (United States).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> Koss [@kossnocorp]{@link https://github.com/kossnocorp}\n * <AUTHOR> Koss [@leshakoss]{@link https://github.com/leshakoss}\n */\nvar locale$9 = {\n  code: 'en-US',\n  formatDistance: formatDistance$9,\n  formatLong: formatLong$9,\n  formatRelative: formatRelative$9,\n  localize: localize$9,\n  match: match$9,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */,\n\n    firstWeekContainsDate: 1\n  }\n};\n\n/**\n * @name subMilliseconds\n * @category Millisecond Helpers\n * @summary Subtract the specified number of milliseconds from the given date.\n *\n * @description\n * Subtract the specified number of milliseconds from the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of milliseconds to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the milliseconds subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 750 milliseconds from 10 July 2014 12:45:30.000:\n * const result = subMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:29.250\n */\n\nfunction subMilliseconds(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addMilliseconds(dirtyDate, -amount);\n}\nvar MILLISECONDS_IN_DAY = 86400000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction getUTCDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var timestamp = date.getTime();\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n  var startOfYearTimestamp = date.getTime();\n  var difference = timestamp - startOfYearTimestamp;\n  return Math.floor(difference / MILLISECONDS_IN_DAY) + 1;\n}\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction startOfUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction getUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);\n  var fourthOfJanuaryOfThisYear = new Date(0);\n  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}\nvar MILLISECONDS_IN_WEEK$1 = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction getUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK$1) + 1;\n}\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction startOfUTCWeek(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction getUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction startOfUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);\n  var year = getUTCWeekYear(dirtyDate, dirtyOptions);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, dirtyOptions);\n  return date;\n}\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction getUTCWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCWeek(date, options).getTime() - startOfUTCWeekYear(date, options).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}\nfunction addLeadingZeros(number, targetLength) {\n  var sign = number < 0 ? '-' : '';\n  var output = Math.abs(number).toString();\n  while (output.length < targetLength) {\n    output = '0' + output;\n  }\n  return sign + output;\n}\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nvar formatters$1 = {\n  // Year\n  y: function (date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n    var signedYear = date.getUTCFullYear(); // Returns 1 for 1 BC (which is year 0 in JavaScript)\n\n    var year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === 'yy' ? year % 100 : year, token.length);\n  },\n  // Month\n  M: function (date, token) {\n    var month = date.getUTCMonth();\n    return token === 'M' ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n  // Day of the month\n  d: function (date, token) {\n    return addLeadingZeros(date.getUTCDate(), token.length);\n  },\n  // AM or PM\n  a: function (date, token) {\n    var dayPeriodEnumValue = date.getUTCHours() / 12 >= 1 ? 'pm' : 'am';\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return dayPeriodEnumValue.toUpperCase();\n      case 'aaa':\n        return dayPeriodEnumValue;\n      case 'aaaaa':\n        return dayPeriodEnumValue[0];\n      case 'aaaa':\n      default:\n        return dayPeriodEnumValue === 'am' ? 'a.m.' : 'p.m.';\n    }\n  },\n  // Hour [1-12]\n  h: function (date, token) {\n    return addLeadingZeros(date.getUTCHours() % 12 || 12, token.length);\n  },\n  // Hour [0-23]\n  H: function (date, token) {\n    return addLeadingZeros(date.getUTCHours(), token.length);\n  },\n  // Minute\n  m: function (date, token) {\n    return addLeadingZeros(date.getUTCMinutes(), token.length);\n  },\n  // Second\n  s: function (date, token) {\n    return addLeadingZeros(date.getUTCSeconds(), token.length);\n  },\n  // Fraction of second\n  S: function (date, token) {\n    var numberOfDigits = token.length;\n    var milliseconds = date.getUTCMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, numberOfDigits - 3));\n    return addLeadingZeros(fractionalSeconds, token.length);\n  }\n};\nvar dayPeriodEnum = {\n  am: 'am',\n  pm: 'pm',\n  midnight: 'midnight',\n  noon: 'noon',\n  morning: 'morning',\n  afternoon: 'afternoon',\n  evening: 'evening',\n  night: 'night'\n};\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nvar formatters = {\n  // Era\n  G: function (date, token, localize) {\n    var era = date.getUTCFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case 'G':\n      case 'GG':\n      case 'GGG':\n        return localize.era(era, {\n          width: 'abbreviated'\n        });\n      // A, B\n\n      case 'GGGGG':\n        return localize.era(era, {\n          width: 'narrow'\n        });\n      // Anno Domini, Before Christ\n\n      case 'GGGG':\n      default:\n        return localize.era(era, {\n          width: 'wide'\n        });\n    }\n  },\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === 'yo') {\n      var signedYear = date.getUTCFullYear(); // Returns 1 for 1 BC (which is year 0 in JavaScript)\n\n      var year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, {\n        unit: 'year'\n      });\n    }\n    return formatters$1.y(date, token);\n  },\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    var signedWeekYear = getUTCWeekYear(date, options); // Returns 1 for 1 BC (which is year 0 in JavaScript)\n\n    var weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear; // Two digit year\n\n    if (token === 'YY') {\n      var twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    } // Ordinal number\n\n    if (token === 'Yo') {\n      return localize.ordinalNumber(weekYear, {\n        unit: 'year'\n      });\n    } // Padding\n\n    return addLeadingZeros(weekYear, token.length);\n  },\n  // ISO week-numbering year\n  R: function (date, token) {\n    var isoWeekYear = getUTCISOWeekYear(date); // Padding\n\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    var year = date.getUTCFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n  // Quarter\n  Q: function (date, token, localize) {\n    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case 'Q':\n        return String(quarter);\n      // 01, 02, 03, 04\n\n      case 'QQ':\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n\n      case 'Qo':\n        return localize.ordinalNumber(quarter, {\n          unit: 'quarter'\n        });\n      // Q1, Q2, Q3, Q4\n\n      case 'QQQ':\n        return localize.quarter(quarter, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n\n      case 'QQQQQ':\n        return localize.quarter(quarter, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // 1st quarter, 2nd quarter, ...\n\n      case 'QQQQ':\n      default:\n        return localize.quarter(quarter, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case 'q':\n        return String(quarter);\n      // 01, 02, 03, 04\n\n      case 'qq':\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n\n      case 'qo':\n        return localize.ordinalNumber(quarter, {\n          unit: 'quarter'\n        });\n      // Q1, Q2, Q3, Q4\n\n      case 'qqq':\n        return localize.quarter(quarter, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n\n      case 'qqqqq':\n        return localize.quarter(quarter, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // 1st quarter, 2nd quarter, ...\n\n      case 'qqqq':\n      default:\n        return localize.quarter(quarter, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // Month\n  M: function (date, token, localize) {\n    var month = date.getUTCMonth();\n    switch (token) {\n      case 'M':\n      case 'MM':\n        return formatters$1.M(date, token);\n      // 1st, 2nd, ..., 12th\n\n      case 'Mo':\n        return localize.ordinalNumber(month + 1, {\n          unit: 'month'\n        });\n      // Jan, Feb, ..., Dec\n\n      case 'MMM':\n        return localize.month(month, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // J, F, ..., D\n\n      case 'MMMMM':\n        return localize.month(month, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // January, February, ..., December\n\n      case 'MMMM':\n      default:\n        return localize.month(month, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone month\n  L: function (date, token, localize) {\n    var month = date.getUTCMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case 'L':\n        return String(month + 1);\n      // 01, 02, ..., 12\n\n      case 'LL':\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n\n      case 'Lo':\n        return localize.ordinalNumber(month + 1, {\n          unit: 'month'\n        });\n      // Jan, Feb, ..., Dec\n\n      case 'LLL':\n        return localize.month(month, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // J, F, ..., D\n\n      case 'LLLLL':\n        return localize.month(month, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // January, February, ..., December\n\n      case 'LLLL':\n      default:\n        return localize.month(month, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // Local week of year\n  w: function (date, token, localize, options) {\n    var week = getUTCWeek(date, options);\n    if (token === 'wo') {\n      return localize.ordinalNumber(week, {\n        unit: 'week'\n      });\n    }\n    return addLeadingZeros(week, token.length);\n  },\n  // ISO week of year\n  I: function (date, token, localize) {\n    var isoWeek = getUTCISOWeek(date);\n    if (token === 'Io') {\n      return localize.ordinalNumber(isoWeek, {\n        unit: 'week'\n      });\n    }\n    return addLeadingZeros(isoWeek, token.length);\n  },\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === 'do') {\n      return localize.ordinalNumber(date.getUTCDate(), {\n        unit: 'date'\n      });\n    }\n    return formatters$1.d(date, token);\n  },\n  // Day of year\n  D: function (date, token, localize) {\n    var dayOfYear = getUTCDayOfYear(date);\n    if (token === 'Do') {\n      return localize.ordinalNumber(dayOfYear, {\n        unit: 'dayOfYear'\n      });\n    }\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n  // Day of week\n  E: function (date, token, localize) {\n    var dayOfWeek = date.getUTCDay();\n    switch (token) {\n      // Tue\n      case 'E':\n      case 'EE':\n      case 'EEE':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n\n      case 'EEEEE':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n\n      case 'EEEEEE':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n\n      case 'EEEE':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Local day of week\n  e: function (date, token, localize, options) {\n    var dayOfWeek = date.getUTCDay();\n    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case 'e':\n        return String(localDayOfWeek);\n      // Padded numerical value\n\n      case 'ee':\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n\n      case 'eo':\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: 'day'\n        });\n      case 'eee':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n\n      case 'eeeee':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n\n      case 'eeeeee':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n\n      case 'eeee':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    var dayOfWeek = date.getUTCDay();\n    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case 'c':\n        return String(localDayOfWeek);\n      // Padded numerical value\n\n      case 'cc':\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n\n      case 'co':\n        return localize.ordinalNumber(localDayOfWeek, {\n          unit: 'day'\n        });\n      case 'ccc':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'standalone'\n        });\n      // T\n\n      case 'ccccc':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'standalone'\n        });\n      // Tu\n\n      case 'cccccc':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'standalone'\n        });\n      // Tuesday\n\n      case 'cccc':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'standalone'\n        });\n    }\n  },\n  // ISO day of week\n  i: function (date, token, localize) {\n    var dayOfWeek = date.getUTCDay();\n    var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case 'i':\n        return String(isoDayOfWeek);\n      // 02\n\n      case 'ii':\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n\n      case 'io':\n        return localize.ordinalNumber(isoDayOfWeek, {\n          unit: 'day'\n        });\n      // Tue\n\n      case 'iii':\n        return localize.day(dayOfWeek, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      // T\n\n      case 'iiiii':\n        return localize.day(dayOfWeek, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      // Tu\n\n      case 'iiiiii':\n        return localize.day(dayOfWeek, {\n          width: 'short',\n          context: 'formatting'\n        });\n      // Tuesday\n\n      case 'iiii':\n      default:\n        return localize.day(dayOfWeek, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // AM or PM\n  a: function (date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'aaa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        }).toLowerCase();\n      case 'aaaaa':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'aaaa':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';\n    }\n    switch (token) {\n      case 'b':\n      case 'bb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'bbb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        }).toLowerCase();\n      case 'bbbbb':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'bbbb':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    var hours = date.getUTCHours();\n    var dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n    switch (token) {\n      case 'B':\n      case 'BB':\n      case 'BBB':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'abbreviated',\n          context: 'formatting'\n        });\n      case 'BBBBB':\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'narrow',\n          context: 'formatting'\n        });\n      case 'BBBB':\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: 'wide',\n          context: 'formatting'\n        });\n    }\n  },\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === 'ho') {\n      var hours = date.getUTCHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return formatters$1.h(date, token);\n  },\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === 'Ho') {\n      return localize.ordinalNumber(date.getUTCHours(), {\n        unit: 'hour'\n      });\n    }\n    return formatters$1.H(date, token);\n  },\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    var hours = date.getUTCHours() % 12;\n    if (token === 'Ko') {\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    var hours = date.getUTCHours();\n    if (hours === 0) hours = 24;\n    if (token === 'ko') {\n      return localize.ordinalNumber(hours, {\n        unit: 'hour'\n      });\n    }\n    return addLeadingZeros(hours, token.length);\n  },\n  // Minute\n  m: function (date, token, localize) {\n    if (token === 'mo') {\n      return localize.ordinalNumber(date.getUTCMinutes(), {\n        unit: 'minute'\n      });\n    }\n    return formatters$1.m(date, token);\n  },\n  // Second\n  s: function (date, token, localize) {\n    if (token === 'so') {\n      return localize.ordinalNumber(date.getUTCSeconds(), {\n        unit: 'second'\n      });\n    }\n    return formatters$1.s(date, token);\n  },\n  // Fraction of second\n  S: function (date, token) {\n    return formatters$1.S(date, token);\n  },\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    if (timezoneOffset === 0) {\n      return 'Z';\n    }\n    switch (token) {\n      // Hours and optional minutes\n      case 'X':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n\n      case 'XXXX':\n      case 'XX':\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n\n      case 'XXXXX':\n      case 'XXX': // Hours and minutes with `:` delimiter\n\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Hours and optional minutes\n      case 'x':\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n\n      case 'xxxx':\n      case 'xx':\n        // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n\n      case 'xxxxx':\n      case 'xxx': // Hours and minutes with `:` delimiter\n\n      default:\n        return formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (GMT)\n  O: function (date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case 'O':\n      case 'OO':\n      case 'OOO':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n\n      case 'OOOO':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Timezone (specific non-location)\n  z: function (date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timezoneOffset = originalDate.getTimezoneOffset();\n    switch (token) {\n      // Short\n      case 'z':\n      case 'zz':\n      case 'zzz':\n        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');\n      // Long\n\n      case 'zzzz':\n      default:\n        return 'GMT' + formatTimezone(timezoneOffset, ':');\n    }\n  },\n  // Seconds timestamp\n  t: function (date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timestamp = Math.floor(originalDate.getTime() / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n  // Milliseconds timestamp\n  T: function (date, token, _localize, options) {\n    var originalDate = options._originalDate || date;\n    var timestamp = originalDate.getTime();\n    return addLeadingZeros(timestamp, token.length);\n  }\n};\nfunction formatTimezoneShort(offset, dirtyDelimiter) {\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = Math.floor(absOffset / 60);\n  var minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  var delimiter = dirtyDelimiter || '';\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\nfunction formatTimezoneWithOptionalMinutes(offset, dirtyDelimiter) {\n  if (offset % 60 === 0) {\n    var sign = offset > 0 ? '-' : '+';\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, dirtyDelimiter);\n}\nfunction formatTimezone(offset, dirtyDelimiter) {\n  var delimiter = dirtyDelimiter || '';\n  var sign = offset > 0 ? '-' : '+';\n  var absOffset = Math.abs(offset);\n  var hours = addLeadingZeros(Math.floor(absOffset / 60), 2);\n  var minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\nfunction dateLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'P':\n      return formatLong.date({\n        width: 'short'\n      });\n    case 'PP':\n      return formatLong.date({\n        width: 'medium'\n      });\n    case 'PPP':\n      return formatLong.date({\n        width: 'long'\n      });\n    case 'PPPP':\n    default:\n      return formatLong.date({\n        width: 'full'\n      });\n  }\n}\nfunction timeLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'p':\n      return formatLong.time({\n        width: 'short'\n      });\n    case 'pp':\n      return formatLong.time({\n        width: 'medium'\n      });\n    case 'ppp':\n      return formatLong.time({\n        width: 'long'\n      });\n    case 'pppp':\n    default:\n      return formatLong.time({\n        width: 'full'\n      });\n  }\n}\nfunction dateTimeLongFormatter(pattern, formatLong) {\n  var matchResult = pattern.match(/(P+)(p+)?/) || [];\n  var datePattern = matchResult[1];\n  var timePattern = matchResult[2];\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n  var dateTimeFormat;\n  switch (datePattern) {\n    case 'P':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'short'\n      });\n      break;\n    case 'PP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'medium'\n      });\n      break;\n    case 'PPP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'long'\n      });\n      break;\n    case 'PPPP':\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: 'full'\n      });\n      break;\n  }\n  return dateTimeFormat.replace('{{date}}', dateLongFormatter(datePattern, formatLong)).replace('{{time}}', timeLongFormatter(timePattern, formatLong));\n}\nvar longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};\nvar protectedDayOfYearTokens = ['D', 'DD'];\nvar protectedWeekYearTokens = ['YY', 'YYYY'];\nfunction isProtectedDayOfYearToken(token) {\n  return protectedDayOfYearTokens.indexOf(token) !== -1;\n}\nfunction isProtectedWeekYearToken(token) {\n  return protectedWeekYearTokens.indexOf(token) !== -1;\n}\nfunction throwProtectedError(token, format, input) {\n  if (token === 'YYYY') {\n    throw new RangeError(\"Use `yyyy` instead of `YYYY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://git.io/fxCyr\"));\n  } else if (token === 'YY') {\n    throw new RangeError(\"Use `yy` instead of `YY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://git.io/fxCyr\"));\n  } else if (token === 'D') {\n    throw new RangeError(\"Use `d` instead of `D` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://git.io/fxCyr\"));\n  } else if (token === 'DD') {\n    throw new RangeError(\"Use `dd` instead of `DD` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://git.io/fxCyr\"));\n  }\n}\n\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\n\nvar formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g; // This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\n\nvar longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\nvar escapedStringRegExp = /^'([^]*?)'?$/;\nvar doubleQuoteRegExp = /''/g;\nvar unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n/**\n * @name format\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. The result may vary by locale.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://git.io/fxCyr\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n * (see the last example)\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 7 below the table).\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   | Notes |\n * |---------------------------------|---------|-----------------------------------|-------|\n * | Era                             | G..GGG  | AD, BC                            |       |\n * |                                 | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 | GGGGG   | A, B                              |       |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | yo      | 44th, 1st, 0th, 17th              | 5,7   |\n * |                                 | yy      | 44, 01, 00, 17                    | 5     |\n * |                                 | yyy     | 044, 001, 1900, 2017              | 5     |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            | 5     |\n * |                                 | yyyyy   | ...                               | 3,5   |\n * | Local week-numbering year       | Y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | Yo      | 44th, 1st, 1900th, 2017th         | 5,7   |\n * |                                 | YY      | 44, 01, 00, 17                    | 5,8   |\n * |                                 | YYY     | 044, 001, 1900, 2017              | 5     |\n * |                                 | YYYY    | 0044, 0001, 1900, 2017            | 5,8   |\n * |                                 | YYYYY   | ...                               | 3,5   |\n * | ISO week-numbering year         | R       | -43, 0, 1, 1900, 2017             | 5,7   |\n * |                                 | RR      | -43, 00, 01, 1900, 2017           | 5,7   |\n * |                                 | RRR     | -043, 000, 001, 1900, 2017        | 5,7   |\n * |                                 | RRRR    | -0043, 0000, 0001, 1900, 2017     | 5,7   |\n * |                                 | RRRRR   | ...                               | 3,5,7 |\n * | Extended year                   | u       | -43, 0, 1, 1900, 2017             | 5     |\n * |                                 | uu      | -43, 01, 1900, 2017               | 5     |\n * |                                 | uuu     | -043, 001, 1900, 2017             | 5     |\n * |                                 | uuuu    | -0043, 0001, 1900, 2017           | 5     |\n * |                                 | uuuuu   | ...                               | 3,5   |\n * | Quarter (formatting)            | Q       | 1, 2, 3, 4                        |       |\n * |                                 | Qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | QQ      | 01, 02, 03, 04                    |       |\n * |                                 | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | q       | 1, 2, 3, 4                        |       |\n * |                                 | qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | qq      | 01, 02, 03, 04                    |       |\n * |                                 | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | qqqqq   | 1, 2, 3, 4                        | 4     |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |       |\n * |                                 | Mo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | MM      | 01, 02, ..., 12                   |       |\n * |                                 | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 | MMMM    | January, February, ..., December  | 2     |\n * |                                 | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | L       | 1, 2, ..., 12                     |       |\n * |                                 | Lo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | LL      | 01, 02, ..., 12                   |       |\n * |                                 | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 | LLLL    | January, February, ..., December  | 2     |\n * |                                 | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | w       | 1, 2, ..., 53                     |       |\n * |                                 | wo      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | I       | 1, 2, ..., 53                     | 7     |\n * |                                 | Io      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | II      | 01, 02, ..., 53                   | 7     |\n * | Day of month                    | d       | 1, 2, ..., 31                     |       |\n * |                                 | do      | 1st, 2nd, ..., 31st               | 7     |\n * |                                 | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     | D       | 1, 2, ..., 365, 366               | 9     |\n * |                                 | Do      | 1st, 2nd, ..., 365th, 366th       | 7     |\n * |                                 | DD      | 01, 02, ..., 365, 366             | 9     |\n * |                                 | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 | DDDD    | ...                               | 3     |\n * | Day of week (formatting)        | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    | i       | 1, 2, 3, ..., 7                   | 7     |\n * |                                 | io      | 1st, 2nd, ..., 7th                | 7     |\n * |                                 | ii      | 01, 02, ..., 07                   | 7     |\n * |                                 | iii     | Mon, Tue, Wed, ..., Sun           | 7     |\n * |                                 | iiii    | Monday, Tuesday, ..., Sunday      | 2,7   |\n * |                                 | iiiii   | M, T, W, T, F, S, S               | 7     |\n * |                                 | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 7     |\n * | Local day of week (formatting)  | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 | eo      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | ee      | 02, 03, ..., 01                   |       |\n * |                                 | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 | co      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | cc      | 02, 03, ..., 01                   |       |\n * |                                 | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          | a..aa   | AM, PM                            |       |\n * |                                 | aaa     | am, pm                            |       |\n * |                                 | aaaa    | a.m., p.m.                        | 2     |\n * |                                 | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          | b..bb   | AM, PM, noon, midnight            |       |\n * |                                 | bbb     | am, pm, noon, midnight            |       |\n * |                                 | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             | B..BBB  | at night, in the morning, ...     |       |\n * |                                 | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 | ho      | 1st, 2nd, ..., 11th, 12th         | 7     |\n * |                                 | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 | Ho      | 0th, 1st, 2nd, ..., 23rd          | 7     |\n * |                                 | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 | Ko      | 1st, 2nd, ..., 11th, 0th          | 7     |\n * |                                 | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 | ko      | 24th, 1st, 2nd, ..., 23rd         | 7     |\n * |                                 | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          | m       | 0, 1, ..., 59                     |       |\n * |                                 | mo      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | mm      | 00, 01, ..., 59                   |       |\n * | Second                          | s       | 0, 1, ..., 59                     |       |\n * |                                 | so      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | ss      | 00, 01, ..., 59                   |       |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |       |\n * |                                 | SS      | 00, 01, ..., 99                   |       |\n * |                                 | SSS     | 000, 001, ..., 999                |       |\n * |                                 | SSSS    | ...                               | 3     |\n * | Timezone (ISO-8601 w/ Z)        | X       | -08, +0530, Z                     |       |\n * |                                 | XX      | -0800, +0530, Z                   |       |\n * |                                 | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       | x       | -08, +0530, +00                   |       |\n * |                                 | xx      | -0800, +0530, +0000               |       |\n * |                                 | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Timezone (GMT)                  | O...OOO | GMT-8, GMT+5:30, GMT+0            |       |\n * |                                 | OOOO    | GMT-08:00, GMT+05:30, GMT+00:00   | 2     |\n * | Timezone (specific non-locat.)  | z...zzz | GMT-8, GMT+5:30, GMT+0            | 6     |\n * |                                 | zzzz    | GMT-08:00, GMT+05:30, GMT+00:00   | 2,6   |\n * | Seconds timestamp               | t       | 512969520                         | 7     |\n * |                                 | tt      | ...                               | 3,7   |\n * | Milliseconds timestamp          | T       | 512969520900                      | 7     |\n * |                                 | TT      | ...                               | 3,7   |\n * | Long localized date             | P       | 04/29/1453                        | 7     |\n * |                                 | PP      | Apr 29, 1453                      | 7     |\n * |                                 | PPP     | April 29th, 1453                  | 7     |\n * |                                 | PPPP    | Friday, April 29th, 1453          | 2,7   |\n * | Long localized time             | p       | 12:00 AM                          | 7     |\n * |                                 | pp      | 12:00:00 AM                       | 7     |\n * |                                 | ppp     | 12:00:00 AM GMT+2                 | 7     |\n * |                                 | pppp    | 12:00:00 AM GMT+02:00             | 2,7   |\n * | Combination of date and time    | Pp      | 04/29/1453, 12:00 AM              | 7     |\n * |                                 | PPpp    | Apr 29, 1453, 12:00:00 AM         | 7     |\n * |                                 | PPPppp  | April 29th, 1453 at ...           | 7     |\n * |                                 | PPPPpppp| Friday, April 29th, 1453 at ...   | 2,7   |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table (e.g. `EEEEEEEEEEE`)\n *    the output will be the same as default pattern for this unit, usually\n *    the longest one (in case of ISO weekdays, `EEEE`). Default patterns for units\n *    are marked with \"2\" in the last column of the table.\n *\n *    `format(new Date(2017, 10, 6), 'MMM') //=> 'Nov'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMM') //=> 'N'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMMM') //=> 'November'`\n *\n * 3. Some patterns could be unlimited length (such as `yyyyyyyy`).\n *    The output will be padded with zeros to match the length of the pattern.\n *\n *    `format(new Date(2017, 10, 6), 'yyyyyyyy') //=> '00002017'`\n *\n * 4. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 5. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` always returns the last two digits of a year,\n *    while `uu` pads single digit years to 2 characters and returns other years unchanged:\n *\n *    | Year | `yy` | `uu` |\n *    |------|------|------|\n *    | 1    |   01 |   01 |\n *    | 14   |   14 |   14 |\n *    | 376  |   76 |  376 |\n *    | 1453 |   53 | 1453 |\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [getISOWeekYear]{@link https://date-fns.org/docs/getISOWeekYear}\n *    and [getWeekYear]{@link https://date-fns.org/docs/getWeekYear}).\n *\n * 6. Specific non-location timezones are currently unavailable in `date-fns`,\n *    so right now these tokens fall back to GMT timezones.\n *\n * 7. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `t`: seconds timestamp\n *    - `T`: milliseconds timestamp\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 8. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://git.io/fxCyr\n *\n * 9. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://git.io/fxCyr\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The second argument is now required for the sake of explicitness.\n *\n *   ```javascript\n *   // Before v2.0.0\n *   format(new Date(2016, 0, 1))\n *\n *   // v2.0.0 onward\n *   format(new Date(2016, 0, 1), \"yyyy-MM-dd'T'HH:mm:ss.SSSxxx\")\n *   ```\n *\n * - New format string API for `format` function\n *   which is based on [Unicode Technical Standard #35](https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table).\n *   See [this post](https://blog.date-fns.org/post/unicode-tokens-in-date-fns-v2-sreatyki91jg) for more details.\n *\n * - Characters are now escaped using single quote symbols (`'`) instead of square brackets.\n *\n * @param {Date|Number} date - the original date\n * @param {String} format - the string of tokens\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {Number} [options.firstWeekContainsDate=1] - the day of January, which is\n * @param {Boolean} [options.useAdditionalWeekYearTokens=false] - if true, allows usage of the week-numbering year tokens `YY` and `YYYY`;\n *   see: https://git.io/fxCyr\n * @param {Boolean} [options.useAdditionalDayOfYearTokens=false] - if true, allows usage of the day of year tokens `D` and `DD`;\n *   see: https://git.io/fxCyr\n * @returns {String} the formatted date string\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n * @throws {RangeError} use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://git.io/fxCyr\n * @throws {RangeError} use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://git.io/fxCyr\n * @throws {RangeError} use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://git.io/fxCyr\n * @throws {RangeError} use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://git.io/fxCyr\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * // Represent 11 February 2014 in middle-endian format:\n * var result = format(new Date(2014, 1, 11), 'MM/dd/yyyy')\n * //=> '02/11/2014'\n *\n * @example\n * // Represent 2 July 2014 in Esperanto:\n * import { eoLocale } from 'date-fns/locale/eo'\n * var result = format(new Date(2014, 6, 2), \"do 'de' MMMM yyyy\", {\n *   locale: eoLocale\n * })\n * //=> '2-a de julio 2014'\n *\n * @example\n * // Escape string by single quote characters:\n * var result = format(new Date(2014, 6, 2, 15), \"h 'o''clock'\")\n * //=> \"3 o'clock\"\n */\n\nfunction format(dirtyDate, dirtyFormatStr, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var formatStr = String(dirtyFormatStr);\n  var options = dirtyOptions || {};\n  var locale = options.locale || locale$9;\n  var localeFirstWeekContainsDate = locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var localeWeekStartsOn = locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  } // Convert the date in system timezone to the same date in UTC+00:00 timezone.\n  // This ensures that when UTC functions will be implemented, locales will be compatible with them.\n  // See an issue about UTC functions: https://github.com/date-fns/date-fns/issues/376\n\n  var timezoneOffset = getTimezoneOffsetInMilliseconds(originalDate);\n  var utcDate = subMilliseconds(originalDate, timezoneOffset);\n  var formatterOptions = {\n    firstWeekContainsDate: firstWeekContainsDate,\n    weekStartsOn: weekStartsOn,\n    locale: locale,\n    _originalDate: originalDate\n  };\n  var result = formatStr.match(longFormattingTokensRegExp).map(function (substring) {\n    var firstCharacter = substring[0];\n    if (firstCharacter === 'p' || firstCharacter === 'P') {\n      var longFormatter = longFormatters[firstCharacter];\n      return longFormatter(substring, locale.formatLong, formatterOptions);\n    }\n    return substring;\n  }).join('').match(formattingTokensRegExp).map(function (substring) {\n    // Replace two single quote characters with one single quote character\n    if (substring === \"''\") {\n      return \"'\";\n    }\n    var firstCharacter = substring[0];\n    if (firstCharacter === \"'\") {\n      return cleanEscapedString(substring);\n    }\n    var formatter = formatters[firstCharacter];\n    if (formatter) {\n      if (!options.useAdditionalWeekYearTokens && isProtectedWeekYearToken(substring)) {\n        throwProtectedError(substring, dirtyFormatStr, dirtyDate);\n      }\n      if (!options.useAdditionalDayOfYearTokens && isProtectedDayOfYearToken(substring)) {\n        throwProtectedError(substring, dirtyFormatStr, dirtyDate);\n      }\n      return formatter(utcDate, substring, locale.localize, formatterOptions);\n    }\n    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n      throw new RangeError('Format string contains an unescaped latin alphabet character `' + firstCharacter + '`');\n    }\n    return substring;\n  }).join('');\n  return result;\n}\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\n\nfunction getDaysInMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var monthIndex = date.getMonth();\n  var lastDayOfMonth = new Date(0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n/**\n * @name subDays\n * @category Day Helpers\n * @summary Subtract the specified number of days from the given date.\n *\n * @description\n * Subtract the specified number of days from the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of days to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the days subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 10 days from 1 September 2014:\n * const result = subDays(new Date(2014, 8, 1), 10)\n * //=> Fri Aug 22 2014 00:00:00\n */\n\nfunction subDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addDays(dirtyDate, -amount);\n}\n\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is today\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * var result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\n\nfunction isToday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, Date.now());\n}\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is yesterday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * var result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\n\nfunction isYesterday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, subDays(Date.now(), 1));\n}\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} month - the month of the new date\n * @returns {Date} the new date with the month set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\n\nfunction setMonth(dirtyDate, dirtyMonth) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var month = toInteger(dirtyMonth);\n  var year = date.getFullYear();\n  var day = date.getDate();\n  var dateWithDesiredMonth = new Date(0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  var daysInMonth = getDaysInMonth(dateWithDesiredMonth); // Set the last day of the new month\n  // if the original date was the last day of the longer month\n\n  date.setMonth(month, Math.min(day, daysInMonth));\n  return date;\n}\n\n/**\n * @name setDate\n * @category Day Helpers\n * @summary Set the day of the month to the given date.\n *\n * @description\n * Set the day of the month to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} dayOfMonth - the day of the month of the new date\n * @returns {Date} the new date with the day of the month set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 30th day of the month to 1 September 2014:\n * var result = setDate(new Date(2014, 8, 1), 30)\n * //=> Tue Sep 30 2014 00:00:00\n */\n\nfunction setDate(dirtyDate, dirtyDayOfMonth) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dayOfMonth = toInteger(dirtyDayOfMonth);\n  date.setDate(dayOfMonth);\n  return date;\n}\n\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} year - the year of the new date\n * @returns {Date} the new date with the year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\n\nfunction setYear(dirtyDate, dirtyYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var year = toInteger(dirtyYear); // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n  date.setFullYear(year);\n  return date;\n}\nconst appendToMap = (map, propName, value) => {\n  const items = map.get(propName);\n  if (!items) {\n    map.set(propName, [value]);\n  } else if (!items.includes(value)) {\n    items.push(value);\n  }\n};\nconst debounce = (fn, ms) => {\n  let timeoutId;\n  return (...args) => {\n    if (timeoutId) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      timeoutId = 0;\n      fn(...args);\n    }, ms);\n  };\n};\n\n/**\n * Check if a possible element isConnected.\n * The property might not be there, so we check for it.\n *\n * We want it to return true if isConnected is not a property,\n * otherwise we would remove these elements and would not update.\n *\n * Better leak in Edge than to be useless.\n */\nconst isConnected = maybeElement => !('isConnected' in maybeElement) || maybeElement.isConnected;\nconst cleanupElements = debounce(map => {\n  for (let key of map.keys()) {\n    map.set(key, map.get(key).filter(isConnected));\n  }\n}, 2000);\nconst stencilSubscription = () => {\n  if (typeof getRenderingRef !== 'function') {\n    // If we are not in a stencil project, we do nothing.\n    // This function is not really exported by @stencil/core.\n    return {};\n  }\n  const elmsToUpdate = new Map();\n  return {\n    dispose: () => elmsToUpdate.clear(),\n    get: propName => {\n      const elm = getRenderingRef();\n      if (elm) {\n        appendToMap(elmsToUpdate, propName, elm);\n      }\n    },\n    set: propName => {\n      const elements = elmsToUpdate.get(propName);\n      if (elements) {\n        elmsToUpdate.set(propName, elements.filter(forceUpdate));\n      }\n      cleanupElements(elmsToUpdate);\n    },\n    reset: () => {\n      elmsToUpdate.forEach(elms => elms.forEach(forceUpdate));\n      cleanupElements(elmsToUpdate);\n    }\n  };\n};\nconst unwrap = val => typeof val === 'function' ? val() : val;\nconst createObservableMap = (defaultState, shouldUpdate = (a, b) => a !== b) => {\n  const unwrappedState = unwrap(defaultState);\n  let states = new Map(Object.entries(unwrappedState !== null && unwrappedState !== void 0 ? unwrappedState : {}));\n  const handlers = {\n    dispose: [],\n    get: [],\n    set: [],\n    reset: []\n  };\n  const reset = () => {\n    var _a;\n    // When resetting the state, the default state may be a function - unwrap it to invoke it.\n    // otherwise, the state won't be properly reset\n    states = new Map(Object.entries((_a = unwrap(defaultState)) !== null && _a !== void 0 ? _a : {}));\n    handlers.reset.forEach(cb => cb());\n  };\n  const dispose = () => {\n    // Call first dispose as resetting the state would\n    // cause less updates ;)\n    handlers.dispose.forEach(cb => cb());\n    reset();\n  };\n  const get = propName => {\n    handlers.get.forEach(cb => cb(propName));\n    return states.get(propName);\n  };\n  const set = (propName, value) => {\n    const oldValue = states.get(propName);\n    if (shouldUpdate(value, oldValue, propName)) {\n      states.set(propName, value);\n      handlers.set.forEach(cb => cb(propName, value, oldValue));\n    }\n  };\n  const state = typeof Proxy === 'undefined' ? {} : new Proxy(unwrappedState, {\n    get(_, propName) {\n      return get(propName);\n    },\n    ownKeys(_) {\n      return Array.from(states.keys());\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    },\n    has(_, propName) {\n      return states.has(propName);\n    },\n    set(_, propName, value) {\n      set(propName, value);\n      return true;\n    }\n  });\n  const on = (eventName, callback) => {\n    handlers[eventName].push(callback);\n    return () => {\n      removeFromArray(handlers[eventName], callback);\n    };\n  };\n  const onChange = (propName, cb) => {\n    const unSet = on('set', (key, newValue) => {\n      if (key === propName) {\n        cb(newValue);\n      }\n    });\n    // We need to unwrap the defaultState because it might be a function.\n    // Otherwise we might not be sending the right reset value.\n    const unReset = on('reset', () => cb(unwrap(defaultState)[propName]));\n    return () => {\n      unSet();\n      unReset();\n    };\n  };\n  const use = (...subscriptions) => {\n    const unsubs = subscriptions.reduce((unsubs, subscription) => {\n      if (subscription.set) {\n        unsubs.push(on('set', subscription.set));\n      }\n      if (subscription.get) {\n        unsubs.push(on('get', subscription.get));\n      }\n      if (subscription.reset) {\n        unsubs.push(on('reset', subscription.reset));\n      }\n      if (subscription.dispose) {\n        unsubs.push(on('dispose', subscription.dispose));\n      }\n      return unsubs;\n    }, []);\n    return () => unsubs.forEach(unsub => unsub());\n  };\n  const forceUpdate = key => {\n    const oldValue = states.get(key);\n    handlers.set.forEach(cb => cb(key, oldValue, oldValue));\n  };\n  return {\n    state,\n    get,\n    set,\n    on,\n    onChange,\n    use,\n    dispose,\n    reset,\n    forceUpdate\n  };\n};\nconst removeFromArray = (array, item) => {\n  const index = array.indexOf(item);\n  if (index >= 0) {\n    array[index] = array[array.length - 1];\n    array.length--;\n  }\n};\nconst createStore = (defaultState, shouldUpdate) => {\n  const map = createObservableMap(defaultState, shouldUpdate);\n  map.use(stencilSubscription());\n  return map;\n};\n\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nfunction isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfWeek = startOfUTCWeek(dirtyDateLeft, options);\n  var dateRightStartOfWeek = startOfUTCWeek(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}\nvar formatDistanceLocale$8 = {\n  lessThanXSeconds: {\n    one: {\n      regular: 'méně než sekunda',\n      past: 'před méně než sekundou',\n      future: 'za méně než sekundu'\n    },\n    few: {\n      regular: 'méně než {{count}} sekundy',\n      past: 'před méně než {{count}} sekundami',\n      future: 'za méně než {{count}} sekundy'\n    },\n    many: {\n      regular: 'méně než {{count}} sekund',\n      past: 'před méně než {{count}} sekundami',\n      future: 'za méně než {{count}} sekund'\n    }\n  },\n  xSeconds: {\n    one: {\n      regular: 'sekunda',\n      past: 'před sekundou',\n      future: 'za sekundu'\n    },\n    few: {\n      regular: '{{count}} sekundy',\n      past: 'před {{count}} sekundami',\n      future: 'za {{count}} sekundy'\n    },\n    many: {\n      regular: '{{count}} sekund',\n      past: 'před {{count}} sekundami',\n      future: 'za {{count}} sekund'\n    }\n  },\n  halfAMinute: {\n    other: {\n      regular: 'půl minuty',\n      past: 'před půl minutou',\n      future: 'za půl minuty'\n    }\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: 'méně než minuta',\n      past: 'před méně než minutou',\n      future: 'za méně než minutu'\n    },\n    few: {\n      regular: 'méně než {{count}} minuty',\n      past: 'před méně než {{count}} minutami',\n      future: 'za méně než {{count}} minuty'\n    },\n    many: {\n      regular: 'méně než {{count}} minut',\n      past: 'před méně než {{count}} minutami',\n      future: 'za méně než {{count}} minut'\n    }\n  },\n  xMinutes: {\n    one: {\n      regular: 'minuta',\n      past: 'před minutou',\n      future: 'za minutu'\n    },\n    few: {\n      regular: '{{count}} minuty',\n      past: 'před {{count}} minutami',\n      future: 'za {{count}} minuty'\n    },\n    many: {\n      regular: '{{count}} minut',\n      past: 'před {{count}} minutami',\n      future: 'za {{count}} minut'\n    }\n  },\n  aboutXHours: {\n    one: {\n      regular: 'přibližně hodina',\n      past: 'přibližně před hodinou',\n      future: 'přibližně za hodinu'\n    },\n    few: {\n      regular: 'přibližně {{count}} hodiny',\n      past: 'přibližně před {{count}} hodinami',\n      future: 'přibližně za {{count}} hodiny'\n    },\n    many: {\n      regular: 'přibližně {{count}} hodin',\n      past: 'přibližně před {{count}} hodinami',\n      future: 'přibližně za {{count}} hodin'\n    }\n  },\n  xHours: {\n    one: {\n      regular: 'hodina',\n      past: 'před hodinou',\n      future: 'za hodinu'\n    },\n    few: {\n      regular: '{{count}} hodiny',\n      past: 'před {{count}} hodinami',\n      future: 'za {{count}} hodiny'\n    },\n    many: {\n      regular: '{{count}} hodin',\n      past: 'před {{count}} hodinami',\n      future: 'za {{count}} hodin'\n    }\n  },\n  xDays: {\n    one: {\n      regular: 'den',\n      past: 'před dnem',\n      future: 'za den'\n    },\n    few: {\n      regular: '{{count}} dny',\n      past: 'před {{count}} dny',\n      future: 'za {{count}} dny'\n    },\n    many: {\n      regular: '{{count}} dní',\n      past: 'před {{count}} dny',\n      future: 'za {{count}} dní'\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      regular: 'přibližně týden',\n      past: 'přibližně před týdnem',\n      future: 'přibližně za týden'\n    },\n    few: {\n      regular: 'přibližně {{count}} týdny',\n      past: 'přibližně před {{count}} týdny',\n      future: 'přibližně za {{count}} týdny'\n    },\n    many: {\n      regular: 'přibližně {{count}} týdnů',\n      past: 'přibližně před {{count}} týdny',\n      future: 'přibližně za {{count}} týdnů'\n    }\n  },\n  xWeeks: {\n    one: {\n      regular: 'týden',\n      past: 'před týdnem',\n      future: 'za týden'\n    },\n    few: {\n      regular: '{{count}} týdny',\n      past: 'před {{count}} týdny',\n      future: 'za {{count}} týdny'\n    },\n    many: {\n      regular: '{{count}} týdnů',\n      past: 'před {{count}} týdny',\n      future: 'za {{count}} týdnů'\n    }\n  },\n  aboutXMonths: {\n    one: {\n      regular: 'přibližně měsíc',\n      past: 'přibližně před měsícem',\n      future: 'přibližně za měsíc'\n    },\n    few: {\n      regular: 'přibližně {{count}} měsíce',\n      past: 'přibližně před {{count}} měsíci',\n      future: 'přibližně za {{count}} měsíce'\n    },\n    many: {\n      regular: 'přibližně {{count}} měsíců',\n      past: 'přibližně před {{count}} měsíci',\n      future: 'přibližně za {{count}} měsíců'\n    }\n  },\n  xMonths: {\n    one: {\n      regular: 'měsíc',\n      past: 'před měsícem',\n      future: 'za měsíc'\n    },\n    few: {\n      regular: '{{count}} měsíce',\n      past: 'před {{count}} měsíci',\n      future: 'za {{count}} měsíce'\n    },\n    many: {\n      regular: '{{count}} měsíců',\n      past: 'před {{count}} měsíci',\n      future: 'za {{count}} měsíců'\n    }\n  },\n  aboutXYears: {\n    one: {\n      regular: 'přibližně rok',\n      past: 'přibližně před rokem',\n      future: 'přibližně za rok'\n    },\n    few: {\n      regular: 'přibližně {{count}} roky',\n      past: 'přibližně před {{count}} roky',\n      future: 'přibližně za {{count}} roky'\n    },\n    many: {\n      regular: 'přibližně {{count}} roků',\n      past: 'přibližně před {{count}} roky',\n      future: 'přibližně za {{count}} roků'\n    }\n  },\n  xYears: {\n    one: {\n      regular: 'rok',\n      past: 'před rokem',\n      future: 'za rok'\n    },\n    few: {\n      regular: '{{count}} roky',\n      past: 'před {{count}} roky',\n      future: 'za {{count}} roky'\n    },\n    many: {\n      regular: '{{count}} roků',\n      past: 'před {{count}} roky',\n      future: 'za {{count}} roků'\n    }\n  },\n  overXYears: {\n    one: {\n      regular: 'více než rok',\n      past: 'před více než rokem',\n      future: 'za více než rok'\n    },\n    few: {\n      regular: 'více než {{count}} roky',\n      past: 'před více než {{count}} roky',\n      future: 'za více než {{count}} roky'\n    },\n    many: {\n      regular: 'více než {{count}} roků',\n      past: 'před více než {{count}} roky',\n      future: 'za více než {{count}} roků'\n    }\n  },\n  almostXYears: {\n    one: {\n      regular: 'skoro rok',\n      past: 'skoro před rokem',\n      future: 'skoro za rok'\n    },\n    few: {\n      regular: 'skoro {{count}} roky',\n      past: 'skoro před {{count}} roky',\n      future: 'skoro za {{count}} roky'\n    },\n    many: {\n      regular: 'skoro {{count}} roků',\n      past: 'skoro před {{count}} roky',\n      future: 'skoro za {{count}} roků'\n    }\n  }\n};\nfunction formatDistance$8(token, count, options) {\n  options = options || {};\n  var scheme = formatDistanceLocale$8[token]; // cs pluralization\n\n  var pluralToken;\n  if (typeof scheme.other === 'object') {\n    pluralToken = 'other';\n  } else if (count === 1) {\n    pluralToken = 'one';\n  } else if (count > 1 && count < 5) {\n    pluralToken = 'few';\n  } else {\n    pluralToken = 'many';\n  } // times\n\n  var suffixExist = options.addSuffix === true;\n  var comparison = options.comparison;\n  var timeToken;\n  if (suffixExist && comparison === -1) {\n    timeToken = 'past';\n  } else if (suffixExist && comparison === 1) {\n    timeToken = 'future';\n  } else {\n    timeToken = 'regular';\n  }\n  return scheme[pluralToken][timeToken].replace('{{count}}', count);\n}\nvar dateFormats$8 = {\n  full: 'EEEE, d. MMMM yyyy',\n  long: 'd. MMMM yyyy',\n  medium: 'd. M. yyyy',\n  short: 'dd.MM.yyyy'\n};\nvar timeFormats$8 = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats$8 = {\n  full: \"{{date}} 'v' {{time}}\",\n  long: \"{{date}} 'v' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$8 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$8,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$8,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$8,\n    defaultWidth: 'full'\n  })\n};\nvar accusativeWeekdays = ['neděli', 'pondělí', 'úterý', 'středu', 'čtvrtek', 'pátek', 'sobotu'];\nvar formatRelativeLocale$8 = {\n  lastWeek: \"'poslední' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: function (date, _baseDate, _options) {\n    var day = date.getUTCDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: 'P'\n};\nfunction formatRelative$8(token, date, baseDate, options) {\n  var format = formatRelativeLocale$8[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n}\nvar eraValues$8 = {\n  narrow: ['př. n. l.', 'n. l.'],\n  abbreviated: ['př. n. l.', 'n. l.'],\n  wide: ['před naším letopočtem', 'našeho letopočtu']\n};\nvar quarterValues$8 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1. čtvrtletí', '2. čtvrtletí', '3. čtvrtletí', '4. čtvrtletí'],\n  wide: ['1. čtvrtletí', '2. čtvrtletí', '3. čtvrtletí', '4. čtvrtletí']\n};\nvar monthValues$8 = {\n  narrow: ['L', 'Ú', 'B', 'D', 'K', 'Č', 'Č', 'S', 'Z', 'Ř', 'L', 'P'],\n  abbreviated: ['led', 'úno', 'bře', 'dub', 'kvě', 'čvn', 'čvc', 'srp', 'zář', 'říj', 'lis', 'pro'],\n  wide: ['leden', 'únor', 'březen', 'duben', 'květen', 'červen', 'červenec', 'srpen', 'září', 'říjen', 'listopad', 'prosinec']\n};\nvar formattingMonthValues$1 = {\n  narrow: ['L', 'Ú', 'B', 'D', 'K', 'Č', 'Č', 'S', 'Z', 'Ř', 'L', 'P'],\n  abbreviated: ['led', 'úno', 'bře', 'dub', 'kvě', 'čvn', 'čvc', 'srp', 'zář', 'říj', 'lis', 'pro'],\n  wide: ['ledna', 'února', 'března', 'dubna', 'května', 'června', 'července', 'srpna', 'září', 'října', 'listopadu', 'prosince']\n};\nvar dayValues$8 = {\n  narrow: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],\n  short: ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],\n  abbreviated: ['ned', 'pon', 'úte', 'stř', 'čtv', 'pát', 'sob'],\n  wide: ['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota']\n};\nvar dayPeriodValues$8 = {\n  narrow: {\n    am: 'dop.',\n    pm: 'odp.',\n    midnight: 'půlnoc',\n    noon: 'poledne',\n    morning: 'ráno',\n    afternoon: 'odpoledne',\n    evening: 'večer',\n    night: 'noc'\n  },\n  abbreviated: {\n    am: 'dop.',\n    pm: 'odp.',\n    midnight: 'půlnoc',\n    noon: 'poledne',\n    morning: 'ráno',\n    afternoon: 'odpoledne',\n    evening: 'večer',\n    night: 'noc'\n  },\n  wide: {\n    am: 'dopoledne',\n    pm: 'odpoledne',\n    midnight: 'půlnoc',\n    noon: 'poledne',\n    morning: 'ráno',\n    afternoon: 'odpoledne',\n    evening: 'večer',\n    night: 'noc'\n  }\n};\nvar formattingDayPeriodValues$6 = {\n  narrow: {\n    am: 'dop.',\n    pm: 'odp.',\n    midnight: 'půlnoc',\n    noon: 'poledne',\n    morning: 'ráno',\n    afternoon: 'odpoledne',\n    evening: 'večer',\n    night: 'noc'\n  },\n  abbreviated: {\n    am: 'dop.',\n    pm: 'odp.',\n    midnight: 'půlnoc',\n    noon: 'poledne',\n    morning: 'ráno',\n    afternoon: 'odpoledne',\n    evening: 'večer',\n    night: 'noc'\n  },\n  wide: {\n    am: 'dopoledne',\n    pm: 'odpoledne',\n    midnight: 'půlnoc',\n    noon: 'poledne',\n    morning: 'ráno',\n    afternoon: 'odpoledne',\n    evening: 'večer',\n    night: 'noc'\n  }\n};\nfunction ordinalNumber$8(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n}\nvar localize$8 = {\n  ordinalNumber: ordinalNumber$8,\n  era: buildLocalizeFn({\n    values: eraValues$8,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$8,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$8,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues$1,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$8,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$8,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$6,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$8 = /^(\\d+)\\.?/i;\nvar parseOrdinalNumberPattern$8 = /\\d+/i;\nvar matchEraPatterns$8 = {\n  narrow: /^(p[řr]ed Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n  abbreviated: /^(pe[řr]ed Kr\\.|pe[řr]ed n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n  wide: /^(p[řr]ed Kristem|pred na[šs][íi]m letopo[čc]tem|po Kristu|na[šs]eho letopo[čc]tu)/i\n};\nvar parseEraPatterns$8 = {\n  any: [/^p[řr]/i, /^(po|n)/i]\n};\nvar matchQuarterPatterns$8 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234]\\. [čc]tvrtlet[íi]/i,\n  wide: /^[1234]\\. [čc]tvrtlet[íi]/i\n};\nvar parseQuarterPatterns$8 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$8 = {\n  narrow: /^[lúubdkčcszřrlp]/i,\n  abbreviated: /^(led|[úu]no|b[řr]e|dub|kv[ěe]|[čc]vn|[čc]vc|srp|z[áa][řr]|[řr][íi]j|lis|pro)/i,\n  wide: /^(leden|ledna|[úu]nora?|b[řr]ezen|b[řr]ezna|duben|dubna|kv[ěe]ten|kv[ěe]tna|[čc]erven(ec|ce)?|[čc]ervna|srpen|srpna|z[áa][řr][íi]|[řr][íi]jen|[řr][íi]jna|listopad(a|u)?|prosinec|prosince)/i\n};\nvar parseMonthPatterns$8 = {\n  narrow: [/^l/i, /^[úu]/i, /^b/i, /^d/i, /^k/i, /^[čc]/i, /^[čc]/i, /^s/i, /^z/i, /^[řr]/i, /^l/i, /^p/i],\n  any: [/^led/i, /^[úu]n/i, /^b[řr]e/i, /^dub/i, /^kv[ěe]/i, /^[čc]vn|[čc]erven(?!\\w)|[čc]ervna/i, /^[čc]vc|[čc]erven(ec|ce)/i, /^srp/i, /^z[áa][řr]/i, /^[řr][íi]j/i, /^lis/i, /^pro/i]\n};\nvar matchDayPatterns$8 = {\n  narrow: /^[npuúsčps]/i,\n  short: /^(ne|po|[úu]t|st|[čc]t|p[áa]|so)/i,\n  abbreviated: /^(ne|po|[úu]t|st|[čc]t|p[áa]|so)/i,\n  wide: /^(ned[ěe]le|pond[ěe]l[íi]|[úu]ter[ýy]|st[řr]eda|[čc]tvrtek|p[áa]tek|sobota)/i\n};\nvar parseDayPatterns$8 = {\n  narrow: [/^n/i, /^p/i, /^[úu]/i, /^s/i, /^[čc]/i, /^p/i, /^s/i],\n  any: [/^ne/i, /^po/i, /^ut/i, /^st/i, /^[čc]t/i, /^p/i, /^so/i]\n};\nvar matchDayPeriodPatterns$8 = {\n  any: /^dopoledne|dop\\.?|odpoledne|odp\\.?|půlnoc|poledne|r[áa]no|odpoledne|ve[čc]er|(v )?noci/i\n};\nvar parseDayPeriodPatterns$8 = {\n  any: {\n    am: /^dop/i,\n    pm: /^odp/i,\n    midnight: /^p[ůu]lnoc/i,\n    noon: /^poledne/i,\n    morning: /r[áa]no/i,\n    afternoon: /odpoledne/i,\n    evening: /ve[čc]er/i,\n    night: /noc/i\n  }\n};\nvar match$8 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$8,\n    parsePattern: parseOrdinalNumberPattern$8,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$8,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$8,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$8,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$8,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$8,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$8,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$8,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$8,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$8,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$8,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Czech locale.\n * @language Czech\n * @iso-639-2 ces\n * <AUTHOR> Rus [@davidrus]{@link https://github.com/davidrus}\n * <AUTHOR> Hrách [@SilenY]{@link https://github.com/SilenY}\n * <AUTHOR> Bíroš [@JozefBiros]{@link https://github.com/JozefBiros}\n */\n\nvar locale$8 = {\n  code: 'cs',\n  formatDistance: formatDistance$8,\n  formatLong: formatLong$8,\n  formatRelative: formatRelative$8,\n  localize: localize$8,\n  match: match$8,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\nvar formatDistanceLocale$7 = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Sekunde',\n      other: 'weniger als {{count}} Sekunden'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    },\n    withPreposition: {\n      one: '1 Sekunde',\n      other: '{{count}} Sekunden'\n    }\n  },\n  halfAMinute: {\n    standalone: 'halbe Minute',\n    withPreposition: 'halben Minute'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    },\n    withPreposition: {\n      one: 'weniger als 1 Minute',\n      other: 'weniger als {{count}} Minuten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    },\n    withPreposition: {\n      one: '1 Minute',\n      other: '{{count}} Minuten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    },\n    withPreposition: {\n      one: 'etwa 1 Stunde',\n      other: 'etwa {{count}} Stunden'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    },\n    withPreposition: {\n      one: '1 Stunde',\n      other: '{{count}} Stunden'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: '1 Tag',\n      other: '{{count}} Tage'\n    },\n    withPreposition: {\n      one: '1 Tag',\n      other: '{{count}} Tagen'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'etwa 1 Woche',\n      other: 'etwa {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: '1 Woche',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monate'\n    },\n    withPreposition: {\n      one: 'etwa 1 Monat',\n      other: 'etwa {{count}} Monaten'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: '1 Monat',\n      other: '{{count}} Monate'\n    },\n    withPreposition: {\n      one: '1 Monat',\n      other: '{{count}} Monaten'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'etwa 1 Jahr',\n      other: 'etwa {{count}} Jahren'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: '1 Jahr',\n      other: '{{count}} Jahre'\n    },\n    withPreposition: {\n      one: '1 Jahr',\n      other: '{{count}} Jahren'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'mehr als 1 Jahr',\n      other: 'mehr als {{count}} Jahren'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahre'\n    },\n    withPreposition: {\n      one: 'fast 1 Jahr',\n      other: 'fast {{count}} Jahren'\n    }\n  }\n};\nvar formatDistance$7 = function (token, count, options) {\n  var result;\n  var tokenValue = options !== null && options !== void 0 && options.addSuffix ? formatDistanceLocale$7[token].withPreposition : formatDistanceLocale$7[token].standalone;\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return 'vor ' + result;\n    }\n  }\n  return result;\n};\n\n// DIN 5008: https://de.wikipedia.org/wiki/Datumsformat#DIN_5008\nvar dateFormats$7 = {\n  full: 'EEEE, do MMMM y',\n  // Montag, 7. Januar 2018\n  long: 'do MMMM y',\n  // 7. Januar 2018\n  medium: 'do MMM y',\n  // 7. Jan. 2018\n  short: 'dd.MM.y' // 07.01.2018\n};\nvar timeFormats$7 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$7 = {\n  full: \"{{date}} 'um' {{time}}\",\n  long: \"{{date}} 'um' {{time}}\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong$7 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$7,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$7,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$7,\n    defaultWidth: 'full'\n  })\n};\nvar formatRelativeLocale$7 = {\n  lastWeek: \"'letzten' eeee 'um' p\",\n  yesterday: \"'gestern um' p\",\n  today: \"'heute um' p\",\n  tomorrow: \"'morgen um' p\",\n  nextWeek: \"eeee 'um' p\",\n  other: 'P'\n};\nvar formatRelative$7 = function (token, _date, _baseDate, _options) {\n  return formatRelativeLocale$7[token];\n};\nvar eraValues$7 = {\n  narrow: ['v.Chr.', 'n.Chr.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['vor Christus', 'nach Christus']\n};\nvar quarterValues$7 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal']\n}; // Note: in German, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues$7 = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],\n  wide: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember']\n}; // https://st.unicode.org/cldr-apps/v#/de/Gregorian/\n\nvar formattingMonthValues = {\n  narrow: monthValues$7.narrow,\n  abbreviated: ['Jan.', 'Feb.', 'März', 'Apr.', 'Mai', 'Juni', 'Juli', 'Aug.', 'Sep.', 'Okt.', 'Nov.', 'Dez.'],\n  wide: monthValues$7.wide\n};\nvar dayValues$7 = {\n  narrow: ['S', 'M', 'D', 'M', 'D', 'F', 'S'],\n  short: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],\n  abbreviated: ['So.', 'Mo.', 'Di.', 'Mi.', 'Do.', 'Fr.', 'Sa.'],\n  wide: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']\n}; // https://www.unicode.org/cldr/charts/32/summary/de.html#1881\n\nvar dayPeriodValues$7 = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachm.',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'Morgen',\n    afternoon: 'Nachmittag',\n    evening: 'Abend',\n    night: 'Nacht'\n  }\n};\nvar formattingDayPeriodValues$5 = {\n  narrow: {\n    am: 'vm.',\n    pm: 'nm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachm.',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  abbreviated: {\n    am: 'vorm.',\n    pm: 'nachm.',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  },\n  wide: {\n    am: 'vormittags',\n    pm: 'nachmittags',\n    midnight: 'Mitternacht',\n    noon: 'Mittag',\n    morning: 'morgens',\n    afternoon: 'nachmittags',\n    evening: 'abends',\n    night: 'nachts'\n  }\n};\nvar ordinalNumber$7 = function (dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize$7 = {\n  ordinalNumber: ordinalNumber$7,\n  era: buildLocalizeFn({\n    values: eraValues$7,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$7,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$7,\n    formattingValues: formattingMonthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$7,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$7,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$5,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$7 = /^(\\d+)(\\.)?/i;\nvar parseOrdinalNumberPattern$7 = /\\d+/i;\nvar matchEraPatterns$7 = {\n  narrow: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  abbreviated: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  wide: /^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i\n};\nvar parseEraPatterns$7 = {\n  any: [/^v/i, /^n/i]\n};\nvar matchQuarterPatterns$7 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](\\.)? Quartal/i\n};\nvar parseQuarterPatterns$7 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$7 = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\\.?/i,\n  wide: /^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i\n};\nvar parseMonthPatterns$7 = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^j[aä]/i, /^f/i, /^mär/i, /^ap/i, /^mai/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns$7 = {\n  narrow: /^[smdmf]/i,\n  short: /^(so|mo|di|mi|do|fr|sa)/i,\n  abbreviated: /^(son?|mon?|die?|mit?|don?|fre?|sam?)\\.?/i,\n  wide: /^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i\n};\nvar parseDayPatterns$7 = {\n  any: [/^so/i, /^mo/i, /^di/i, /^mi/i, /^do/i, /^f/i, /^sa/i]\n};\nvar matchDayPeriodPatterns$7 = {\n  narrow: /^(vm\\.?|nm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n  abbreviated: /^(vorm\\.?|nachm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n  wide: /^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i\n};\nvar parseDayPeriodPatterns$7 = {\n  any: {\n    am: /^v/i,\n    pm: /^n/i,\n    midnight: /^Mitte/i,\n    noon: /^Mitta/i,\n    morning: /morgens/i,\n    afternoon: /nachmittags/i,\n    // will never be matched. Afternoon is matched by `pm`\n    evening: /abends/i,\n    night: /nachts/i // will never be matched. Night is matched by `pm`\n  }\n};\nvar match$7 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$7,\n    parsePattern: parseOrdinalNumberPattern$7,\n    valueCallback: function (value) {\n      return parseInt(value);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$7,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$7,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$7,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$7,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$7,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$7,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$7,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$7,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$7,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns$7,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary German locale.\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> Eilmsteiner [@DeMuu]{@link https://github.com/DeMuu}\n * <AUTHOR> [@asia-t]{@link https://github.com/asia-t}\n * <AUTHOR> Vuong Ngo [@vanvuongngo]{@link https://github.com/vanvuongngo}\n * <AUTHOR> [@pex]{@link https://github.com/pex}\n * <AUTHOR> Keck [@Philipp91]{@link https://github.com/Philipp91}\n */\nvar locale$7 = {\n  code: 'de',\n  formatDistance: formatDistance$7,\n  formatLong: formatLong$7,\n  formatRelative: formatRelative$7,\n  localize: localize$7,\n  match: match$7,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\nvar formatDistanceLocale$6 = {\n  lessThanXSeconds: {\n    one: 'menos de un segundo',\n    other: 'menos de {{count}} segundos'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundos'\n  },\n  halfAMinute: 'medio minuto',\n  lessThanXMinutes: {\n    one: 'menos de un minuto',\n    other: 'menos de {{count}} minutos'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutos'\n  },\n  aboutXHours: {\n    one: 'alrededor de 1 hora',\n    other: 'alrededor de {{count}} horas'\n  },\n  xHours: {\n    one: '1 hora',\n    other: '{{count}} horas'\n  },\n  xDays: {\n    one: '1 día',\n    other: '{{count}} días'\n  },\n  aboutXWeeks: {\n    one: 'alrededor de 1 semana',\n    other: 'alrededor de {{count}} semanas'\n  },\n  xWeeks: {\n    one: '1 semana',\n    other: '{{count}} semanas'\n  },\n  aboutXMonths: {\n    one: 'alrededor de 1 mes',\n    other: 'alrededor de {{count}} meses'\n  },\n  xMonths: {\n    one: '1 mes',\n    other: '{{count}} meses'\n  },\n  aboutXYears: {\n    one: 'alrededor de 1 año',\n    other: 'alrededor de {{count}} años'\n  },\n  xYears: {\n    one: '1 año',\n    other: '{{count}} años'\n  },\n  overXYears: {\n    one: 'más de 1 año',\n    other: 'más de {{count}} años'\n  },\n  almostXYears: {\n    one: 'casi 1 año',\n    other: 'casi {{count}} años'\n  }\n};\nvar formatDistance$6 = function (token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale$6[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'en ' + result;\n    } else {\n      return 'hace ' + result;\n    }\n  }\n  return result;\n};\nvar dateFormats$6 = {\n  full: \"EEEE, d 'de' MMMM 'de' y\",\n  long: \"d 'de' MMMM 'de' y\",\n  medium: 'd MMM y',\n  short: 'dd/MM/y'\n};\nvar timeFormats$6 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$6 = {\n  full: \"{{date}} 'a las' {{time}}\",\n  long: \"{{date}} 'a las' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$6 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$6,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$6,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$6,\n    defaultWidth: 'full'\n  })\n};\nvar formatRelativeLocale$6 = {\n  lastWeek: \"'el' eeee 'pasado a la' p\",\n  yesterday: \"'ayer a la' p\",\n  today: \"'hoy a la' p\",\n  tomorrow: \"'mañana a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'pasado a las' p\",\n  yesterday: \"'ayer a las' p\",\n  today: \"'hoy a las' p\",\n  tomorrow: \"'mañana a las' p\",\n  nextWeek: \"eeee 'a las' p\",\n  other: 'P'\n};\nvar formatRelative$6 = function (token, date, _baseDate, _options) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  } else {\n    return formatRelativeLocale$6[token];\n  }\n};\nvar eraValues$6 = {\n  narrow: ['AC', 'DC'],\n  abbreviated: ['AC', 'DC'],\n  wide: ['antes de cristo', 'después de cristo']\n};\nvar quarterValues$6 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['T1', 'T2', 'T3', 'T4'],\n  wide: ['1º trimestre', '2º trimestre', '3º trimestre', '4º trimestre']\n};\nvar monthValues$6 = {\n  narrow: ['e', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n  abbreviated: ['ene', 'feb', 'mar', 'abr', 'may', 'jun', 'jul', 'ago', 'sep', 'oct', 'nov', 'dic'],\n  wide: ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre']\n};\nvar dayValues$6 = {\n  narrow: ['d', 'l', 'm', 'm', 'j', 'v', 's'],\n  short: ['do', 'lu', 'ma', 'mi', 'ju', 'vi', 'sá'],\n  abbreviated: ['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],\n  wide: ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado']\n};\nvar dayPeriodValues$6 = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mn',\n    noon: 'md',\n    morning: 'mañana',\n    afternoon: 'tarde',\n    evening: 'tarde',\n    night: 'noche'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'medianoche',\n    noon: 'mediodia',\n    morning: 'mañana',\n    afternoon: 'tarde',\n    evening: 'tarde',\n    night: 'noche'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'medianoche',\n    noon: 'mediodia',\n    morning: 'mañana',\n    afternoon: 'tarde',\n    evening: 'tarde',\n    night: 'noche'\n  }\n};\nvar formattingDayPeriodValues$4 = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'mn',\n    noon: 'md',\n    morning: 'de la mañana',\n    afternoon: 'de la tarde',\n    evening: 'de la tarde',\n    night: 'de la noche'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'medianoche',\n    noon: 'mediodia',\n    morning: 'de la mañana',\n    afternoon: 'de la tarde',\n    evening: 'de la tarde',\n    night: 'de la noche'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'medianoche',\n    noon: 'mediodia',\n    morning: 'de la mañana',\n    afternoon: 'de la tarde',\n    evening: 'de la tarde',\n    night: 'de la noche'\n  }\n};\nvar ordinalNumber$6 = function (dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'º';\n};\nvar localize$6 = {\n  ordinalNumber: ordinalNumber$6,\n  era: buildLocalizeFn({\n    values: eraValues$6,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$6,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$6,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$6,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$6,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$4,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$6 = /^(\\d+)(º)?/i;\nvar parseOrdinalNumberPattern$6 = /\\d+/i;\nvar matchEraPatterns$6 = {\n  narrow: /^(ac|dc|a|d)/i,\n  abbreviated: /^(a\\.?\\s?c\\.?|a\\.?\\s?e\\.?\\s?c\\.?|d\\.?\\s?c\\.?|e\\.?\\s?c\\.?)/i,\n  wide: /^(antes de cristo|antes de la era com[uú]n|despu[eé]s de cristo|era com[uú]n)/i\n};\nvar parseEraPatterns$6 = {\n  any: [/^ac/i, /^dc/i],\n  wide: [/^(antes de cristo|antes de la era com[uú]n)/i, /^(despu[eé]s de cristo|era com[uú]n)/i]\n};\nvar matchQuarterPatterns$6 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^[1234](º)? trimestre/i\n};\nvar parseQuarterPatterns$6 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$6 = {\n  narrow: /^[efmajsond]/i,\n  abbreviated: /^(ene|feb|mar|abr|may|jun|jul|ago|sep|oct|nov|dic)/i,\n  wide: /^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i\n};\nvar parseMonthPatterns$6 = {\n  narrow: [/^e/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^en/i, /^feb/i, /^mar/i, /^abr/i, /^may/i, /^jun/i, /^jul/i, /^ago/i, /^sep/i, /^oct/i, /^nov/i, /^dic/i]\n};\nvar matchDayPatterns$6 = {\n  narrow: /^[dlmjvs]/i,\n  short: /^(do|lu|ma|mi|ju|vi|s[áa])/i,\n  abbreviated: /^(dom|lun|mar|mi[ée]|jue|vie|s[áa]b)/i,\n  wide: /^(domingo|lunes|martes|mi[ée]rcoles|jueves|viernes|s[áa]bado)/i\n};\nvar parseDayPatterns$6 = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^do/i, /^lu/i, /^ma/i, /^mi/i, /^ju/i, /^vi/i, /^sa/i]\n};\nvar matchDayPeriodPatterns$6 = {\n  narrow: /^(a|p|mn|md|(de la|a las) (mañana|tarde|noche))/i,\n  any: /^([ap]\\.?\\s?m\\.?|medianoche|mediodia|(de la|a las) (mañana|tarde|noche))/i\n};\nvar parseDayPeriodPatterns$6 = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mn/i,\n    noon: /^md/i,\n    morning: /mañana/i,\n    afternoon: /tarde/i,\n    evening: /tarde/i,\n    night: /noche/i\n  }\n};\nvar match$6 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$6,\n    parsePattern: parseOrdinalNumberPattern$6,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$6,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$6,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$6,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$6,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$6,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$6,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$6,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$6,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$6,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$6,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Spanish locale.\n * @language Spanish\n * @iso-639-2 spa\n * <AUTHOR> Angosto [@juanangosto]{@link https://github.com/juanangosto}\n * <AUTHOR> Grau [@guigrpa]{@link https://github.com/guigrpa}\n * <AUTHOR> Agüero [@fjaguero]{@link https://github.com/fjaguero}\n * <AUTHOR> Haro [@harogaston]{@link https://github.com/harogaston}\n * <AUTHOR> Carballo [@YagoCarballo]{@link https://github.com/YagoCarballo}\n */\nvar locale$6 = {\n  code: 'es',\n  formatDistance: formatDistance$6,\n  formatLong: formatLong$6,\n  formatRelative: formatRelative$6,\n  localize: localize$6,\n  match: match$6,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 1\n  }\n};\nvar formatDistanceLocale$5 = {\n  lessThanXSeconds: {\n    one: 'moins d’une seconde',\n    other: 'moins de {{count}} secondes'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} secondes'\n  },\n  halfAMinute: '30 secondes',\n  lessThanXMinutes: {\n    one: 'moins d’une minute',\n    other: 'moins de {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'environ 1 heure',\n    other: 'environ {{count}} heures'\n  },\n  xHours: {\n    one: '1 heure',\n    other: '{{count}} heures'\n  },\n  xDays: {\n    one: '1 jour',\n    other: '{{count}} jours'\n  },\n  aboutXWeeks: {\n    one: 'environ 1 semaine',\n    other: 'environ {{count}} semaines'\n  },\n  xWeeks: {\n    one: '1 semaine',\n    other: '{{count}} semaines'\n  },\n  aboutXMonths: {\n    one: 'environ 1 mois',\n    other: 'environ {{count}} mois'\n  },\n  xMonths: {\n    one: '1 mois',\n    other: '{{count}} mois'\n  },\n  aboutXYears: {\n    one: 'environ 1 an',\n    other: 'environ {{count}} ans'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} ans'\n  },\n  overXYears: {\n    one: 'plus d’un an',\n    other: 'plus de {{count}} ans'\n  },\n  almostXYears: {\n    one: 'presqu’un an',\n    other: 'presque {{count}} ans'\n  }\n};\nvar formatDistance$5 = function (token, count, options) {\n  var result;\n  var form = formatDistanceLocale$5[token];\n  if (typeof form === 'string') {\n    result = form;\n  } else if (count === 1) {\n    result = form.one;\n  } else {\n    result = form.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dans ' + result;\n    } else {\n      return 'il y a ' + result;\n    }\n  }\n  return result;\n};\nvar dateFormats$5 = {\n  full: 'EEEE d MMMM y',\n  long: 'd MMMM y',\n  medium: 'd MMM y',\n  short: 'dd/MM/y'\n};\nvar timeFormats$5 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$5 = {\n  full: \"{{date}} 'à' {{time}}\",\n  long: \"{{date}} 'à' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$5 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$5,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$5,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$5,\n    defaultWidth: 'full'\n  })\n};\nvar formatRelativeLocale$5 = {\n  lastWeek: \"eeee 'dernier à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'prochain à' p\",\n  other: 'P'\n};\nvar formatRelative$5 = function (token, _date, _baseDate, _options) {\n  return formatRelativeLocale$5[token];\n};\nvar eraValues$5 = {\n  narrow: ['av. J.-C', 'ap. J.-C'],\n  abbreviated: ['av. J.-C', 'ap. J.-C'],\n  wide: ['avant Jésus-Christ', 'après Jésus-Christ']\n};\nvar quarterValues$5 = {\n  narrow: ['T1', 'T2', 'T3', 'T4'],\n  abbreviated: ['1er trim.', '2ème trim.', '3ème trim.', '4ème trim.'],\n  wide: ['1er trimestre', '2ème trimestre', '3ème trimestre', '4ème trimestre']\n};\nvar monthValues$5 = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin', 'juil.', 'août', 'sept.', 'oct.', 'nov.', 'déc.'],\n  wide: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']\n};\nvar dayValues$5 = {\n  narrow: ['D', 'L', 'M', 'M', 'J', 'V', 'S'],\n  short: ['di', 'lu', 'ma', 'me', 'je', 've', 'sa'],\n  abbreviated: ['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],\n  wide: ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi']\n};\nvar dayPeriodValues$5 = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'mat.',\n    afternoon: 'ap.m.',\n    evening: 'soir',\n    night: 'mat.'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'matin',\n    afternoon: 'après-midi',\n    evening: 'soir',\n    night: 'matin'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'du matin',\n    afternoon: 'de l’après-midi',\n    evening: 'du soir',\n    night: 'du matin'\n  }\n};\nvar ordinalNumber$5 = function (dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) return '0';\n  var feminineUnits = ['year', 'week', 'hour', 'minute', 'second'];\n  var suffix;\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? 'ère' : 'er';\n  } else {\n    suffix = 'ème';\n  }\n  return number + suffix;\n};\nvar localize$5 = {\n  ordinalNumber: ordinalNumber$5,\n  era: buildLocalizeFn({\n    values: eraValues$5,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$5,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$5,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$5,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$5,\n    defaultWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$5 = /^(\\d+)(ième|ère|ème|er|e)?/i;\nvar parseOrdinalNumberPattern$5 = /\\d+/i;\nvar matchEraPatterns$5 = {\n  narrow: /^(av\\.J\\.C|ap\\.J\\.C|ap\\.J\\.-C)/i,\n  abbreviated: /^(av\\.J\\.-C|av\\.J-C|apr\\.J\\.-C|apr\\.J-C|ap\\.J-C)/i,\n  wide: /^(avant Jésus-Christ|après Jésus-Christ)/i\n};\nvar parseEraPatterns$5 = {\n  any: [/^av/i, /^ap/i]\n};\nvar matchQuarterPatterns$5 = {\n  narrow: /^T?[1234]/i,\n  abbreviated: /^[1234](er|ème|e)? trim\\.?/i,\n  wide: /^[1234](er|ème|e)? trimestre/i\n};\nvar parseQuarterPatterns$5 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$5 = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\\.?/i,\n  wide: /^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i\n};\nvar parseMonthPatterns$5 = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^av/i, /^ma/i, /^juin/i, /^juil/i, /^ao/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns$5 = {\n  narrow: /^[lmjvsd]/i,\n  short: /^(di|lu|ma|me|je|ve|sa)/i,\n  abbreviated: /^(dim|lun|mar|mer|jeu|ven|sam)\\.?/i,\n  wide: /^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i\n};\nvar parseDayPatterns$5 = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^di/i, /^lu/i, /^ma/i, /^me/i, /^je/i, /^ve/i, /^sa/i]\n};\nvar matchDayPeriodPatterns$5 = {\n  narrow: /^(a|p|minuit|midi|mat\\.?|ap\\.?m\\.?|soir|nuit)/i,\n  any: /^([ap]\\.?\\s?m\\.?|du matin|de l'après[-\\s]midi|du soir|de la nuit)/i\n};\nvar parseDayPeriodPatterns$5 = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^min/i,\n    noon: /^mid/i,\n    morning: /mat/i,\n    afternoon: /ap/i,\n    evening: /soir/i,\n    night: /nuit/i\n  }\n};\nvar match$5 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$5,\n    parsePattern: parseOrdinalNumberPattern$5,\n    valueCallback: function (value) {\n      return parseInt(value);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$5,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$5,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$5,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$5,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$5,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$5,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$5,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$5,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$5,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$5,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale.\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> Dupouy [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> B [@fbonzon]{@link https://github.com/fbonzon}\n */\n\nvar locale$5 = {\n  code: 'fr',\n  formatDistance: formatDistance$5,\n  formatLong: formatLong$5,\n  formatRelative: formatRelative$5,\n  localize: localize$5,\n  match: match$5,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\nvar formatDistanceLocale$4 = {\n  lessThanXSeconds: {\n    one: 'meno di un secondo',\n    other: 'meno di {{count}} secondi'\n  },\n  xSeconds: {\n    one: 'un secondo',\n    other: '{{count}} secondi'\n  },\n  halfAMinute: 'alcuni secondi',\n  lessThanXMinutes: {\n    one: 'meno di un minuto',\n    other: 'meno di {{count}} minuti'\n  },\n  xMinutes: {\n    one: 'un minuto',\n    other: '{{count}} minuti'\n  },\n  aboutXHours: {\n    one: \"circa un'ora\",\n    other: 'circa {{count}} ore'\n  },\n  xHours: {\n    one: \"un'ora\",\n    other: '{{count}} ore'\n  },\n  xDays: {\n    one: 'un giorno',\n    other: '{{count}} giorni'\n  },\n  aboutXWeeks: {\n    one: 'circa una settimana',\n    other: 'circa {{count}} settimane'\n  },\n  xWeeks: {\n    one: 'una settimana',\n    other: '{{count}} settimane'\n  },\n  aboutXMonths: {\n    one: 'circa un mese',\n    other: 'circa {{count}} mesi'\n  },\n  xMonths: {\n    one: 'un mese',\n    other: '{{count}} mesi'\n  },\n  aboutXYears: {\n    one: 'circa un anno',\n    other: 'circa {{count}} anni'\n  },\n  xYears: {\n    one: 'un anno',\n    other: '{{count}} anni'\n  },\n  overXYears: {\n    one: 'più di un anno',\n    other: 'più di {{count}} anni'\n  },\n  almostXYears: {\n    one: 'quasi un anno',\n    other: 'quasi {{count}} anni'\n  }\n};\nvar formatDistance$4 = function (token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale$4[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'tra ' + result;\n    } else {\n      return result + ' fa';\n    }\n  }\n  return result;\n};\nvar dateFormats$4 = {\n  full: 'EEEE d MMMM y',\n  long: 'd MMMM y',\n  medium: 'd MMM y',\n  short: 'dd/MM/y'\n};\nvar timeFormats$4 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$4 = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong$4 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$4,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$4,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$4,\n    defaultWidth: 'full'\n  })\n};\nvar weekdays = ['domenica', 'lunedì', 'martedì', 'mercoledì', 'giovedì', 'venerdì', 'sabato'];\nfunction lastWeek(day) {\n  switch (day) {\n    case 0:\n      return \"'domenica scorsa alle' p\";\n    default:\n      return \"'\" + weekdays[day] + \" scorso alle' p\";\n  }\n}\nfunction thisWeek(day) {\n  return \"'\" + weekdays[day] + \" alle' p\";\n}\nfunction nextWeek(day) {\n  switch (day) {\n    case 0:\n      return \"'domenica prossima alle' p\";\n    default:\n      return \"'\" + weekdays[day] + \" prossimo alle' p\";\n  }\n}\nvar formatRelativeLocale$4 = {\n  lastWeek: function (date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'ieri alle' p\",\n  today: \"'oggi alle' p\",\n  tomorrow: \"'domani alle' p\",\n  nextWeek: function (date, baseDate, options) {\n    var day = date.getUTCDay();\n    if (isSameUTCWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: 'P'\n};\nvar formatRelative$4 = function (token, date, baseDate, options) {\n  var format = formatRelativeLocale$4[token];\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n  return format;\n};\nvar eraValues$4 = {\n  narrow: ['aC', 'dC'],\n  abbreviated: ['a.C.', 'd.C.'],\n  wide: ['avanti Cristo', 'dopo Cristo']\n};\nvar quarterValues$4 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['T1', 'T2', 'T3', 'T4'],\n  wide: ['1º trimestre', '2º trimestre', '3º trimestre', '4º trimestre']\n};\nvar monthValues$4 = {\n  narrow: ['G', 'F', 'M', 'A', 'M', 'G', 'L', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['gen', 'feb', 'mar', 'apr', 'mag', 'giu', 'lug', 'ago', 'set', 'ott', 'nov', 'dic'],\n  wide: ['gennaio', 'febbraio', 'marzo', 'aprile', 'maggio', 'giugno', 'luglio', 'agosto', 'settembre', 'ottobre', 'novembre', 'dicembre']\n};\nvar dayValues$4 = {\n  narrow: ['D', 'L', 'M', 'M', 'G', 'V', 'S'],\n  short: ['dom', 'lun', 'mar', 'mer', 'gio', 'ven', 'sab'],\n  abbreviated: ['dom', 'lun', 'mar', 'mer', 'gio', 'ven', 'sab'],\n  wide: ['domenica', 'lunedì', 'martedì', 'mercoledì', 'giovedì', 'venerdì', 'sabato']\n};\nvar dayPeriodValues$4 = {\n  narrow: {\n    am: 'm.',\n    pm: 'p.',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'mattina',\n    afternoon: 'pomeriggio',\n    evening: 'sera',\n    night: 'notte'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'mattina',\n    afternoon: 'pomeriggio',\n    evening: 'sera',\n    night: 'notte'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'mattina',\n    afternoon: 'pomeriggio',\n    evening: 'sera',\n    night: 'notte'\n  }\n};\nvar formattingDayPeriodValues$3 = {\n  narrow: {\n    am: 'm.',\n    pm: 'p.',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'di mattina',\n    afternoon: 'del pomeriggio',\n    evening: 'di sera',\n    night: 'di notte'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'di mattina',\n    afternoon: 'del pomeriggio',\n    evening: 'di sera',\n    night: 'di notte'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mezzanotte',\n    noon: 'mezzogiorno',\n    morning: 'di mattina',\n    afternoon: 'del pomeriggio',\n    evening: 'di sera',\n    night: 'di notte'\n  }\n};\nvar ordinalNumber$4 = function (dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'º';\n};\nvar localize$4 = {\n  ordinalNumber: ordinalNumber$4,\n  era: buildLocalizeFn({\n    values: eraValues$4,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$4,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$4,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$4,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$4,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$3,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$4 = /^(\\d+)(º)?/i;\nvar parseOrdinalNumberPattern$4 = /\\d+/i;\nvar matchEraPatterns$4 = {\n  narrow: /^(aC|dC)/i,\n  abbreviated: /^(a\\.?\\s?C\\.?|a\\.?\\s?e\\.?\\s?v\\.?|d\\.?\\s?C\\.?|e\\.?\\s?v\\.?)/i,\n  wide: /^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i\n};\nvar parseEraPatterns$4 = {\n  any: [/^a/i, /^(d|e)/i]\n};\nvar matchQuarterPatterns$4 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^t[1234]/i,\n  wide: /^[1234](º)? trimestre/i\n};\nvar parseQuarterPatterns$4 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$4 = {\n  narrow: /^[gfmalsond]/i,\n  abbreviated: /^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,\n  wide: /^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i\n};\nvar parseMonthPatterns$4 = {\n  narrow: [/^g/i, /^f/i, /^m/i, /^a/i, /^m/i, /^g/i, /^l/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ge/i, /^f/i, /^mar/i, /^ap/i, /^mag/i, /^gi/i, /^l/i, /^ag/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns$4 = {\n  narrow: /^[dlmgvs]/i,\n  short: /^(do|lu|ma|me|gi|ve|sa)/i,\n  abbreviated: /^(dom|lun|mar|mer|gio|ven|sab)/i,\n  wide: /^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i\n};\nvar parseDayPatterns$4 = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^g/i, /^v/i, /^s/i],\n  any: [/^d/i, /^l/i, /^ma/i, /^me/i, /^g/i, /^v/i, /^s/i]\n};\nvar matchDayPeriodPatterns$4 = {\n  narrow: /^(a|m\\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,\n  any: /^([ap]\\.?\\s?m\\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i\n};\nvar parseDayPeriodPatterns$4 = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mezza/i,\n    noon: /^mezzo/i,\n    morning: /mattina/i,\n    afternoon: /pomeriggio/i,\n    evening: /sera/i,\n    night: /notte/i\n  }\n};\nvar match$4 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$4,\n    parsePattern: parseOrdinalNumberPattern$4,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$4,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$4,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$4,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$4,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$4,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$4,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$4,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$4,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$4,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$4,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Italian locale.\n * @language Italian\n * @iso-639-2 ita\n * <AUTHOR> Restifo [@albertorestifo]{@link https://github.com/albertorestifo}\n * <AUTHOR> Polimeni [@giofilo]{@link https://github.com/giofilo}\n * <AUTHOR> Carrese [@vin-car]{@link https://github.com/vin-car}\n */\n\nvar locale$4 = {\n  code: 'it',\n  formatDistance: formatDistance$4,\n  formatLong: formatLong$4,\n  formatRelative: formatRelative$4,\n  localize: localize$4,\n  match: match$4,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\nfunction declensionGroup(scheme, count) {\n  if (count === 1) {\n    return scheme.one;\n  }\n  var rem100 = count % 100; // ends with 11-20\n\n  if (rem100 <= 20 && rem100 > 10) {\n    return scheme.other;\n  }\n  var rem10 = rem100 % 10; // ends with 2, 3, 4\n\n  if (rem10 >= 2 && rem10 <= 4) {\n    return scheme.twoFour;\n  }\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  time = time || 'regular';\n  var group = declensionGroup(scheme, count);\n  var finalText = group[time] || group;\n  return finalText.replace('{{count}}', count);\n}\nvar formatDistanceLocale$3 = {\n  lessThanXSeconds: {\n    one: {\n      regular: 'mniej niż sekunda',\n      past: 'mniej niż sekundę',\n      future: 'mniej niż sekundę'\n    },\n    twoFour: 'mniej niż {{count}} sekundy',\n    other: 'mniej niż {{count}} sekund'\n  },\n  xSeconds: {\n    one: {\n      regular: 'sekunda',\n      past: 'sekundę',\n      future: 'sekundę'\n    },\n    twoFour: '{{count}} sekundy',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: {\n    one: 'pół minuty',\n    twoFour: 'pół minuty',\n    other: 'pół minuty'\n  },\n  lessThanXMinutes: {\n    one: {\n      regular: 'mniej niż minuta',\n      past: 'mniej niż minutę',\n      future: 'mniej niż minutę'\n    },\n    twoFour: 'mniej niż {{count}} minuty',\n    other: 'mniej niż {{count}} minut'\n  },\n  xMinutes: {\n    one: {\n      regular: 'minuta',\n      past: 'minutę',\n      future: 'minutę'\n    },\n    twoFour: '{{count}} minuty',\n    other: '{{count}} minut'\n  },\n  aboutXHours: {\n    one: {\n      regular: 'około godziny',\n      past: 'około godziny',\n      future: 'około godzinę'\n    },\n    twoFour: 'około {{count}} godziny',\n    other: 'około {{count}} godzin'\n  },\n  xHours: {\n    one: {\n      regular: 'godzina',\n      past: 'godzinę',\n      future: 'godzinę'\n    },\n    twoFour: '{{count}} godziny',\n    other: '{{count}} godzin'\n  },\n  xDays: {\n    one: {\n      regular: 'dzień',\n      past: 'dzień',\n      future: '1 dzień'\n    },\n    twoFour: '{{count}} dni',\n    other: '{{count}} dni'\n  },\n  aboutXWeeks: {\n    one: 'około tygodnia',\n    twoFour: 'około {{count}} tygodni',\n    other: 'około {{count}} tygodni'\n  },\n  xWeeks: {\n    one: 'tydzień',\n    twoFour: '{{count}} tygodnie',\n    other: '{{count}} tygodni'\n  },\n  aboutXMonths: {\n    one: 'około miesiąc',\n    twoFour: 'około {{count}} miesiące',\n    other: 'około {{count}} miesięcy'\n  },\n  xMonths: {\n    one: 'miesiąc',\n    twoFour: '{{count}} miesiące',\n    other: '{{count}} miesięcy'\n  },\n  aboutXYears: {\n    one: 'około rok',\n    twoFour: 'około {{count}} lata',\n    other: 'około {{count}} lat'\n  },\n  xYears: {\n    one: 'rok',\n    twoFour: '{{count}} lata',\n    other: '{{count}} lat'\n  },\n  overXYears: {\n    one: 'ponad rok',\n    twoFour: 'ponad {{count}} lata',\n    other: 'ponad {{count}} lat'\n  },\n  almostXYears: {\n    one: 'prawie rok',\n    twoFour: 'prawie {{count}} lata',\n    other: 'prawie {{count}} lat'\n  }\n};\nfunction formatDistance$3(token, count, options) {\n  options = options || {};\n  var scheme = formatDistanceLocale$3[token];\n  if (!options.addSuffix) {\n    return declension(scheme, count);\n  }\n  if (options.comparison > 0) {\n    return 'za ' + declension(scheme, count, 'future');\n  } else {\n    return declension(scheme, count, 'past') + ' temu';\n  }\n}\nvar dateFormats$3 = {\n  full: 'EEEE, do MMMM y',\n  long: 'do MMMM y',\n  medium: 'do MMM y',\n  short: 'dd.MM.y'\n};\nvar timeFormats$3 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$3 = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$3 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$3,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$3,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$3,\n    defaultWidth: 'full'\n  })\n};\nvar adjectivesLastWeek = {\n  masculine: 'ostatni',\n  feminine: 'ostatnia'\n};\nvar adjectivesThisWeek = {\n  masculine: 'ten',\n  feminine: 'ta'\n};\nvar adjectivesNextWeek = {\n  masculine: 'następny',\n  feminine: 'następna'\n};\nvar dayGrammaticalGender = {\n  0: 'feminine',\n  1: 'masculine',\n  2: 'masculine',\n  3: 'feminine',\n  4: 'masculine',\n  5: 'masculine',\n  6: 'feminine'\n};\nfunction getAdjectives(token, date, baseDate, options) {\n  if (isSameUTCWeek(date, baseDate, options)) {\n    return adjectivesThisWeek;\n  } else if (token === 'lastWeek') {\n    return adjectivesLastWeek;\n  } else if (token === 'nextWeek') {\n    return adjectivesNextWeek;\n  } else {\n    throw new Error(\"Cannot determine adjectives for token \".concat(token));\n  }\n}\nfunction getAdjective(token, date, baseDate, options) {\n  var day = date.getUTCDay();\n  var adjectives = getAdjectives(token, date, baseDate, options);\n  var grammaticalGender = dayGrammaticalGender[day];\n  return adjectives[grammaticalGender];\n}\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n  var adjective = getAdjective(token, date, baseDate, options);\n  return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nvar formatRelativeLocale$3 = {\n  lastWeek: dayAndTimeWithAdjective,\n  yesterday: \"'wczoraj o' p\",\n  today: \"'dzisiaj o' p\",\n  tomorrow: \"'jutro o' p\",\n  nextWeek: dayAndTimeWithAdjective,\n  other: 'P'\n};\nfunction formatRelative$3(token, date, baseDate, options) {\n  var format = formatRelativeLocale$3[token];\n  if (typeof format === 'function') {\n    return format(token, date, baseDate, options);\n  }\n  return format;\n}\nfunction ordinalNumber$3(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return String(number);\n}\nvar eraValues$3 = {\n  narrow: ['p.n.e.', 'n.e.'],\n  abbreviated: ['p.n.e.', 'n.e.'],\n  wide: ['przed naszą erą', 'naszej ery']\n};\nvar quarterValues$3 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['I kw.', 'II kw.', 'III kw.', 'IV kw.'],\n  wide: ['I kwartał', 'II kwartał', 'III kwartał', 'IV kwartał']\n};\nvar monthValues$3 = {\n  narrow: ['S', 'L', 'M', 'K', 'M', 'C', 'L', 'S', 'W', 'P', 'L', 'G'],\n  abbreviated: ['sty', 'lut', 'mar', 'kwi', 'maj', 'cze', 'lip', 'sie', 'wrz', 'paź', 'lis', 'gru'],\n  wide: ['styczeń', 'luty', 'marzec', 'kwiecień', 'maj', 'czerwiec', 'lipiec', 'sierpień', 'wrzesień', 'październik', 'listopad', 'grudzień']\n};\nvar monthFormattingValues = {\n  narrow: ['s', 'l', 'm', 'k', 'm', 'c', 'l', 's', 'w', 'p', 'l', 'g'],\n  abbreviated: ['sty', 'lut', 'mar', 'kwi', 'maj', 'cze', 'lip', 'sie', 'wrz', 'paź', 'lis', 'gru'],\n  wide: ['stycznia', 'lutego', 'marca', 'kwietnia', 'maja', 'czerwca', 'lipca', 'sierpnia', 'września', 'października', 'listopada', 'grudnia']\n};\nvar dayValues$3 = {\n  narrow: ['N', 'P', 'W', 'Ś', 'C', 'P', 'S'],\n  short: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],\n  abbreviated: ['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],\n  wide: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota']\n};\nvar dayFormattingValues = {\n  narrow: ['n', 'p', 'w', 'ś', 'c', 'p', 's'],\n  short: ['nie', 'pon', 'wto', 'śro', 'czw', 'pią', 'sob'],\n  abbreviated: ['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],\n  wide: ['niedziela', 'poniedziałek', 'wtorek', 'środa', 'czwartek', 'piątek', 'sobota']\n};\nvar dayPeriodValues$3 = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'półn.',\n    noon: 'poł',\n    morning: 'rano',\n    afternoon: 'popoł.',\n    evening: 'wiecz.',\n    night: 'noc'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'północ',\n    noon: 'południe',\n    morning: 'rano',\n    afternoon: 'popołudnie',\n    evening: 'wieczór',\n    night: 'noc'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'północ',\n    noon: 'południe',\n    morning: 'rano',\n    afternoon: 'popołudnie',\n    evening: 'wieczór',\n    night: 'noc'\n  }\n};\nvar dayPeriodFormattingValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'o półn.',\n    noon: 'w poł.',\n    morning: 'rano',\n    afternoon: 'po poł.',\n    evening: 'wiecz.',\n    night: 'w nocy'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'o północy',\n    noon: 'w południe',\n    morning: 'rano',\n    afternoon: 'po południu',\n    evening: 'wieczorem',\n    night: 'w nocy'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'o północy',\n    noon: 'w południe',\n    morning: 'rano',\n    afternoon: 'po południu',\n    evening: 'wieczorem',\n    night: 'w nocy'\n  }\n};\nvar localize$3 = {\n  ordinalNumber: ordinalNumber$3,\n  era: buildLocalizeFn({\n    values: eraValues$3,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$3,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$3,\n    defaultWidth: 'wide',\n    formattingValues: monthFormattingValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$3,\n    defaultWidth: 'wide',\n    formattingValues: dayFormattingValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$3,\n    defaultWidth: 'wide',\n    formattingValues: dayPeriodFormattingValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$3 = /^(\\d+)?/i;\nvar parseOrdinalNumberPattern$3 = /\\d+/i;\nvar matchEraPatterns$3 = {\n  narrow: /^(p\\.?\\s*n\\.?\\s*e\\.?\\s*|n\\.?\\s*e\\.?\\s*)/i,\n  abbreviated: /^(p\\.?\\s*n\\.?\\s*e\\.?\\s*|n\\.?\\s*e\\.?\\s*)/i,\n  wide: /^(przed\\s*nasz(ą|a)\\s*er(ą|a)|naszej\\s*ery)/i\n};\nvar parseEraPatterns$3 = {\n  any: [/^p/i, /^n/i]\n};\nvar matchQuarterPatterns$3 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^(I|II|III|IV)\\s*kw\\.?/i,\n  wide: /^(I|II|III|IV)\\s*kwarta(ł|l)/i\n};\nvar parseQuarterPatterns$3 = {\n  narrow: [/1/i, /2/i, /3/i, /4/i],\n  any: [/^I kw/i, /^II kw/i, /^III kw/i, /^IV kw/i]\n};\nvar matchMonthPatterns$3 = {\n  narrow: /^[slmkcwpg]/i,\n  abbreviated: /^(sty|lut|mar|kwi|maj|cze|lip|sie|wrz|pa(ź|z)|lis|gru)/i,\n  wide: /^(stycznia|stycze(ń|n)|lutego|luty|marca|marzec|kwietnia|kwiecie(ń|n)|maja|maj|czerwca|czerwiec|lipca|lipiec|sierpnia|sierpie(ń|n)|wrze(ś|s)nia|wrzesie(ń|n)|pa(ź|z)dziernika|pa(ź|z)dziernik|listopada|listopad|grudnia|grudzie(ń|n))/i\n};\nvar parseMonthPatterns$3 = {\n  narrow: [/^s/i, /^l/i, /^m/i, /^k/i, /^m/i, /^c/i, /^l/i, /^s/i, /^w/i, /^p/i, /^l/i, /^g/i],\n  any: [/^st/i, /^lu/i, /^mar/i, /^k/i, /^maj/i, /^c/i, /^lip/i, /^si/i, /^w/i, /^p/i, /^lis/i, /^g/i]\n};\nvar matchDayPatterns$3 = {\n  narrow: /^[npwścs]/i,\n  short: /^(nie|pon|wto|(ś|s)ro|czw|pi(ą|a)|sob)/i,\n  abbreviated: /^(niedz|pon|wt|(ś|s)r|czw|pt|sob)\\.?/i,\n  wide: /^(niedziela|poniedzia(ł|l)ek|wtorek|(ś|s)roda|czwartek|pi(ą|a)tek|sobota)/i\n};\nvar parseDayPatterns$3 = {\n  narrow: [/^n/i, /^p/i, /^w/i, /^ś/i, /^c/i, /^p/i, /^s/i],\n  abbreviated: [/^n/i, /^po/i, /^w/i, /^(ś|s)r/i, /^c/i, /^pt/i, /^so/i],\n  any: [/^n/i, /^po/i, /^w/i, /^(ś|s)r/i, /^c/i, /^pi/i, /^so/i]\n};\nvar matchDayPeriodPatterns$3 = {\n  narrow: /^(^a$|^p$|pó(ł|l)n\\.?|o\\s*pó(ł|l)n\\.?|po(ł|l)\\.?|w\\s*po(ł|l)\\.?|po\\s*po(ł|l)\\.?|rano|wiecz\\.?|noc|w\\s*nocy)/i,\n  any: /^(am|pm|pó(ł|l)noc|o\\s*pó(ł|l)nocy|po(ł|l)udnie|w\\s*po(ł|l)udnie|popo(ł|l)udnie|po\\s*po(ł|l)udniu|rano|wieczór|wieczorem|noc|w\\s*nocy)/i\n};\nvar parseDayPeriodPatterns$3 = {\n  narrow: {\n    am: /^a$/i,\n    pm: /^p$/i,\n    midnight: /pó(ł|l)n/i,\n    noon: /po(ł|l)/i,\n    morning: /rano/i,\n    afternoon: /po\\s*po(ł|l)/i,\n    evening: /wiecz/i,\n    night: /noc/i\n  },\n  any: {\n    am: /^am/i,\n    pm: /^pm/i,\n    midnight: /pó(ł|l)n/i,\n    noon: /po(ł|l)/i,\n    morning: /rano/i,\n    afternoon: /po\\s*po(ł|l)/i,\n    evening: /wiecz/i,\n    night: /noc/i\n  }\n};\nvar match$3 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$3,\n    parsePattern: parseOrdinalNumberPattern$3,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$3,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$3,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$3,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$3,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$3,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$3,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$3,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$3,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$3,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$3,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Polish locale.\n * @language Polish\n * @iso-639-2 pol\n * <AUTHOR> Derks [@ertrzyiks]{@link https://github.com/ertrzyiks}\n * <AUTHOR> RAG [@justrag]{@link https://github.com/justrag}\n * <AUTHOR> Grzyb [@mikolajgrzyb]{@link https://github.com/mikolajgrzyb}\n * <AUTHOR> Tokarski [@mutisz]{@link https://github.com/mutisz}\n */\n\nvar locale$3 = {\n  code: 'pl',\n  formatDistance: formatDistance$3,\n  formatLong: formatLong$3,\n  formatRelative: formatRelative$3,\n  localize: localize$3,\n  match: match$3,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\nvar formatDistanceLocale$2 = {\n  lessThanXSeconds: {\n    one: 'menos de um segundo',\n    other: 'menos de {{count}} segundos'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundos'\n  },\n  halfAMinute: 'meio minuto',\n  lessThanXMinutes: {\n    one: 'menos de um minuto',\n    other: 'menos de {{count}} minutos'\n  },\n  xMinutes: {\n    one: '1 minuto',\n    other: '{{count}} minutos'\n  },\n  aboutXHours: {\n    one: 'aproximadamente 1 hora',\n    other: 'aproximadamente {{count}} horas'\n  },\n  xHours: {\n    one: '1 hora',\n    other: '{{count}} horas'\n  },\n  xDays: {\n    one: '1 dia',\n    other: '{{count}} dias'\n  },\n  aboutXWeeks: {\n    one: 'aproximadamente 1 semana',\n    other: 'aproximadamente {{count}} semanas'\n  },\n  xWeeks: {\n    one: '1 semana',\n    other: '{{count}} semanas'\n  },\n  aboutXMonths: {\n    one: 'aproximadamente 1 mês',\n    other: 'aproximadamente {{count}} meses'\n  },\n  xMonths: {\n    one: '1 mês',\n    other: '{{count}} meses'\n  },\n  aboutXYears: {\n    one: 'aproximadamente 1 ano',\n    other: 'aproximadamente {{count}} anos'\n  },\n  xYears: {\n    one: '1 ano',\n    other: '{{count}} anos'\n  },\n  overXYears: {\n    one: 'mais de 1 ano',\n    other: 'mais de {{count}} anos'\n  },\n  almostXYears: {\n    one: 'quase 1 ano',\n    other: 'quase {{count}} anos'\n  }\n};\nfunction formatDistance$2(token, count, options) {\n  options = options || {};\n  var result;\n  if (typeof formatDistanceLocale$2[token] === 'string') {\n    result = formatDistanceLocale$2[token];\n  } else if (count === 1) {\n    result = formatDistanceLocale$2[token].one;\n  } else {\n    result = formatDistanceLocale$2[token].other.replace('{{count}}', count);\n  }\n  if (options.addSuffix) {\n    if (options.comparison > 0) {\n      return 'daqui a ' + result;\n    } else {\n      return 'há ' + result;\n    }\n  }\n  return result;\n}\nvar dateFormats$2 = {\n  full: \"EEEE, d 'de' MMMM 'de' y\",\n  long: \"d 'de' MMMM 'de' y\",\n  medium: \"d 'de' MMM 'de' y\",\n  short: 'dd/MM/y'\n};\nvar timeFormats$2 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$2 = {\n  full: \"{{date}} 'às' {{time}}\",\n  long: \"{{date}} 'às' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$2 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$2,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$2,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$2,\n    defaultWidth: 'full'\n  })\n};\nvar formatRelativeLocale$2 = {\n  lastWeek: \"'na última' eeee 'às' p\",\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: 'P'\n};\nfunction formatRelative$2(token, _date, _baseDate, _options) {\n  return formatRelativeLocale$2[token];\n}\nfunction ordinalNumber$2(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  return number + 'º';\n}\nvar eraValues$2 = {\n  narrow: ['aC', 'dC'],\n  abbreviated: ['a.C.', 'd.C.'],\n  wide: ['antes de Cristo', 'depois de Cristo']\n};\nvar quarterValues$2 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['T1', 'T2', 'T3', 'T4'],\n  wide: ['1º trimestre', '2º trimestre', '3º trimestre', '4º trimestre']\n};\nvar monthValues$2 = {\n  narrow: ['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n  abbreviated: ['jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set', 'out', 'nov', 'dez'],\n  wide: ['janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho', 'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro']\n};\nvar dayValues$2 = {\n  narrow: ['d', 's', 't', 'q', 'q', 's', 's'],\n  short: ['dom', 'seg', 'ter', 'qua', 'qui', 'sex', 'sáb'],\n  abbreviated: ['dom', 'seg', 'ter', 'qua', 'qui', 'sex', 'sáb'],\n  wide: ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado']\n};\nvar dayPeriodValues$2 = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'meia-noite',\n    noon: 'meio-dia',\n    morning: 'manhã',\n    afternoon: 'tarde',\n    evening: 'noite',\n    night: 'madrugada'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'meia-noite',\n    noon: 'meio-dia',\n    morning: 'manhã',\n    afternoon: 'tarde',\n    evening: 'noite',\n    night: 'madrugada'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'meia-noite',\n    noon: 'meio-dia',\n    morning: 'manhã',\n    afternoon: 'tarde',\n    evening: 'noite',\n    night: 'madrugada'\n  }\n};\nvar formattingDayPeriodValues$2 = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'meia-noite',\n    noon: 'meio-dia',\n    morning: 'da manhã',\n    afternoon: 'da tarde',\n    evening: 'da noite',\n    night: 'da madrugada'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'meia-noite',\n    noon: 'meio-dia',\n    morning: 'da manhã',\n    afternoon: 'da tarde',\n    evening: 'da noite',\n    night: 'da madrugada'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'meia-noite',\n    noon: 'meio-dia',\n    morning: 'da manhã',\n    afternoon: 'da tarde',\n    evening: 'da noite',\n    night: 'da madrugada'\n  }\n};\nvar localize$2 = {\n  ordinalNumber: ordinalNumber$2,\n  era: buildLocalizeFn({\n    values: eraValues$2,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$2,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$2,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$2,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$2,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$2,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$2 = /^(\\d+)(º|ª)?/i;\nvar parseOrdinalNumberPattern$2 = /\\d+/i;\nvar matchEraPatterns$2 = {\n  narrow: /^(ac|dc|a|d)/i,\n  abbreviated: /^(a\\.?\\s?c\\.?|a\\.?\\s?e\\.?\\s?c\\.?|d\\.?\\s?c\\.?|e\\.?\\s?c\\.?)/i,\n  wide: /^(antes de cristo|antes da era comum|depois de cristo|era comum)/i\n};\nvar parseEraPatterns$2 = {\n  any: [/^ac/i, /^dc/i],\n  wide: [/^(antes de cristo|antes da era comum)/i, /^(depois de cristo|era comum)/i]\n};\nvar matchQuarterPatterns$2 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^T[1234]/i,\n  wide: /^[1234](º|ª)? trimestre/i\n};\nvar parseQuarterPatterns$2 = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns$2 = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,\n  wide: /^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i\n};\nvar parseMonthPatterns$2 = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^ab/i, /^mai/i, /^jun/i, /^jul/i, /^ag/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns$2 = {\n  narrow: /^[dstq]/i,\n  short: /^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,\n  abbreviated: /^(dom|seg|ter|qua|qui|sex|s[áa]b)/i,\n  wide: /^(domingo|segunda-?\\s?feira|terça-?\\s?feira|quarta-?\\s?feira|quinta-?\\s?feira|sexta-?\\s?feira|s[áa]bado)/i\n};\nvar parseDayPatterns$2 = {\n  narrow: [/^d/i, /^s/i, /^t/i, /^q/i, /^q/i, /^s/i, /^s/i],\n  any: [/^d/i, /^seg/i, /^t/i, /^qua/i, /^qui/i, /^sex/i, /^s[áa]/i]\n};\nvar matchDayPeriodPatterns$2 = {\n  narrow: /^(a|p|meia-?\\s?noite|meio-?\\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i,\n  any: /^([ap]\\.?\\s?m\\.?|meia-?\\s?noite|meio-?\\s?dia|(da) (manh[ãa]|tarde|noite|madrugada))/i\n};\nvar parseDayPeriodPatterns$2 = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^meia/i,\n    noon: /^meio/i,\n    morning: /manh[ãa]/i,\n    afternoon: /tarde/i,\n    evening: /noite/i,\n    night: /madrugada/i\n  }\n};\nvar match$2 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$2,\n    parsePattern: parseOrdinalNumberPattern$2,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$2,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$2,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$2,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$2,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$2,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$2,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$2,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$2,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$2,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$2,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Portuguese locale.\n * @language Portuguese\n * @iso-639-2 por\n * <AUTHOR> Freire [@dfreire]{@link https://github.com/dfreire}\n * <AUTHOR> de la Rosa [@adrm]{@link https://github.com/adrm}\n */\n\nvar locale$2 = {\n  code: 'pt',\n  formatDistance: formatDistance$2,\n  formatLong: formatLong$2,\n  formatRelative: formatRelative$2,\n  localize: localize$2,\n  match: match$2,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\nvar formatDistanceLocale$1 = {\n  lessThanXSeconds: {\n    one: 'bir saniyeden az',\n    other: '{{count}} saniyeden az'\n  },\n  xSeconds: {\n    one: '1 saniye',\n    other: '{{count}} saniye'\n  },\n  halfAMinute: 'yarım dakika',\n  lessThanXMinutes: {\n    one: 'bir dakikadan az',\n    other: '{{count}} dakikadan az'\n  },\n  xMinutes: {\n    one: '1 dakika',\n    other: '{{count}} dakika'\n  },\n  aboutXHours: {\n    one: 'yaklaşık 1 saat',\n    other: 'yaklaşık {{count}} saat'\n  },\n  xHours: {\n    one: '1 saat',\n    other: '{{count}} saat'\n  },\n  xDays: {\n    one: '1 gün',\n    other: '{{count}} gün'\n  },\n  aboutXWeeks: {\n    one: 'yaklaşık 1 hafta',\n    other: 'yaklaşık {{count}} hafta'\n  },\n  xWeeks: {\n    one: '1 hafta',\n    other: '{{count}} hafta'\n  },\n  aboutXMonths: {\n    one: 'yaklaşık 1 ay',\n    other: 'yaklaşık {{count}} ay'\n  },\n  xMonths: {\n    one: '1 ay',\n    other: '{{count}} ay'\n  },\n  aboutXYears: {\n    one: 'yaklaşık 1 yıl',\n    other: 'yaklaşık {{count}} yıl'\n  },\n  xYears: {\n    one: '1 yıl',\n    other: '{{count}} yıl'\n  },\n  overXYears: {\n    one: '1 yıldan fazla',\n    other: '{{count}} yıldan fazla'\n  },\n  almostXYears: {\n    one: 'neredeyse 1 yıl',\n    other: 'neredeyse {{count}} yıl'\n  }\n};\nvar formatDistance$1 = function (token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale$1[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' sonra';\n    } else {\n      return result + ' önce';\n    }\n  }\n  return result;\n};\nvar dateFormats$1 = {\n  full: 'd MMMM y EEEE',\n  long: 'd MMMM y',\n  medium: 'd MMM y',\n  short: 'dd.MM.yyyy'\n};\nvar timeFormats$1 = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats$1 = {\n  full: \"{{date}} 'saat' {{time}}\",\n  long: \"{{date}} 'saat' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong$1 = {\n  date: buildFormatLongFn({\n    formats: dateFormats$1,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats$1,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats$1,\n    defaultWidth: 'full'\n  })\n};\nvar formatRelativeLocale$1 = {\n  lastWeek: \"'geçen hafta' eeee 'saat' p\",\n  yesterday: \"'dün saat' p\",\n  today: \"'bugün saat' p\",\n  tomorrow: \"'yarın saat' p\",\n  nextWeek: \"eeee 'saat' p\",\n  other: 'P'\n};\nvar formatRelative$1 = function (token, _date, _baseDate, _options) {\n  return formatRelativeLocale$1[token];\n};\nvar eraValues$1 = {\n  narrow: ['MÖ', 'MS'],\n  abbreviated: ['MÖ', 'MS'],\n  wide: ['Milattan Önce', 'Milattan Sonra']\n};\nvar quarterValues$1 = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1Ç', '2Ç', '3Ç', '4Ç'],\n  wide: ['İlk çeyrek', 'İkinci Çeyrek', 'Üçüncü çeyrek', 'Son çeyrek']\n};\nvar monthValues$1 = {\n  narrow: ['O', 'Ş', 'M', 'N', 'M', 'H', 'T', 'A', 'E', 'E', 'K', 'A'],\n  abbreviated: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'],\n  wide: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran', 'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık']\n};\nvar dayValues$1 = {\n  narrow: ['P', 'P', 'S', 'Ç', 'P', 'C', 'C'],\n  short: ['Pz', 'Pt', 'Sa', 'Ça', 'Pe', 'Cu', 'Ct'],\n  abbreviated: ['Paz', 'Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cts'],\n  wide: ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi']\n};\nvar dayPeriodValues$1 = {\n  narrow: {\n    am: 'öö',\n    pm: 'ös',\n    midnight: 'gy',\n    noon: 'ö',\n    morning: 'sa',\n    afternoon: 'ös',\n    evening: 'ak',\n    night: 'ge'\n  },\n  abbreviated: {\n    am: 'ÖÖ',\n    pm: 'ÖS',\n    midnight: 'gece yarısı',\n    noon: 'öğle',\n    morning: 'sabah',\n    afternoon: 'öğleden sonra',\n    evening: 'akşam',\n    night: 'gece'\n  },\n  wide: {\n    am: 'Ö.Ö.',\n    pm: 'Ö.S.',\n    midnight: 'gece yarısı',\n    noon: 'öğle',\n    morning: 'sabah',\n    afternoon: 'öğleden sonra',\n    evening: 'akşam',\n    night: 'gece'\n  }\n};\nvar formattingDayPeriodValues$1 = {\n  narrow: {\n    am: 'öö',\n    pm: 'ös',\n    midnight: 'gy',\n    noon: 'ö',\n    morning: 'sa',\n    afternoon: 'ös',\n    evening: 'ak',\n    night: 'ge'\n  },\n  abbreviated: {\n    am: 'ÖÖ',\n    pm: 'ÖS',\n    midnight: 'gece yarısı',\n    noon: 'öğlen',\n    morning: 'sabahleyin',\n    afternoon: 'öğleden sonra',\n    evening: 'akşamleyin',\n    night: 'geceleyin'\n  },\n  wide: {\n    am: 'ö.ö.',\n    pm: 'ö.s.',\n    midnight: 'gece yarısı',\n    noon: 'öğlen',\n    morning: 'sabahleyin',\n    afternoon: 'öğleden sonra',\n    evening: 'akşamleyin',\n    night: 'geceleyin'\n  }\n};\nvar ordinalNumber$1 = function (dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize$1 = {\n  ordinalNumber: ordinalNumber$1,\n  era: buildLocalizeFn({\n    values: eraValues$1,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues$1,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues$1,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues$1,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues$1,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues$1,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern$1 = /^(\\d+)(\\.)?/i;\nvar parseOrdinalNumberPattern$1 = /\\d+/i;\nvar matchEraPatterns$1 = {\n  narrow: /^(mö|ms)/i,\n  abbreviated: /^(mö|ms)/i,\n  wide: /^(milattan önce|milattan sonra)/i\n};\nvar parseEraPatterns$1 = {\n  any: [/(^mö|^milattan önce)/i, /(^ms|^milattan sonra)/i]\n};\nvar matchQuarterPatterns$1 = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234]ç/i,\n  wide: /^((i|İ)lk|(i|İ)kinci|üçüncü|son) çeyrek/i\n};\nvar parseQuarterPatterns$1 = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n  abbreviated: [/1ç/i, /2ç/i, /3ç/i, /4ç/i],\n  wide: [/^(i|İ)lk çeyrek/i, /(i|İ)kinci çeyrek/i, /üçüncü çeyrek/i, /son çeyrek/i]\n};\nvar matchMonthPatterns$1 = {\n  narrow: /^[oşmnhtaek]/i,\n  abbreviated: /^(oca|şub|mar|nis|may|haz|tem|ağu|eyl|eki|kas|ara)/i,\n  wide: /^(ocak|şubat|mart|nisan|mayıs|haziran|temmuz|ağustos|eylül|ekim|kasım|aralık)/i\n};\nvar parseMonthPatterns$1 = {\n  narrow: [/^o/i, /^ş/i, /^m/i, /^n/i, /^m/i, /^h/i, /^t/i, /^a/i, /^e/i, /^e/i, /^k/i, /^a/i],\n  any: [/^o/i, /^ş/i, /^mar/i, /^n/i, /^may/i, /^h/i, /^t/i, /^ağ/i, /^ey/i, /^ek/i, /^k/i, /^ar/i]\n};\nvar matchDayPatterns$1 = {\n  narrow: /^[psçc]/i,\n  short: /^(pz|pt|sa|ça|pe|cu|ct)/i,\n  abbreviated: /^(paz|pzt|sal|çar|per|cum|cts)/i,\n  wide: /^(pazar|pazartesi|salı|çarşamba|perşembe|cuma|cumartesi)/i\n};\nvar parseDayPatterns$1 = {\n  narrow: [/^p/i, /^p/i, /^s/i, /^ç/i, /^p/i, /^c/i, /^c/i],\n  any: [/^pz/i, /^pt/i, /^sa/i, /^ça/i, /^pe/i, /^cu/i, /^ct/i],\n  wide: [/^pazar/i, /^pazartesi/i, /^salı/i, /^çarşamba/i, /^perşembe/i, /^cuma/i, /cumartesi/i]\n};\nvar matchDayPeriodPatterns$1 = {\n  narrow: /^(öö|ös|gy|ö|sa|ös|ak|ge)/i,\n  any: /^(ö\\.?\\s?[ös]\\.?|öğleden sonra|gece yarısı|öğle|(sabah|öğ|akşam|gece)(leyin))/i\n};\nvar parseDayPeriodPatterns$1 = {\n  any: {\n    am: /^ö\\.?ö\\.?/i,\n    pm: /^ö\\.?s\\.?/i,\n    midnight: /^(gy|gece yarısı)/i,\n    noon: /^öğ/i,\n    morning: /^sa/i,\n    afternoon: /^öğleden sonra/i,\n    evening: /^ak/i,\n    night: /^ge/i\n  }\n};\nvar match$1 = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern$1,\n    parsePattern: parseOrdinalNumberPattern$1,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns$1,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns$1,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns$1,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns$1,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns$1,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns$1,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns$1,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns$1,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns$1,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns$1,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Turkish locale.\n * @language Turkish\n * @iso-639-2 tur\n * <AUTHOR> Aydın [@alpcanaydin]{@link https://github.com/alpcanaydin}\n * <AUTHOR> Sargın [@berkaey]{@link https://github.com/berkaey}\n * <AUTHOR> Bulut [@bulutfatih]{@link https://github.com/bulutfatih}\n * <AUTHOR> Demirbilek [@dbtek]{@link https://github.com/dbtek}\n * <AUTHOR> Kayar [@ikayar]{@link https://github.com/ikayar}\n *\n *\n */\n\nvar locale$1 = {\n  code: 'tr',\n  formatDistance: formatDistance$1,\n  formatLong: formatLong$1,\n  formatRelative: formatRelative$1,\n  localize: localize$1,\n  match: match$1,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 1\n  }\n};\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '不到 1 秒',\n    other: '不到 {{count}} 秒'\n  },\n  xSeconds: {\n    one: '1 秒',\n    other: '{{count}} 秒'\n  },\n  halfAMinute: '半分钟',\n  lessThanXMinutes: {\n    one: '不到 1 分钟',\n    other: '不到 {{count}} 分钟'\n  },\n  xMinutes: {\n    one: '1 分钟',\n    other: '{{count}} 分钟'\n  },\n  xHours: {\n    one: '1 小时',\n    other: '{{count}} 小时'\n  },\n  aboutXHours: {\n    one: '大约 1 小时',\n    other: '大约 {{count}} 小时'\n  },\n  xDays: {\n    one: '1 天',\n    other: '{{count}} 天'\n  },\n  aboutXWeeks: {\n    one: '大约 1 个星期',\n    other: '大约 {{count}} 个星期'\n  },\n  xWeeks: {\n    one: '1 个星期',\n    other: '{{count}} 个星期'\n  },\n  aboutXMonths: {\n    one: '大约 1 个月',\n    other: '大约 {{count}} 个月'\n  },\n  xMonths: {\n    one: '1 个月',\n    other: '{{count}} 个月'\n  },\n  aboutXYears: {\n    one: '大约 1 年',\n    other: '大约 {{count}} 年'\n  },\n  xYears: {\n    one: '1 年',\n    other: '{{count}} 年'\n  },\n  overXYears: {\n    one: '超过 1 年',\n    other: '超过 {{count}} 年'\n  },\n  almostXYears: {\n    one: '将近 1 年',\n    other: '将近 {{count}} 年'\n  }\n};\nfunction formatDistance(token, count, options) {\n  options = options || {};\n  var result;\n  if (typeof formatDistanceLocale[token] === 'string') {\n    result = formatDistanceLocale[token];\n  } else if (count === 1) {\n    result = formatDistanceLocale[token].one;\n  } else {\n    result = formatDistanceLocale[token].other.replace('{{count}}', count);\n  }\n  if (options.addSuffix) {\n    if (options.comparison > 0) {\n      return result + '内';\n    } else {\n      return result + '前';\n    }\n  }\n  return result;\n}\nvar dateFormats = {\n  full: \"y'年'M'月'd'日' EEEE\",\n  long: \"y'年'M'月'd'日'\",\n  medium: 'yyyy-MM-dd',\n  short: 'yy-MM-dd'\n};\nvar timeFormats = {\n  full: 'zzzz a h:mm:ss',\n  long: 'z a h:mm:ss',\n  medium: 'a h:mm:ss',\n  short: 'a h:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nfunction checkWeek(_date, _baseDate, _options, baseFormat) {\n  if (isSameUTCWeek(_date, _baseDate, _options)) {\n    return baseFormat; // in same week\n  } else if (_date.getTime() > _baseDate.getTime()) {\n    return \"'下个'\" + baseFormat; // in next week\n  }\n  return \"'上个'\" + baseFormat; // in last week\n}\nvar formatRelativeLocale = {\n  lastWeek: checkWeek,\n  // days before yesterday, maybe in this week or last week\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: checkWeek,\n  // days after tomorrow, maybe in this week or next week\n  other: 'PP p'\n};\nfunction formatRelative(token, _date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(_date, _baseDate, _options, 'eeee p');\n  }\n  return format;\n}\nvar eraValues = {\n  narrow: ['前', '公元'],\n  abbreviated: ['前', '公元'],\n  wide: ['公元前', '公元']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['第一季', '第二季', '第三季', '第四季'],\n  wide: ['第一季度', '第二季度', '第三季度', '第四季度']\n};\nvar monthValues = {\n  narrow: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'],\n  abbreviated: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n  wide: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']\n};\nvar dayValues = {\n  narrow: ['日', '一', '二', '三', '四', '五', '六'],\n  short: ['日', '一', '二', '三', '四', '五', '六'],\n  abbreviated: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\n  wide: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '凌晨',\n    noon: '午',\n    morning: '早',\n    afternoon: '下午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜间'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜间'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '凌晨',\n    noon: '午',\n    morning: '早',\n    afternoon: '下午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜间'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜间'\n  }\n};\nfunction ordinalNumber(dirtyNumber, dirtyOptions) {\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`:\n  //\n  //   var options = dirtyOptions || {}\n  //   var unit = String(options.unit)\n  //\n  // where `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'\n  var number = Number(dirtyNumber);\n  var options = dirtyOptions || {};\n  var unit = String(options.unit);\n  switch (unit) {\n    case 'date':\n      return number.toString() + '日';\n    case 'hour':\n      return number.toString() + '时';\n    case 'minute':\n      return number.toString() + '分';\n    case 'second':\n      return number.toString() + '秒';\n    default:\n      return '第 ' + number.toString();\n  }\n}\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar matchOrdinalNumberPattern = /^(第\\s*)?\\d+(日|时|分|秒)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(前)/i,\n  abbreviated: /^(前)/i,\n  wide: /^(公元前|公元)/i\n};\nvar parseEraPatterns = {\n  any: [/^(前)/i, /^(公元)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^第[一二三四]刻/i,\n  wide: /^第[一二三四]刻钟/i\n};\nvar parseQuarterPatterns = {\n  any: [/(1|一)/i, /(2|二)/i, /(3|三)/i, /(4|四)/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(一|二|三|四|五|六|七|八|九|十[二一])/i,\n  abbreviated: /^(一|二|三|四|五|六|七|八|九|十[二一]|\\d|1[12])月/i,\n  wide: /^(一|二|三|四|五|六|七|八|九|十[二一])月/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^一/i, /^二/i, /^三/i, /^四/i, /^五/i, /^六/i, /^七/i, /^八/i, /^九/i, /^十(?!(一|二))/i, /^十一/i, /^十二/i],\n  any: [/^一|1/i, /^二|2/i, /^三|3/i, /^四|4/i, /^五|5/i, /^六|6/i, /^七|7/i, /^八|8/i, /^九|9/i, /^十(?!(一|二))|10/i, /^十一|11/i, /^十二|12/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[一二三四五六日]/i,\n  short: /^[一二三四五六日]/i,\n  abbreviated: /^周[一二三四五六日]/i,\n  wide: /^星期[一二三四五六日]/i\n};\nvar parseDayPatterns = {\n  any: [/日/i, /一/i, /二/i, /三/i, /四/i, /五/i, /六/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^上午?/i,\n    pm: /^下午?/i,\n    midnight: /^午夜/i,\n    noon: /^[中正]午/i,\n    morning: /^早上/i,\n    afternoon: /^下午/i,\n    evening: /^晚上?/i,\n    night: /^凌晨/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function (value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function (index) {\n      return index + 1;\n    }\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Chinese Simplified locale.\n * @language Chinese Simplified\n * @iso-639-2 zho\n * <AUTHOR> Geng [@KingMario]{@link https://github.com/KingMario}\n * <AUTHOR> Shuoyun [@fnlctrl]{@link https://github.com/fnlctrl}\n * <AUTHOR> [@sabrinamiao]{@link https://github.com/sabrinamiao}\n * <AUTHOR> Wu [@cubicwork]{@link https://github.com/cubicwork}\n * <AUTHOR> Lam [@skyuplam]{@link https://github.com/skyuplam}\n */\n\nvar locale = {\n  code: 'zh-CN',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1\n    /* Monday */,\n\n    firstWeekContainsDate: 4\n  }\n};\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nconst DEFAULT_LOCALE = {\n  language: 'en',\n  dateFormat: 'MM/dd/y',\n  timeFormat: 'H:mm a'\n};\nconst LOCALE_MAP = {\n  de: locale$7,\n  cs: locale$8,\n  en: locale$9,\n  fr: locale$5,\n  tr: locale$1,\n  it: locale$4,\n  pl: locale$3,\n  pt: locale$2,\n  es: locale$6,\n  zh: locale\n};\nconst localization = {\n  showFooter: {\n    en: 'Expand',\n    de: 'Ausklappen',\n    es: 'Ampliar',\n    it: 'Espandi',\n    cs: 'Zobrazit zápatí'\n  },\n  hideFooter: {\n    en: 'Collapse',\n    de: 'Einklappen',\n    es: 'Colapso',\n    it: 'Crollo',\n    cs: 'Skrýt zápatí'\n  },\n  messages: {\n    en: 'Messages',\n    de: 'Meldungen',\n    es: 'Mensajes',\n    it: 'Messaggi',\n    cs: 'Zprávy'\n  },\n  messageFilters: {\n    en: 'Filter and Sort',\n    de: 'Filtern und Sortieren',\n    es: 'Filtrar y clasificar',\n    it: 'Filtra e Ordina',\n    cs: 'Filtr a řazení'\n  },\n  emptyMessageSearchResult: {\n    en: 'There are no results for the selected filter criteria.',\n    de: 'Für die gewählten Filterkriterien liegen keine Ergebnisse vor.',\n    es: 'No hay resultados para los criterios de filtro seleccionados.',\n    it: 'Non ci sono risultati per il criterio di filtro selezionato.',\n    cs: 'Pro vybraná kritéria filtru nejsou k dispozici žádné výsledky.'\n  },\n  emptyMessageSearchFacility: {\n    en: 'There are no facilities for the given search string.',\n    de: 'Es gibt keine Einrichtungen zu dieser Suche',\n    it: 'Non ci sono risultati per questa stringa di ricerca.',\n    cs: 'Požadované lokace nebyly nalezeny.'\n  },\n  emptyMessageList: {\n    en: 'There are no messages. Look here for new messages.',\n    de: 'Es liegen keine Meldungen vor. Neue Meldungen sind hier zu finden.',\n    es: 'No hay mensajes. Los nuevos mensajes se pueden encontrar aquí.',\n    it: 'Non ci sono messaggi. Guarda qui per nuovi messaggi.',\n    cs: 'Nejsou k dispozici žádné zprávy. Nové zprávy naleznete zde.'\n  },\n  seeAllMessages: {\n    en: 'View all messages',\n    de: 'Alle Meldungen anzeigen',\n    es: 'Ver todos los mensajes',\n    it: 'Visualizza tutti i messaggi',\n    cs: 'Zobrazit všechny zprávy'\n  },\n  today: {\n    en: 'Today',\n    de: 'Heute',\n    es: 'Hoy',\n    it: 'Oggi',\n    cs: 'Dnes'\n  },\n  yesterday: {\n    en: 'Yesterday',\n    de: 'Gestern',\n    es: 'Ayer',\n    it: 'Ieri',\n    cs: 'Včera'\n  },\n  last: {\n    en: 'Last',\n    de: 'Letzter',\n    es: 'Último',\n    it: 'Ultimo',\n    cs: 'Poslední'\n  },\n  search: {\n    en: 'Search',\n    de: 'Suche',\n    es: 'Buscar',\n    it: 'Cerca',\n    cs: 'Vyhledat'\n  },\n  searchOrSelect: {\n    en: 'Search or select',\n    de: 'Suche or wähle aus',\n    es: 'Buscar u seleccionar',\n    it: 'Cerca o seleziona'\n  },\n  searchType: {\n    en: 'Search type',\n    de: 'Suchtyp',\n    es: 'Tipo de búsqueda',\n    it: 'Tipo di ricerca'\n  },\n  allTypes: {\n    en: 'All message types',\n    de: 'Alle Meldungstypen',\n    es: 'Todos los tipos de mensajes',\n    it: 'Tutti i tipi di messaggi',\n    cs: 'Všechny typy zpráv'\n  },\n  information: {\n    en: 'Information',\n    de: 'Information',\n    es: 'Información',\n    it: 'Informazioni',\n    cs: 'Informace'\n  },\n  success: {\n    en: 'Completed',\n    de: 'Abgeschlossen',\n    es: 'Completado',\n    it: 'Completato',\n    cs: 'Dokončeno'\n  },\n  warning: {\n    en: 'Warning',\n    de: 'Warnung',\n    es: 'Advertencia',\n    it: 'Avviso',\n    cs: 'Výstraha'\n  },\n  error: {\n    en: 'Error',\n    de: 'Fehler',\n    es: 'Error',\n    it: 'Errore',\n    cs: 'Chyba'\n  },\n  descending: {\n    en: 'Descending',\n    de: 'Absteigend',\n    es: 'Descendiendo',\n    it: 'Discendente',\n    cs: 'Sestupné'\n  },\n  ascending: {\n    en: 'Ascending',\n    de: 'Aufsteigend',\n    es: 'Ascendiendo',\n    it: 'Ascendente',\n    cs: 'Vzestupné'\n  },\n  messageTypes: {\n    en: 'Message types',\n    de: 'Meldungstypen',\n    es: 'Tipos de mensajes',\n    it: 'Tipi di messaggi',\n    cs: 'Typy zpráv'\n  },\n  showMoreDetails: {\n    en: 'Show more',\n    de: 'Mehr Details',\n    es: 'Más detalles',\n    it: 'Mostra di più',\n    cs: 'Zobrazit více'\n  },\n  discardAll: {\n    en: 'Delete all',\n    de: 'Alle löschen',\n    es: 'Borrar todo',\n    it: 'Elimina tutti',\n    cs: 'Odstranit vše'\n  },\n  entities: {\n    en: 'Entries',\n    de: 'Einträge',\n    es: 'Entradas',\n    it: 'Elementi',\n    cs: 'Vstupy'\n  },\n  title: {\n    en: 'Title',\n    de: 'Titel',\n    es: 'Título',\n    it: 'Titolo',\n    cs: 'Název'\n  },\n  description: {\n    en: 'Description',\n    de: 'Beschreibung',\n    es: 'Descripción',\n    it: 'Descrizione',\n    cs: 'Popis'\n  },\n  dateTime: {\n    en: 'Date/time',\n    de: 'Datum/Zeit',\n    es: 'Fecha/Hora',\n    it: 'Data/Ora',\n    cs: 'Datum/čas'\n  },\n  messageClass: {\n    en: 'Category',\n    de: 'Kategorie',\n    es: 'Categoría',\n    it: 'Categoria',\n    cs: 'Kategorie'\n  },\n  goTo: {\n    en: 'Go to',\n    de: 'Gehe zu',\n    es: 'Ir a',\n    it: 'Vai a',\n    cs: 'Přejít na'\n  },\n  close: {\n    en: 'Close',\n    de: 'Schließen',\n    es: 'Cerrar',\n    it: 'Chiudi',\n    cs: 'Zavřít'\n  },\n  documentation: {\n    en: 'Documentation',\n    de: 'Dokumentation',\n    es: 'Documentación',\n    it: 'Documenti',\n    cs: 'Dokumentace'\n  },\n  didYouMean: {\n    en: 'Suggestions',\n    de: 'Vorschläge',\n    es: 'Propuestas',\n    it: 'Indicazioni',\n    cs: 'Návrhy'\n  },\n  mostRelevant: {\n    en: 'Matching results',\n    de: 'Passende Ergebnisse',\n    es: 'Resultados coincidentes',\n    it: 'Risultati',\n    cs: 'Shodné výsledky'\n  },\n  dismiss: {\n    en: 'Discard',\n    de: 'Verwerfen',\n    es: 'Descartar',\n    it: 'Elimina',\n    cs: 'Zrušit'\n  },\n  setDate: {\n    en: 'Set date',\n    de: 'Datum festlegen',\n    es: 'Fijar fecha',\n    it: 'Imposta data',\n    cs: 'Nastavit datum'\n  },\n  setTime: {\n    en: 'Set time',\n    de: 'Zeit festlegen',\n    es: 'Fijar hora',\n    it: 'Imposta tempo'\n  },\n  range: {\n    en: 'Time span',\n    de: 'Zeitraum',\n    es: 'Período',\n    it: 'Periodo',\n    cs: 'Časový rozsah'\n  },\n  setDateTime: {\n    en: 'Set date & time',\n    de: 'Datum & Uhrzeit festlegen',\n    es: 'Fijar la fecha y la hora',\n    it: 'Imposta data e ora',\n    cs: 'Nastavit datum a čas'\n  },\n  setRange: {\n    en: 'Set time span',\n    de: 'Zeitraum festlegen',\n    es: 'Establece el período',\n    it: 'Imposta periodo',\n    cs: 'Nastavit časové období'\n  },\n  save: {\n    en: 'Save',\n    de: 'Speichern',\n    es: 'Guardar',\n    it: 'Salva',\n    cs: 'Uložit'\n  },\n  templates: {\n    en: 'Templates',\n    de: 'Vorlagen',\n    es: 'Plantillas',\n    it: 'Tempi',\n    cs: 'Časové rozpětí'\n  },\n  calendar: {\n    en: 'Calendar',\n    de: 'Kalendar',\n    es: 'Calendario',\n    it: 'Calendario'\n  },\n  time: {\n    en: 'Time',\n    de: 'Uhrzeit',\n    es: 'aeg',\n    it: 'Ora',\n    cs: 'Čas'\n  },\n  startTime: {\n    en: 'Start Time',\n    de: 'Startzeit',\n    es: 'Start Time',\n    it: 'Ora di inizio',\n    cs: 'Čas zahájení'\n  },\n  endTime: {\n    en: 'End Time',\n    de: 'Endzeit',\n    es: 'End Time',\n    it: 'Ora di fine',\n    cs: 'Čas ukončení'\n  },\n  listItemsPerPage: {\n    en: 'Elements per page',\n    de: 'Elemente pro Seite',\n    es: 'Elementos por página',\n    it: 'Elementi per pagina',\n    cs: 'Počet položek na stránku'\n  },\n  goToPageX: {\n    en: 'To page {page}',\n    de: 'Zu Seite {page}',\n    es: 'A la página {page}',\n    it: 'A pagina {page}',\n    cs: 'Na stránku {page}'\n  },\n  pagination: {\n    en: 'pagination',\n    de: 'Seitennummerierung',\n    es: 'paginación',\n    it: 'impaginazione',\n    cs: 'paginace'\n  },\n  goToNextPage: {\n    en: 'To next page',\n    de: 'Zur nächsten Seite',\n    es: 'A la página siguiente',\n    it: 'Alla pagina successiva',\n    cs: 'Na další stránku'\n  },\n  goToPreviousPage: {\n    en: 'To previous page',\n    de: 'Zur vorherigen Seite',\n    es: 'A la página anterior',\n    it: 'Alla pagina precedente',\n    cs: 'Na předchozí stránku'\n  },\n  recentSearch: {\n    en: 'Recent searches',\n    de: 'Letzte Suchanfragen',\n    es: 'Búsquedas recientes',\n    it: 'Ricerche recenti',\n    cs: 'Poslední vyhledávání'\n  },\n  back: {\n    en: 'Back',\n    de: 'Zurück',\n    es: 'Atrás',\n    it: 'Indietro',\n    cs: 'Zpět'\n  },\n  'reset-selection': {\n    en: 'Reset selection',\n    de: 'Zurücksetzen',\n    es: 'Resetear',\n    it: 'Resetta selezione'\n  },\n  reset: {\n    en: 'Reset',\n    de: 'Zurücksetzen',\n    es: 'Resetear',\n    it: 'Resetta',\n    cs: 'Obnovit'\n  },\n  faclities: {\n    en: 'Facilities',\n    de: 'Einrichtungen',\n    es: 'Equipos',\n    it: 'Impianti',\n    cs: 'Lokace'\n  },\n  selectSomething: {\n    en: 'Select {{search-type-label}}',\n    de: '{{search-type-label}} auswählen',\n    es: 'Seleccione {search-type-label}}',\n    it: 'Seleziona {{ricerca-tipo-label}}'\n  },\n  noFilterResults: {\n    en: 'No items to display.',\n    de: 'Keine Elemente zum Anzeigen.',\n    es: 'No hay artículos para mostrar.',\n    it: 'Nessun elemento da visualizzare.'\n  },\n  calendarWeek: {\n    en: 'CW {{calendar-week}}',\n    de: 'KW {{calendar-week}}'\n  }\n};\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nconst {\n  state,\n  onChange\n} = createStore({\n  language: DEFAULT_LOCALE.language,\n  dateFormat: DEFAULT_LOCALE.dateFormat,\n  timeFormat: DEFAULT_LOCALE.timeFormat,\n  translations: {},\n  languageListenerActive: false,\n  isStateSet: false\n});\n// Needed when text is not directly translated in the dom\nfunction listenToLanguageChange(callback) {\n  onChange('language', () => callback());\n}\nasync function setLocale() {\n  if (state.isStateSet) {\n    return;\n  }\n  setupLangChangeListener();\n  return Promise.all([getLanguage(), getDateFormat(), getTimeFormat()]).then(async ([language, dateFormat, timeFormat]) => {\n    state.isStateSet = true;\n    state.language = language;\n    state.dateFormat = dateFormat;\n    state.timeFormat = timeFormat;\n    return;\n  });\n}\n/**\n * Gets user language from the document lang attribute\n * Or from the portal user settings global object\n */\nasync function getLanguage() {\n  let language = document.documentElement.getAttribute('lang');\n  if (!language || language === 'undefined') {\n    try {\n      language = await getPortalContext().getSCSUserSetting('bci.system.language');\n    } catch (_a) {\n      language = DEFAULT_LOCALE.language;\n    }\n  }\n  return language;\n}\n/**\n * Gets dateformat string from global user settings object\n * Or takes default\n */\nasync function getDateFormat() {\n  try {\n    return await getPortalContext().getSCSUserSetting('bci.system.dateformat');\n  } catch (_a) {\n    return DEFAULT_LOCALE.dateFormat;\n  }\n}\n/**\n * Gets timeformat string from global user settings object\n * Or takes default\n */\nasync function getTimeFormat() {\n  try {\n    return await getPortalContext().getSCSUserSetting('bci.system.timeformat');\n  } catch (_a) {\n    return DEFAULT_LOCALE.timeFormat;\n  }\n}\nfunction setupLangChangeListener() {\n  if (state.languageListenerActive) {\n    return;\n  }\n  const observer = new MutationObserver(mutations => {\n    // If the language attribute has changed\n    if (mutations.filter(item => item.attributeName === 'lang').length > 0) {\n      state.language = document.documentElement.getAttribute('lang');\n    }\n  });\n  observer.observe(document.documentElement, {\n    attributes: true\n  });\n  state.languageListenerActive = true;\n}\nfunction getPortalContext() {\n  return window['BCIPortal'];\n}\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n/**\n * Translate the given key to the desired language based on the localization dictionary provided\n * under BCIPortal global object.\n *\n * @param key language key of the related text\n * @param language language code. Default is the user language.\n */\nfunction translate(key) {\n  try {\n    if (localization[key]) {\n      return localization[key][state.language] || localization[key]['en'] || key;\n    }\n    return key;\n  } catch (_a) {\n    return key;\n  }\n}\n/**\n * Format a timestamp according to the user's configured date format\n *\n * @param timestamp\n */\nfunction formatDate(timestamp, dateFormat, timeFormat) {\n  try {\n    return format(new Date(timestamp), (dateFormat || state.dateFormat) + ' ' + (timeFormat || state.timeFormat));\n  } catch (_a) {\n    return format(new Date(timestamp), DEFAULT_LOCALE.dateFormat + ' ' + DEFAULT_LOCALE.timeFormat);\n  }\n}\n/**\n * TODO\n *\n * @param timestamp\n */\nfunction formatDateAsDayString(timestamp, dateFormat = DEFAULT_LOCALE.dateFormat) {\n  if (!timestamp) {\n    return;\n  }\n  try {\n    const date = new Date(timestamp);\n    if (isToday(date)) {\n      return translate('today');\n    } else if (isYesterday(date)) {\n      return translate('yesterday');\n    } else if (differenceInDays(new Date(), date) < 7) {\n      return translate('last') + ' ' + format(new Date(timestamp), 'EEEE', {\n        locale: LOCALE_MAP[state.language || DEFAULT_LOCALE.language]\n      });\n    }\n    return format(new Date(timestamp), dateFormat || state.dateFormat);\n  } catch (_a) {\n    return format(new Date(timestamp), DEFAULT_LOCALE.dateFormat);\n  }\n}\n/**\n * Gets a new logger instance to log to the browser console.\n */\nfunction getLogger() {\n  const defaultPrefix = '[bci-portal]';\n  return {\n    info: (msg, object) => {\n      object ? console.log(`${defaultPrefix} ${msg}`, object) : console.log(`${defaultPrefix} ${msg}`);\n    },\n    warn: (msg, object) => {\n      object ? console.warn(`${defaultPrefix} ${msg}`, object) : console.warn(`${defaultPrefix} ${msg}`);\n    },\n    error: (msg, object) => {\n      object ? console.error(`${defaultPrefix} ${msg}`, object) : console.error(`${defaultPrefix} ${msg}`);\n    }\n  };\n}\n/**\n * creates a date instance with specified parameters\n * @param dateInstance to use as basis\n * @param year year to set\n * @param month month so set\n * @param day day in month to set\n */\nfunction createDate(dateInstance, year, month, day = null) {\n  dateInstance = setYear(dateInstance, year);\n  dateInstance = setMonth(dateInstance, month);\n  if (day !== null) {\n    dateInstance = setDate(dateInstance, day);\n  }\n  return dateInstance;\n}\n/**\n * flattens an array\n * @param array\n */\nfunction flattenArray(array) {\n  return [].concat.apply([], array);\n}\n/**\n * Returns whether app is in responsive mode\n */\nfunction isResponsiveMode() {\n  const currentWidth = window.innerWidth;\n  if (currentWidth < 768) {\n    return true;\n  }\n  return false;\n}\n//this method checks if timeformat is with Meridiem(AM/PM) value\nfunction timeFormatHasMeridiem(timeformat) {\n  // space is required as timeformats with AM/PM always have a space between the time and the meridiem\n  return timeformat.includes(' a');\n}\n//this method checks if timeformat has seconds\nfunction isHideSeconds(timeformat) {\n  return timeformat.includes(':ss');\n}\nfunction tap(callback) {\n  return function (value) {\n    callback(value);\n    return value;\n  };\n}\nexport { isSameDay as A, getDaysInMonth as B, isValid as C, DEFAULT_LOCALE as D, startOfDay as E, timeFormatHasMeridiem as F, isHideSeconds as G, listenToLanguageChange as H, formatDateAsDayString as I, formatDate as J, LOCALE_MAP as L, tap as a, toDate as b, toInteger as c, getUTCISOWeek as d, getUTCWeek as e, flattenArray as f, getLogger as g, getUTCWeekYear as h, isResponsiveMode as i, startOfUTCWeek as j, startOfUTCISOWeek as k, isProtectedWeekYearToken as l, throwProtectedError as m, isProtectedDayOfYearToken as n, subMilliseconds as o, getTimezoneOffsetInMilliseconds as p, locale$9 as q, requiredArgs as r, setLocale as s, translate as t, longFormatters as u, addDays as v, format as w, setMonth as x, state as y, createDate as z };\n\n"], "mappings": ";;;;;;;;;AAEA,SAAS,UAAU,aAAa;AAC9B,MAAI,gBAAgB,QAAQ,gBAAgB,QAAQ,gBAAgB,OAAO;AACzE,WAAO;AAAA,EACT;AACA,MAAI,SAAS,OAAO,WAAW;AAC/B,MAAI,MAAM,MAAM,GAAG;AACjB,WAAO;AAAA,EACT;AACA,SAAO,SAAS,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,MAAM;AAC3D;AACA,SAAS,aAAa,UAAU,MAAM;AACpC,MAAI,KAAK,SAAS,UAAU;AAC1B,UAAM,IAAI,UAAU,WAAW,eAAe,WAAW,IAAI,MAAM,MAAM,yBAAyB,KAAK,SAAS,UAAU;AAAA,EAC5H;AACF;AAiCA,SAAS,OAAO,UAAU;AACxB,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,OAAO,UAAU,SAAS,KAAK,QAAQ;AAEpD,MAAI,oBAAoB,QAAQ,OAAO,aAAa,YAAY,WAAW,iBAAiB;AAE1F,WAAO,IAAI,KAAK,SAAS,QAAQ,CAAC;AAAA,EACpC,WAAW,OAAO,aAAa,YAAY,WAAW,mBAAmB;AACvE,WAAO,IAAI,KAAK,QAAQ;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,aAAa,YAAY,WAAW,sBAAsB,OAAO,YAAY,aAAa;AAEpG,cAAQ,KAAK,kJAAkJ;AAE/J,cAAQ,KAAK,IAAI,MAAM,EAAE,KAAK;AAAA,IAChC;AACA,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AACF;AAyBA,SAAS,QAAQ,WAAW,aAAa;AACvC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,SAAS,UAAU,WAAW;AAClC,MAAI,MAAM,MAAM,GAAG;AACjB,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AACA,MAAI,CAAC,QAAQ;AAEX,WAAO;AAAA,EACT;AACA,OAAK,QAAQ,KAAK,QAAQ,IAAI,MAAM;AACpC,SAAO;AACT;AAyBA,SAAS,gBAAgB,WAAW,aAAa;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,YAAY,OAAO,SAAS,EAAE,QAAQ;AAC1C,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,IAAI,KAAK,YAAY,MAAM;AACpC;AAaA,SAAS,gCAAgC,MAAM;AAC7C,MAAI,UAAU,IAAI,KAAK,KAAK,IAAI,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG,KAAK,gBAAgB,CAAC,CAAC;AACnK,UAAQ,eAAe,KAAK,YAAY,CAAC;AACzC,SAAO,KAAK,QAAQ,IAAI,QAAQ,QAAQ;AAC1C;AAyBA,SAAS,WAAW,WAAW;AAC7B,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;AACA,IAAI,wBAAwB;AAoC5B,SAAS,yBAAyB,eAAe,gBAAgB;AAC/D,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,WAAW,aAAa;AAC7C,MAAI,kBAAkB,WAAW,cAAc;AAC/C,MAAI,gBAAgB,eAAe,QAAQ,IAAI,gCAAgC,cAAc;AAC7F,MAAI,iBAAiB,gBAAgB,QAAQ,IAAI,gCAAgC,eAAe;AAIhG,SAAO,KAAK,OAAO,gBAAgB,kBAAkB,qBAAqB;AAC5E;AAmCA,SAAS,UAAU,eAAe,gBAAgB;AAChD,eAAa,GAAG,SAAS;AACzB,MAAI,qBAAqB,WAAW,aAAa;AACjD,MAAI,sBAAsB,WAAW,cAAc;AACnD,SAAO,mBAAmB,QAAQ,MAAM,oBAAoB,QAAQ;AACtE;AAuCA,SAAS,OAAO,OAAO;AACrB,eAAa,GAAG,SAAS;AACzB,SAAO,iBAAiB,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACzG;AA4DA,SAAS,QAAQ,WAAW;AAC1B,eAAa,GAAG,SAAS;AACzB,MAAI,CAAC,OAAO,SAAS,KAAK,OAAO,cAAc,UAAU;AACvD,WAAO;AAAA,EACT;AACA,MAAI,OAAO,OAAO,SAAS;AAC3B,SAAO,CAAC,MAAM,OAAO,IAAI,CAAC;AAC5B;AAMA,SAAS,gBAAgB,UAAU,WAAW;AAC5C,MAAI,OAAO,SAAS,YAAY,IAAI,UAAU,YAAY,KAAK,SAAS,SAAS,IAAI,UAAU,SAAS,KAAK,SAAS,QAAQ,IAAI,UAAU,QAAQ,KAAK,SAAS,SAAS,IAAI,UAAU,SAAS,KAAK,SAAS,WAAW,IAAI,UAAU,WAAW,KAAK,SAAS,WAAW,IAAI,UAAU,WAAW,KAAK,SAAS,gBAAgB,IAAI,UAAU,gBAAgB;AAClW,MAAI,OAAO,GAAG;AACZ,WAAO;AAAA,EACT,WAAW,OAAO,GAAG;AACnB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAuDA,SAAS,iBAAiB,eAAe,gBAAgB;AACvD,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,OAAO,gBAAgB,UAAU,SAAS;AAC9C,MAAI,aAAa,KAAK,IAAI,yBAAyB,UAAU,SAAS,CAAC;AACvE,WAAS,QAAQ,SAAS,QAAQ,IAAI,OAAO,UAAU;AAGvD,MAAI,mBAAmB,OAAO,gBAAgB,UAAU,SAAS,MAAM,CAAC,IAAI;AAC5E,MAAI,SAAS,QAAQ,aAAa;AAElC,SAAO,WAAW,IAAI,IAAI;AAC5B;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,SAAS;AACtD,MAAI;AACJ,MAAI,aAAa,uBAAuB,KAAK;AAC7C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,MAAM,SAAS,CAAC;AAAA,EACjE;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM;AAC/B,SAAO,WAAY;AACjB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,QAAI,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI,KAAK;AACzD,QAAIA,UAAS,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,YAAY;AAClE,WAAOA;AAAA,EACT;AACF;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,WAAW,UAAU;AAClE,SAAO,uBAAuB,KAAK;AACrC;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAU,YAAY,cAAc;AACzC,QAAI,UAAU,gBAAgB,CAAC;AAC/B,QAAI,UAAU,QAAQ,UAAU,OAAO,QAAQ,OAAO,IAAI;AAC1D,QAAI;AACJ,QAAI,YAAY,gBAAgB,KAAK,kBAAkB;AACrD,UAAI,eAAe,KAAK,0BAA0B,KAAK;AACvD,UAAI,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI;AACpD,oBAAc,KAAK,iBAAiB,KAAK,KAAK,KAAK,iBAAiB,YAAY;AAAA,IAClF,OAAO;AACL,UAAI,gBAAgB,KAAK;AACzB,UAAI,SAAS,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI,KAAK;AAC1D,oBAAc,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,aAAa;AAAA,IAChE;AACA,QAAI,QAAQ,KAAK,mBAAmB,KAAK,iBAAiB,UAAU,IAAI;AAExE,WAAO,YAAY,KAAK;AAAA,EAC1B;AACF;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,GAAG;AAAA,EACjB,aAAa,CAAC,MAAM,IAAI;AAAA,EACxB,MAAM,CAAC,iBAAiB,aAAa;AACvC;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,eAAe,eAAe,eAAe,aAAa;AACnE;AAKA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,WAAW,YAAY,SAAS,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU;AACjI;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,UAAU,UAAU,WAAW,aAAa,YAAY,UAAU,UAAU;AACrF;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,SAAU,aAAa,UAAU;AACrD,MAAI,SAAS,OAAO,WAAW;AAO/B,MAAI,SAAS,SAAS;AACtB,MAAI,SAAS,MAAM,SAAS,IAAI;AAC9B,YAAQ,SAAS,IAAI;AAAA,MACnB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB,KAAK;AACH,eAAO,SAAS;AAAA,MAClB,KAAK;AACH,eAAO,SAAS;AAAA,IACpB;AAAA,EACF;AACA,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,SAAU,QAAQ;AACvB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,QAAQ,QAAQ;AACpB,QAAI,eAAe,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,iBAAiB;AAClG,QAAI,cAAc,OAAO,MAAM,YAAY;AAC3C,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,YAAY,CAAC;AACjC,QAAI,gBAAgB,SAAS,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,iBAAiB;AACnG,QAAI,MAAM,MAAM,QAAQ,aAAa,IAAI,UAAU,eAAe,SAAU,SAAS;AACnF,aAAO,QAAQ,KAAK,aAAa;AAAA,IACnC,CAAC,IAAI,QAAQ,eAAe,SAAU,SAAS;AAC7C,aAAO,QAAQ,KAAK,aAAa;AAAA,IACnC,CAAC;AACD,QAAI;AACJ,YAAQ,KAAK,gBAAgB,KAAK,cAAc,GAAG,IAAI;AACvD,YAAQ,QAAQ,gBAAgB,QAAQ,cAAc,KAAK,IAAI;AAC/D,QAAI,OAAO,OAAO,MAAM,cAAc,MAAM;AAC5C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,QAAQ,QAAQ,WAAW;AAClC,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,KAAK,UAAU,OAAO,GAAG,CAAC,GAAG;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAU,OAAO,WAAW;AACnC,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,QAAI,UAAU,MAAM,GAAG,CAAC,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,SAAU,QAAQ;AACvB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,cAAc,OAAO,MAAM,KAAK,YAAY;AAChD,QAAI,CAAC,YAAa,QAAO;AACzB,QAAI,gBAAgB,YAAY,CAAC;AACjC,QAAI,cAAc,OAAO,MAAM,KAAK,YAAY;AAChD,QAAI,CAAC,YAAa,QAAO;AACzB,QAAI,QAAQ,KAAK,gBAAgB,KAAK,cAAc,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC;AACnF,YAAQ,QAAQ,gBAAgB,QAAQ,cAAc,KAAK,IAAI;AAC/D,QAAI,OAAO,OAAO,MAAM,cAAc,MAAM;AAC5C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,OAAO,SAAS;AACxB;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AACrG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,MAAM;AAC3D;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAWA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AAyBA,SAAS,gBAAgB,WAAW,aAAa;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,gBAAgB,WAAW,CAAC,MAAM;AAC3C;AACA,IAAI,sBAAsB;AAG1B,SAAS,gBAAgB,WAAW;AAClC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,YAAY,KAAK,QAAQ;AAC7B,OAAK,YAAY,GAAG,CAAC;AACrB,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC3B,MAAI,uBAAuB,KAAK,QAAQ;AACxC,MAAI,aAAa,YAAY;AAC7B,SAAO,KAAK,MAAM,aAAa,mBAAmB,IAAI;AACxD;AAIA,SAAS,kBAAkB,WAAW;AACpC,eAAa,GAAG,SAAS;AACzB,MAAI,eAAe;AACnB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,UAAU;AACzB,MAAI,QAAQ,MAAM,eAAe,IAAI,KAAK,MAAM;AAChD,OAAK,WAAW,KAAK,WAAW,IAAI,IAAI;AACxC,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC3B,SAAO;AACT;AAIA,SAAS,kBAAkB,WAAW;AACpC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,eAAe;AAC/B,MAAI,4BAA4B,oBAAI,KAAK,CAAC;AAC1C,4BAA0B,eAAe,OAAO,GAAG,GAAG,CAAC;AACvD,4BAA0B,YAAY,GAAG,GAAG,GAAG,CAAC;AAChD,MAAI,kBAAkB,kBAAkB,yBAAyB;AACjE,MAAI,4BAA4B,oBAAI,KAAK,CAAC;AAC1C,4BAA0B,eAAe,MAAM,GAAG,CAAC;AACnD,4BAA0B,YAAY,GAAG,GAAG,GAAG,CAAC;AAChD,MAAI,kBAAkB,kBAAkB,yBAAyB;AACjE,MAAI,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AAC/C,WAAO,OAAO;AAAA,EAChB,WAAW,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AACtD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;AAIA,SAAS,sBAAsB,WAAW;AACxC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,kBAAkB,SAAS;AACtC,MAAI,kBAAkB,oBAAI,KAAK,CAAC;AAChC,kBAAgB,eAAe,MAAM,GAAG,CAAC;AACzC,kBAAgB,YAAY,GAAG,GAAG,GAAG,CAAC;AACtC,MAAI,OAAO,kBAAkB,eAAe;AAC5C,SAAO;AACT;AACA,IAAI,yBAAyB;AAG7B,SAAS,cAAc,WAAW;AAChC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,kBAAkB,IAAI,EAAE,QAAQ,IAAI,sBAAsB,IAAI,EAAE,QAAQ;AAInF,SAAO,KAAK,MAAM,OAAO,sBAAsB,IAAI;AACrD;AAIA,SAAS,eAAe,WAAW,cAAc;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,UAAU,gBAAgB,CAAC;AAC/B,MAAIC,UAAS,QAAQ;AACrB,MAAI,qBAAqBA,WAAUA,QAAO,WAAWA,QAAO,QAAQ;AACpE,MAAI,sBAAsB,sBAAsB,OAAO,IAAI,UAAU,kBAAkB;AACvF,MAAI,eAAe,QAAQ,gBAAgB,OAAO,sBAAsB,UAAU,QAAQ,YAAY;AAEtG,MAAI,EAAE,gBAAgB,KAAK,gBAAgB,IAAI;AAC7C,UAAM,IAAI,WAAW,kDAAkD;AAAA,EACzE;AACA,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,UAAU;AACzB,MAAI,QAAQ,MAAM,eAAe,IAAI,KAAK,MAAM;AAChD,OAAK,WAAW,KAAK,WAAW,IAAI,IAAI;AACxC,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC3B,SAAO;AACT;AAIA,SAAS,eAAe,WAAW,cAAc;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,eAAe;AAC/B,MAAI,UAAU,gBAAgB,CAAC;AAC/B,MAAIA,UAAS,QAAQ;AACrB,MAAI,8BAA8BA,WAAUA,QAAO,WAAWA,QAAO,QAAQ;AAC7E,MAAI,+BAA+B,+BAA+B,OAAO,IAAI,UAAU,2BAA2B;AAClH,MAAI,wBAAwB,QAAQ,yBAAyB,OAAO,+BAA+B,UAAU,QAAQ,qBAAqB;AAE1I,MAAI,EAAE,yBAAyB,KAAK,yBAAyB,IAAI;AAC/D,UAAM,IAAI,WAAW,2DAA2D;AAAA,EAClF;AACA,MAAI,sBAAsB,oBAAI,KAAK,CAAC;AACpC,sBAAoB,eAAe,OAAO,GAAG,GAAG,qBAAqB;AACrE,sBAAoB,YAAY,GAAG,GAAG,GAAG,CAAC;AAC1C,MAAI,kBAAkB,eAAe,qBAAqB,YAAY;AACtE,MAAI,sBAAsB,oBAAI,KAAK,CAAC;AACpC,sBAAoB,eAAe,MAAM,GAAG,qBAAqB;AACjE,sBAAoB,YAAY,GAAG,GAAG,GAAG,CAAC;AAC1C,MAAI,kBAAkB,eAAe,qBAAqB,YAAY;AACtE,MAAI,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AAC/C,WAAO,OAAO;AAAA,EAChB,WAAW,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AACtD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;AAIA,SAAS,mBAAmB,WAAW,cAAc;AACnD,eAAa,GAAG,SAAS;AACzB,MAAI,UAAU,gBAAgB,CAAC;AAC/B,MAAIA,UAAS,QAAQ;AACrB,MAAI,8BAA8BA,WAAUA,QAAO,WAAWA,QAAO,QAAQ;AAC7E,MAAI,+BAA+B,+BAA+B,OAAO,IAAI,UAAU,2BAA2B;AAClH,MAAI,wBAAwB,QAAQ,yBAAyB,OAAO,+BAA+B,UAAU,QAAQ,qBAAqB;AAC1I,MAAI,OAAO,eAAe,WAAW,YAAY;AACjD,MAAI,YAAY,oBAAI,KAAK,CAAC;AAC1B,YAAU,eAAe,MAAM,GAAG,qBAAqB;AACvD,YAAU,YAAY,GAAG,GAAG,GAAG,CAAC;AAChC,MAAI,OAAO,eAAe,WAAW,YAAY;AACjD,SAAO;AACT;AACA,IAAI,uBAAuB;AAG3B,SAAS,WAAW,WAAW,SAAS;AACtC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,eAAe,MAAM,OAAO,EAAE,QAAQ,IAAI,mBAAmB,MAAM,OAAO,EAAE,QAAQ;AAI/F,SAAO,KAAK,MAAM,OAAO,oBAAoB,IAAI;AACnD;AACA,SAAS,gBAAgB,QAAQ,cAAc;AAC7C,MAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,MAAI,SAAS,KAAK,IAAI,MAAM,EAAE,SAAS;AACvC,SAAO,OAAO,SAAS,cAAc;AACnC,aAAS,MAAM;AAAA,EACjB;AACA,SAAO,OAAO;AAChB;AAeA,IAAI,eAAe;AAAA;AAAA,EAEjB,GAAG,SAAU,MAAM,OAAO;AASxB,QAAI,aAAa,KAAK,eAAe;AAErC,QAAI,OAAO,aAAa,IAAI,aAAa,IAAI;AAC7C,WAAO,gBAAgB,UAAU,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM;AAAA,EACzE;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,QAAI,QAAQ,KAAK,YAAY;AAC7B,WAAO,UAAU,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAgB,QAAQ,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,KAAK,WAAW,GAAG,MAAM,MAAM;AAAA,EACxD;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,QAAI,qBAAqB,KAAK,YAAY,IAAI,MAAM,IAAI,OAAO;AAC/D,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,mBAAmB,YAAY;AAAA,MACxC,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,mBAAmB,CAAC;AAAA,MAC7B,KAAK;AAAA,MACL;AACE,eAAO,uBAAuB,OAAO,SAAS;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,KAAK,YAAY,IAAI,MAAM,IAAI,MAAM,MAAM;AAAA,EACpE;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,KAAK,YAAY,GAAG,MAAM,MAAM;AAAA,EACzD;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,KAAK,cAAc,GAAG,MAAM,MAAM;AAAA,EAC3D;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,gBAAgB,KAAK,cAAc,GAAG,MAAM,MAAM;AAAA,EAC3D;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,QAAI,iBAAiB,MAAM;AAC3B,QAAI,eAAe,KAAK,mBAAmB;AAC3C,QAAI,oBAAoB,KAAK,MAAM,eAAe,KAAK,IAAI,IAAI,iBAAiB,CAAC,CAAC;AAClF,WAAO,gBAAgB,mBAAmB,MAAM,MAAM;AAAA,EACxD;AACF;AACA,IAAI,gBAAgB;AAAA,EAClB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AACT;AA+CA,IAAI,aAAa;AAAA;AAAA,EAEf,GAAG,SAAU,MAAM,OAAOC,WAAU;AAClC,QAAI,MAAM,KAAK,eAAe,IAAI,IAAI,IAAI;AAC1C,YAAQ,OAAO;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,IAAI,KAAK;AAAA,UACvB,OAAO;AAAA,QACT,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,KAAK;AAAA,UACvB,OAAO;AAAA,QACT,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,KAAK;AAAA,UACvB,OAAO;AAAA,QACT,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAElC,QAAI,UAAU,MAAM;AAClB,UAAI,aAAa,KAAK,eAAe;AAErC,UAAI,OAAO,aAAa,IAAI,aAAa,IAAI;AAC7C,aAAOA,UAAS,cAAc,MAAM;AAAA,QAClC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,QAAI,iBAAiB,eAAe,MAAM,OAAO;AAEjD,QAAI,WAAW,iBAAiB,IAAI,iBAAiB,IAAI;AAEzD,QAAI,UAAU,MAAM;AAClB,UAAI,eAAe,WAAW;AAC9B,aAAO,gBAAgB,cAAc,CAAC;AAAA,IACxC;AAEA,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,UAAU;AAAA,QACtC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,WAAO,gBAAgB,UAAU,MAAM,MAAM;AAAA,EAC/C;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,QAAI,cAAc,kBAAkB,IAAI;AAExC,WAAO,gBAAgB,aAAa,MAAM,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,GAAG,SAAU,MAAM,OAAO;AACxB,QAAI,OAAO,KAAK,eAAe;AAC/B,WAAO,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC3C;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,KAAK,MAAM,KAAK,YAAY,IAAI,KAAK,CAAC;AACpD,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,OAAO;AAAA,MAGvB,KAAK;AACH,eAAO,gBAAgB,SAAS,CAAC;AAAA,MAGnC,KAAK;AACH,eAAOA,UAAS,cAAc,SAAS;AAAA,UACrC,MAAM;AAAA,QACR,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,KAAK,MAAM,KAAK,YAAY,IAAI,KAAK,CAAC;AACpD,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,OAAO;AAAA,MAGvB,KAAK;AACH,eAAO,gBAAgB,SAAS,CAAC;AAAA,MAGnC,KAAK;AACH,eAAOA,UAAS,cAAc,SAAS;AAAA,UACrC,MAAM;AAAA,QACR,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,QAAQ,SAAS;AAAA,UAC/B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY;AAC7B,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAO,aAAa,EAAE,MAAM,KAAK;AAAA,MAGnC,KAAK;AACH,eAAOA,UAAS,cAAc,QAAQ,GAAG;AAAA,UACvC,MAAM;AAAA,QACR,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY;AAC7B,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,QAAQ,CAAC;AAAA,MAGzB,KAAK;AACH,eAAO,gBAAgB,QAAQ,GAAG,CAAC;AAAA,MAGrC,KAAK;AACH,eAAOA,UAAS,cAAc,QAAQ,GAAG;AAAA,UACvC,MAAM;AAAA,QACR,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,MAAM,OAAO;AAAA,UAC3B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,QAAI,OAAO,WAAW,MAAM,OAAO;AACnC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,MAAM;AAAA,QAClC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC3C;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,cAAc,IAAI;AAChC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,SAAS;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,SAAS,MAAM,MAAM;AAAA,EAC9C;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,WAAW,GAAG;AAAA,QAC/C,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,YAAY,gBAAgB,IAAI;AACpC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,WAAW;AAAA,QACvC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAChD;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,YAAY,KAAK,UAAU;AAC/B,YAAQ,OAAO;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,QAAI,YAAY,KAAK,UAAU;AAC/B,QAAI,kBAAkB,YAAY,QAAQ,eAAe,KAAK,KAAK;AACnE,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,cAAc;AAAA,MAG9B,KAAK;AACH,eAAO,gBAAgB,gBAAgB,CAAC;AAAA,MAG1C,KAAK;AACH,eAAOA,UAAS,cAAc,gBAAgB;AAAA,UAC5C,MAAM;AAAA,QACR,CAAC;AAAA,MACH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU,SAAS;AAC3C,QAAI,YAAY,KAAK,UAAU;AAC/B,QAAI,kBAAkB,YAAY,QAAQ,eAAe,KAAK,KAAK;AACnE,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,cAAc;AAAA,MAG9B,KAAK;AACH,eAAO,gBAAgB,gBAAgB,MAAM,MAAM;AAAA,MAGrD,KAAK;AACH,eAAOA,UAAS,cAAc,gBAAgB;AAAA,UAC5C,MAAM;AAAA,QACR,CAAC;AAAA,MACH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,YAAY,KAAK,UAAU;AAC/B,QAAI,eAAe,cAAc,IAAI,IAAI;AACzC,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,OAAO,YAAY;AAAA,MAG5B,KAAK;AACH,eAAO,gBAAgB,cAAc,MAAM,MAAM;AAAA,MAGnD,KAAK;AACH,eAAOA,UAAS,cAAc,cAAc;AAAA,UAC1C,MAAM;AAAA,QACR,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AACH,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MAGH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,IAAI,WAAW;AAAA,UAC7B,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY;AAC7B,QAAI,qBAAqB,QAAQ,MAAM,IAAI,OAAO;AAClD,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,EAAE,YAAY;AAAA,MACjB,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY;AAC7B,QAAI;AACJ,QAAI,UAAU,IAAI;AAChB,2BAAqB,cAAc;AAAA,IACrC,WAAW,UAAU,GAAG;AACtB,2BAAqB,cAAc;AAAA,IACrC,OAAO;AACL,2BAAqB,QAAQ,MAAM,IAAI,OAAO;AAAA,IAChD;AACA,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC,EAAE,YAAY;AAAA,MACjB,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY;AAC7B,QAAI;AACJ,QAAI,SAAS,IAAI;AACf,2BAAqB,cAAc;AAAA,IACrC,WAAW,SAAS,IAAI;AACtB,2BAAqB,cAAc;AAAA,IACrC,WAAW,SAAS,GAAG;AACrB,2BAAqB,cAAc;AAAA,IACrC,OAAO;AACL,2BAAqB,cAAc;AAAA,IACrC;AACA,YAAQ,OAAO;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AACH,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,MACH,KAAK;AAAA,MACL;AACE,eAAOA,UAAS,UAAU,oBAAoB;AAAA,UAC5C,OAAO;AAAA,UACP,SAAS;AAAA,QACX,CAAC;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,UAAI,QAAQ,KAAK,YAAY,IAAI;AACjC,UAAI,UAAU,EAAG,SAAQ;AACzB,aAAOA,UAAS,cAAc,OAAO;AAAA,QACnC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,YAAY,GAAG;AAAA,QAChD,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY,IAAI;AACjC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,OAAO;AAAA,QACnC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,OAAO,MAAM,MAAM;AAAA,EAC5C;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,QAAQ,KAAK,YAAY;AAC7B,QAAI,UAAU,EAAG,SAAQ;AACzB,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,OAAO;AAAA,QACnC,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,gBAAgB,OAAO,MAAM,MAAM;AAAA,EAC5C;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,cAAc,GAAG;AAAA,QAClD,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAOA,WAAU;AAClC,QAAI,UAAU,MAAM;AAClB,aAAOA,UAAS,cAAc,KAAK,cAAc,GAAG;AAAA,QAClD,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO;AACxB,WAAO,aAAa,EAAE,MAAM,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO,WAAW,SAAS;AAC5C,QAAI,eAAe,QAAQ,iBAAiB;AAC5C,QAAI,iBAAiB,aAAa,kBAAkB;AACpD,QAAI,mBAAmB,GAAG;AACxB,aAAO;AAAA,IACT;AACA,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,kCAAkC,cAAc;AAAA,MAKzD,KAAK;AAAA,MACL,KAAK;AAEH,eAAO,eAAe,cAAc;AAAA,MAKtC,KAAK;AAAA,MACL,KAAK;AAAA,MAEL;AACE,eAAO,eAAe,gBAAgB,GAAG;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO,WAAW,SAAS;AAC5C,QAAI,eAAe,QAAQ,iBAAiB;AAC5C,QAAI,iBAAiB,aAAa,kBAAkB;AACpD,YAAQ,OAAO;AAAA,MAEb,KAAK;AACH,eAAO,kCAAkC,cAAc;AAAA,MAKzD,KAAK;AAAA,MACL,KAAK;AAEH,eAAO,eAAe,cAAc;AAAA,MAKtC,KAAK;AAAA,MACL,KAAK;AAAA,MAEL;AACE,eAAO,eAAe,gBAAgB,GAAG;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO,WAAW,SAAS;AAC5C,QAAI,eAAe,QAAQ,iBAAiB;AAC5C,QAAI,iBAAiB,aAAa,kBAAkB;AACpD,YAAQ,OAAO;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,oBAAoB,gBAAgB,GAAG;AAAA,MAGxD,KAAK;AAAA,MACL;AACE,eAAO,QAAQ,eAAe,gBAAgB,GAAG;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO,WAAW,SAAS;AAC5C,QAAI,eAAe,QAAQ,iBAAiB;AAC5C,QAAI,iBAAiB,aAAa,kBAAkB;AACpD,YAAQ,OAAO;AAAA,MAEb,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,QAAQ,oBAAoB,gBAAgB,GAAG;AAAA,MAGxD,KAAK;AAAA,MACL;AACE,eAAO,QAAQ,eAAe,gBAAgB,GAAG;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO,WAAW,SAAS;AAC5C,QAAI,eAAe,QAAQ,iBAAiB;AAC5C,QAAI,YAAY,KAAK,MAAM,aAAa,QAAQ,IAAI,GAAI;AACxD,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAChD;AAAA;AAAA,EAEA,GAAG,SAAU,MAAM,OAAO,WAAW,SAAS;AAC5C,QAAI,eAAe,QAAQ,iBAAiB;AAC5C,QAAI,YAAY,aAAa,QAAQ;AACrC,WAAO,gBAAgB,WAAW,MAAM,MAAM;AAAA,EAChD;AACF;AACA,SAAS,oBAAoB,QAAQ,gBAAgB;AACnD,MAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,MAAI,YAAY,KAAK,IAAI,MAAM;AAC/B,MAAI,QAAQ,KAAK,MAAM,YAAY,EAAE;AACrC,MAAI,UAAU,YAAY;AAC1B,MAAI,YAAY,GAAG;AACjB,WAAO,OAAO,OAAO,KAAK;AAAA,EAC5B;AACA,MAAI,YAAY,kBAAkB;AAClC,SAAO,OAAO,OAAO,KAAK,IAAI,YAAY,gBAAgB,SAAS,CAAC;AACtE;AACA,SAAS,kCAAkC,QAAQ,gBAAgB;AACjE,MAAI,SAAS,OAAO,GAAG;AACrB,QAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,WAAO,OAAO,gBAAgB,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,EACxD;AACA,SAAO,eAAe,QAAQ,cAAc;AAC9C;AACA,SAAS,eAAe,QAAQ,gBAAgB;AAC9C,MAAI,YAAY,kBAAkB;AAClC,MAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,MAAI,YAAY,KAAK,IAAI,MAAM;AAC/B,MAAI,QAAQ,gBAAgB,KAAK,MAAM,YAAY,EAAE,GAAG,CAAC;AACzD,MAAI,UAAU,gBAAgB,YAAY,IAAI,CAAC;AAC/C,SAAO,OAAO,QAAQ,YAAY;AACpC;AACA,SAAS,kBAAkB,SAASC,aAAY;AAC9C,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AACH,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AACH,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AAAA,IACL;AACE,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,EACL;AACF;AACA,SAAS,kBAAkB,SAASA,aAAY;AAC9C,UAAQ,SAAS;AAAA,IACf,KAAK;AACH,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AACH,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AACH,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,IACH,KAAK;AAAA,IACL;AACE,aAAOA,YAAW,KAAK;AAAA,QACrB,OAAO;AAAA,MACT,CAAC;AAAA,EACL;AACF;AACA,SAAS,sBAAsB,SAASA,aAAY;AAClD,MAAI,cAAc,QAAQ,MAAM,WAAW,KAAK,CAAC;AACjD,MAAI,cAAc,YAAY,CAAC;AAC/B,MAAI,cAAc,YAAY,CAAC;AAC/B,MAAI,CAAC,aAAa;AAChB,WAAO,kBAAkB,SAASA,WAAU;AAAA,EAC9C;AACA,MAAI;AACJ,UAAQ,aAAa;AAAA,IACnB,KAAK;AACH,uBAAiBA,YAAW,SAAS;AAAA,QACnC,OAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF,KAAK;AACH,uBAAiBA,YAAW,SAAS;AAAA,QACnC,OAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF,KAAK;AACH,uBAAiBA,YAAW,SAAS;AAAA,QACnC,OAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF,KAAK;AAAA,IACL;AACE,uBAAiBA,YAAW,SAAS;AAAA,QACnC,OAAO;AAAA,MACT,CAAC;AACD;AAAA,EACJ;AACA,SAAO,eAAe,QAAQ,YAAY,kBAAkB,aAAaA,WAAU,CAAC,EAAE,QAAQ,YAAY,kBAAkB,aAAaA,WAAU,CAAC;AACtJ;AACA,IAAI,iBAAiB;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAI,2BAA2B,CAAC,KAAK,IAAI;AACzC,IAAI,0BAA0B,CAAC,MAAM,MAAM;AAC3C,SAAS,0BAA0B,OAAO;AACxC,SAAO,yBAAyB,QAAQ,KAAK,MAAM;AACrD;AACA,SAAS,yBAAyB,OAAO;AACvC,SAAO,wBAAwB,QAAQ,KAAK,MAAM;AACpD;AACA,SAAS,oBAAoB,OAAOH,SAAQ,OAAO;AACjD,MAAI,UAAU,QAAQ;AACpB,UAAM,IAAI,WAAW,qCAAqC,OAAOA,SAAQ,wCAAwC,EAAE,OAAO,OAAO,8BAA8B,CAAC;AAAA,EAClK,WAAW,UAAU,MAAM;AACzB,UAAM,IAAI,WAAW,iCAAiC,OAAOA,SAAQ,wCAAwC,EAAE,OAAO,OAAO,8BAA8B,CAAC;AAAA,EAC9J,WAAW,UAAU,KAAK;AACxB,UAAM,IAAI,WAAW,+BAA+B,OAAOA,SAAQ,oDAAoD,EAAE,OAAO,OAAO,8BAA8B,CAAC;AAAA,EACxK,WAAW,UAAU,MAAM;AACzB,UAAM,IAAI,WAAW,iCAAiC,OAAOA,SAAQ,oDAAoD,EAAE,OAAO,OAAO,8BAA8B,CAAC;AAAA,EAC1K;AACF;AAaA,IAAI,yBAAyB;AAG7B,IAAI,6BAA6B;AACjC,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,IAAI,gCAAgC;AAyTpC,SAAS,OAAO,WAAW,gBAAgB,cAAc;AACvD,eAAa,GAAG,SAAS;AACzB,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,UAAU,gBAAgB,CAAC;AAC/B,MAAIC,UAAS,QAAQ,UAAU;AAC/B,MAAI,8BAA8BA,QAAO,WAAWA,QAAO,QAAQ;AACnE,MAAI,+BAA+B,+BAA+B,OAAO,IAAI,UAAU,2BAA2B;AAClH,MAAI,wBAAwB,QAAQ,yBAAyB,OAAO,+BAA+B,UAAU,QAAQ,qBAAqB;AAE1I,MAAI,EAAE,yBAAyB,KAAK,yBAAyB,IAAI;AAC/D,UAAM,IAAI,WAAW,2DAA2D;AAAA,EAClF;AACA,MAAI,qBAAqBA,QAAO,WAAWA,QAAO,QAAQ;AAC1D,MAAI,sBAAsB,sBAAsB,OAAO,IAAI,UAAU,kBAAkB;AACvF,MAAI,eAAe,QAAQ,gBAAgB,OAAO,sBAAsB,UAAU,QAAQ,YAAY;AAEtG,MAAI,EAAE,gBAAgB,KAAK,gBAAgB,IAAI;AAC7C,UAAM,IAAI,WAAW,kDAAkD;AAAA,EACzE;AACA,MAAI,CAACA,QAAO,UAAU;AACpB,UAAM,IAAI,WAAW,uCAAuC;AAAA,EAC9D;AACA,MAAI,CAACA,QAAO,YAAY;AACtB,UAAM,IAAI,WAAW,yCAAyC;AAAA,EAChE;AACA,MAAI,eAAe,OAAO,SAAS;AACnC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAIA,MAAI,iBAAiB,gCAAgC,YAAY;AACjE,MAAI,UAAU,gBAAgB,cAAc,cAAc;AAC1D,MAAI,mBAAmB;AAAA,IACrB;AAAA,IACA;AAAA,IACA,QAAQA;AAAA,IACR,eAAe;AAAA,EACjB;AACA,MAAI,SAAS,UAAU,MAAM,0BAA0B,EAAE,IAAI,SAAU,WAAW;AAChF,QAAI,iBAAiB,UAAU,CAAC;AAChC,QAAI,mBAAmB,OAAO,mBAAmB,KAAK;AACpD,UAAI,gBAAgB,eAAe,cAAc;AACjD,aAAO,cAAc,WAAWA,QAAO,YAAY,gBAAgB;AAAA,IACrE;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,sBAAsB,EAAE,IAAI,SAAU,WAAW;AAEjE,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,UAAU,CAAC;AAChC,QAAI,mBAAmB,KAAK;AAC1B,aAAO,mBAAmB,SAAS;AAAA,IACrC;AACA,QAAI,YAAY,WAAW,cAAc;AACzC,QAAI,WAAW;AACb,UAAI,CAAC,QAAQ,+BAA+B,yBAAyB,SAAS,GAAG;AAC/E,4BAAoB,WAAW,gBAAgB,SAAS;AAAA,MAC1D;AACA,UAAI,CAAC,QAAQ,gCAAgC,0BAA0B,SAAS,GAAG;AACjF,4BAAoB,WAAW,gBAAgB,SAAS;AAAA,MAC1D;AACA,aAAO,UAAU,SAAS,WAAWA,QAAO,UAAU,gBAAgB;AAAA,IACxE;AACA,QAAI,eAAe,MAAM,6BAA6B,GAAG;AACvD,YAAM,IAAI,WAAW,mEAAmE,iBAAiB,GAAG;AAAA,IAC9G;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,EAAE;AACV,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AACjC,SAAO,MAAM,MAAM,mBAAmB,EAAE,CAAC,EAAE,QAAQ,mBAAmB,GAAG;AAC3E;AAwBA,SAAS,eAAe,WAAW;AACjC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,aAAa,KAAK,SAAS;AAC/B,MAAI,iBAAiB,oBAAI,KAAK,CAAC;AAC/B,iBAAe,YAAY,MAAM,aAAa,GAAG,CAAC;AAClD,iBAAe,SAAS,GAAG,GAAG,GAAG,CAAC;AAClC,SAAO,eAAe,QAAQ;AAChC;AAyBA,SAAS,QAAQ,WAAW,aAAa;AACvC,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,QAAQ,WAAW,CAAC,MAAM;AACnC;AA4BA,SAAS,QAAQ,WAAW;AAC1B,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU,WAAW,KAAK,IAAI,CAAC;AACxC;AA4BA,SAAS,YAAY,WAAW;AAC9B,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU,WAAW,QAAQ,KAAK,IAAI,GAAG,CAAC,CAAC;AACpD;AAyBA,SAAS,SAAS,WAAW,YAAY;AACvC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,QAAQ,UAAU,UAAU;AAChC,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,MAAM,KAAK,QAAQ;AACvB,MAAI,uBAAuB,oBAAI,KAAK,CAAC;AACrC,uBAAqB,YAAY,MAAM,OAAO,EAAE;AAChD,uBAAqB,SAAS,GAAG,GAAG,GAAG,CAAC;AACxC,MAAI,cAAc,eAAe,oBAAoB;AAGrD,OAAK,SAAS,OAAO,KAAK,IAAI,KAAK,WAAW,CAAC;AAC/C,SAAO;AACT;AAyBA,SAAS,QAAQ,WAAW,iBAAiB;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,aAAa,UAAU,eAAe;AAC1C,OAAK,QAAQ,UAAU;AACvB,SAAO;AACT;AAyBA,SAAS,QAAQ,WAAW,WAAW;AACrC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,UAAU,SAAS;AAE9B,MAAI,MAAM,KAAK,QAAQ,CAAC,GAAG;AACzB,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AACA,OAAK,YAAY,IAAI;AACrB,SAAO;AACT;AACA,IAAM,cAAc,CAAC,KAAK,UAAU,UAAU;AAC5C,QAAM,QAAQ,IAAI,IAAI,QAAQ;AAC9B,MAAI,CAAC,OAAO;AACV,QAAI,IAAI,UAAU,CAAC,KAAK,CAAC;AAAA,EAC3B,WAAW,CAAC,MAAM,SAAS,KAAK,GAAG;AACjC,UAAM,KAAK,KAAK;AAAA,EAClB;AACF;AACA,IAAM,WAAW,CAAC,IAAI,OAAO;AAC3B,MAAI;AACJ,SAAO,IAAI,SAAS;AAClB,QAAI,WAAW;AACb,mBAAa,SAAS;AAAA,IACxB;AACA,gBAAY,WAAW,MAAM;AAC3B,kBAAY;AACZ,SAAG,GAAG,IAAI;AAAA,IACZ,GAAG,EAAE;AAAA,EACP;AACF;AAWA,IAAM,cAAc,kBAAgB,EAAE,iBAAiB,iBAAiB,aAAa;AACrF,IAAM,kBAAkB,SAAS,SAAO;AACtC,WAAS,OAAO,IAAI,KAAK,GAAG;AAC1B,QAAI,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,OAAO,WAAW,CAAC;AAAA,EAC/C;AACF,GAAG,GAAI;AACP,IAAM,sBAAsB,MAAM;AAChC,MAAI,OAAO,oBAAoB,YAAY;AAGzC,WAAO,CAAC;AAAA,EACV;AACA,QAAM,eAAe,oBAAI,IAAI;AAC7B,SAAO;AAAA,IACL,SAAS,MAAM,aAAa,MAAM;AAAA,IAClC,KAAK,cAAY;AACf,YAAM,MAAM,gBAAgB;AAC5B,UAAI,KAAK;AACP,oBAAY,cAAc,UAAU,GAAG;AAAA,MACzC;AAAA,IACF;AAAA,IACA,KAAK,cAAY;AACf,YAAM,WAAW,aAAa,IAAI,QAAQ;AAC1C,UAAI,UAAU;AACZ,qBAAa,IAAI,UAAU,SAAS,OAAO,WAAW,CAAC;AAAA,MACzD;AACA,sBAAgB,YAAY;AAAA,IAC9B;AAAA,IACA,OAAO,MAAM;AACX,mBAAa,QAAQ,UAAQ,KAAK,QAAQ,WAAW,CAAC;AACtD,sBAAgB,YAAY;AAAA,IAC9B;AAAA,EACF;AACF;AACA,IAAM,SAAS,SAAO,OAAO,QAAQ,aAAa,IAAI,IAAI;AAC1D,IAAM,sBAAsB,CAAC,cAAc,eAAe,CAAC,GAAG,MAAM,MAAM,MAAM;AAC9E,QAAM,iBAAiB,OAAO,YAAY;AAC1C,MAAI,SAAS,IAAI,IAAI,OAAO,QAAQ,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiB,CAAC,CAAC,CAAC;AAC/G,QAAM,WAAW;AAAA,IACf,SAAS,CAAC;AAAA,IACV,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,IACN,OAAO,CAAC;AAAA,EACV;AACA,QAAM,QAAQ,MAAM;AAClB,QAAI;AAGJ,aAAS,IAAI,IAAI,OAAO,SAAS,KAAK,OAAO,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAC,CAAC;AAChG,aAAS,MAAM,QAAQ,QAAM,GAAG,CAAC;AAAA,EACnC;AACA,QAAM,UAAU,MAAM;AAGpB,aAAS,QAAQ,QAAQ,QAAM,GAAG,CAAC;AACnC,UAAM;AAAA,EACR;AACA,QAAM,MAAM,cAAY;AACtB,aAAS,IAAI,QAAQ,QAAM,GAAG,QAAQ,CAAC;AACvC,WAAO,OAAO,IAAI,QAAQ;AAAA,EAC5B;AACA,QAAM,MAAM,CAAC,UAAU,UAAU;AAC/B,UAAM,WAAW,OAAO,IAAI,QAAQ;AACpC,QAAI,aAAa,OAAO,UAAU,QAAQ,GAAG;AAC3C,aAAO,IAAI,UAAU,KAAK;AAC1B,eAAS,IAAI,QAAQ,QAAM,GAAG,UAAU,OAAO,QAAQ,CAAC;AAAA,IAC1D;AAAA,EACF;AACA,QAAMG,SAAQ,OAAO,UAAU,cAAc,CAAC,IAAI,IAAI,MAAM,gBAAgB;AAAA,IAC1E,IAAI,GAAG,UAAU;AACf,aAAO,IAAI,QAAQ;AAAA,IACrB;AAAA,IACA,QAAQ,GAAG;AACT,aAAO,MAAM,KAAK,OAAO,KAAK,CAAC;AAAA,IACjC;AAAA,IACA,2BAA2B;AACzB,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,IAAI,GAAG,UAAU;AACf,aAAO,OAAO,IAAI,QAAQ;AAAA,IAC5B;AAAA,IACA,IAAI,GAAG,UAAU,OAAO;AACtB,UAAI,UAAU,KAAK;AACnB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,QAAM,KAAK,CAAC,WAAW,aAAa;AAClC,aAAS,SAAS,EAAE,KAAK,QAAQ;AACjC,WAAO,MAAM;AACX,sBAAgB,SAAS,SAAS,GAAG,QAAQ;AAAA,IAC/C;AAAA,EACF;AACA,QAAMC,YAAW,CAAC,UAAU,OAAO;AACjC,UAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,aAAa;AACzC,UAAI,QAAQ,UAAU;AACpB,WAAG,QAAQ;AAAA,MACb;AAAA,IACF,CAAC;AAGD,UAAM,UAAU,GAAG,SAAS,MAAM,GAAG,OAAO,YAAY,EAAE,QAAQ,CAAC,CAAC;AACpE,WAAO,MAAM;AACX,YAAM;AACN,cAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,MAAM,IAAI,kBAAkB;AAChC,UAAM,SAAS,cAAc,OAAO,CAACC,SAAQ,iBAAiB;AAC5D,UAAI,aAAa,KAAK;AACpB,QAAAA,QAAO,KAAK,GAAG,OAAO,aAAa,GAAG,CAAC;AAAA,MACzC;AACA,UAAI,aAAa,KAAK;AACpB,QAAAA,QAAO,KAAK,GAAG,OAAO,aAAa,GAAG,CAAC;AAAA,MACzC;AACA,UAAI,aAAa,OAAO;AACtB,QAAAA,QAAO,KAAK,GAAG,SAAS,aAAa,KAAK,CAAC;AAAA,MAC7C;AACA,UAAI,aAAa,SAAS;AACxB,QAAAA,QAAO,KAAK,GAAG,WAAW,aAAa,OAAO,CAAC;AAAA,MACjD;AACA,aAAOA;AAAA,IACT,GAAG,CAAC,CAAC;AACL,WAAO,MAAM,OAAO,QAAQ,WAAS,MAAM,CAAC;AAAA,EAC9C;AACA,QAAMC,eAAc,SAAO;AACzB,UAAM,WAAW,OAAO,IAAI,GAAG;AAC/B,aAAS,IAAI,QAAQ,QAAM,GAAG,KAAK,UAAU,QAAQ,CAAC;AAAA,EACxD;AACA,SAAO;AAAA,IACL,OAAAH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAE;AAAA,EACF;AACF;AACA,IAAM,kBAAkB,CAAC,OAAO,SAAS;AACvC,QAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,MAAI,SAAS,GAAG;AACd,UAAM,KAAK,IAAI,MAAM,MAAM,SAAS,CAAC;AACrC,UAAM;AAAA,EACR;AACF;AACA,IAAM,cAAc,CAAC,cAAc,iBAAiB;AAClD,QAAM,MAAM,oBAAoB,cAAc,YAAY;AAC1D,MAAI,IAAI,oBAAoB,CAAC;AAC7B,SAAO;AACT;AAIA,SAAS,cAAc,eAAe,gBAAgB,SAAS;AAC7D,eAAa,GAAG,SAAS;AACzB,MAAI,sBAAsB,eAAe,eAAe,OAAO;AAC/D,MAAI,uBAAuB,eAAe,gBAAgB,OAAO;AACjE,SAAO,oBAAoB,QAAQ,MAAM,qBAAqB,QAAQ;AACxE;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,OAAO,OAAO,SAAS;AAC/C,YAAU,WAAW,CAAC;AACtB,MAAI,SAAS,uBAAuB,KAAK;AAEzC,MAAI;AACJ,MAAI,OAAO,OAAO,UAAU,UAAU;AACpC,kBAAc;AAAA,EAChB,WAAW,UAAU,GAAG;AACtB,kBAAc;AAAA,EAChB,WAAW,QAAQ,KAAK,QAAQ,GAAG;AACjC,kBAAc;AAAA,EAChB,OAAO;AACL,kBAAc;AAAA,EAChB;AAEA,MAAI,cAAc,QAAQ,cAAc;AACxC,MAAI,aAAa,QAAQ;AACzB,MAAI;AACJ,MAAI,eAAe,eAAe,IAAI;AACpC,gBAAY;AAAA,EACd,WAAW,eAAe,eAAe,GAAG;AAC1C,gBAAY;AAAA,EACd,OAAO;AACL,gBAAY;AAAA,EACd;AACA,SAAO,OAAO,WAAW,EAAE,SAAS,EAAE,QAAQ,aAAa,KAAK;AAClE;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,qBAAqB,CAAC,UAAU,WAAW,SAAS,UAAU,WAAW,SAAS,QAAQ;AAC9F,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU,SAAU,MAAM,WAAW,UAAU;AAC7C,QAAI,MAAM,KAAK,UAAU;AACzB,WAAO,QAAQ,mBAAmB,GAAG,IAAI;AAAA,EAC3C;AAAA,EACA,OAAO;AACT;AACA,SAAS,iBAAiB,OAAO,MAAM,UAAU,SAAS;AACxD,MAAIP,UAAS,uBAAuB,KAAK;AACzC,MAAI,OAAOA,YAAW,YAAY;AAChC,WAAOA,QAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AACA,SAAOA;AACT;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,aAAa,OAAO;AAAA,EAC7B,aAAa,CAAC,aAAa,OAAO;AAAA,EAClC,MAAM,CAAC,yBAAyB,kBAAkB;AACpD;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AAAA,EAC5E,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AACvE;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,SAAS,QAAQ,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,QAAQ,SAAS,YAAY,UAAU;AAC7H;AACA,IAAI,0BAA0B;AAAA,EAC5B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,SAAS,SAAS,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,QAAQ,SAAS,aAAa,UAAU;AAC/H;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACjD,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,UAAU,WAAW,SAAS,UAAU,WAAW,SAAS,QAAQ;AAC7E;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,aAAa;AACpC,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,WAAW,UAAU;AAC7B;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,UAAU,OAAO,OAAO,OAAO,UAAU,UAAU,OAAO,OAAO,UAAU,OAAO,KAAK;AAAA,EACvG,KAAK,CAAC,SAAS,WAAW,YAAY,SAAS,YAAY,sCAAsC,6BAA6B,SAAS,eAAe,eAAe,SAAS,OAAO;AACvL;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,UAAU,OAAO,UAAU,OAAO,KAAK;AAAA,EAC9D,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,OAAO,MAAM;AAChE;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAaA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,EACnB;AAAA,EACA,kBAAkB;AAAA,IAChB,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa;AAAA,IACX,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,MACf,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,SAAS;AACtD,MAAI;AACJ,MAAI,aAAa,YAAY,QAAQ,YAAY,UAAU,QAAQ,YAAY,uBAAuB,KAAK,EAAE,kBAAkB,uBAAuB,KAAK,EAAE;AAC7J,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAC9D;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA;AAAA,EAEN,QAAQ;AAAA;AAAA,EAER,OAAO;AAAA;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,WAAW,UAAU;AAClE,SAAO,uBAAuB,KAAK;AACrC;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,UAAU,QAAQ;AAAA,EAC3B,aAAa,CAAC,UAAU,QAAQ;AAAA,EAChC,MAAM,CAAC,gBAAgB,eAAe;AACxC;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,cAAc,cAAc,cAAc,YAAY;AAC/D;AAKA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,UAAU,WAAW,QAAQ,SAAS,OAAO,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,UAAU;AAC9H;AAEA,IAAI,wBAAwB;AAAA,EAC1B,QAAQ,cAAc;AAAA,EACtB,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EAC3G,MAAM,cAAc;AACtB;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,WAAW,UAAU,YAAY,YAAY,cAAc,WAAW,SAAS;AACxF;AAEA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,SAAU,aAAa;AAC3C,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,OAAO,KAAK;AACpB;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,WAAW,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AACxG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,MAAM;AAC7D;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA;AAAA,IAEX,SAAS;AAAA,IACT,OAAO;AAAA;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAcA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,SAAS;AACtD,MAAI;AACJ,MAAI,aAAa,uBAAuB,KAAK;AAC7C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,MAAM,SAAS,CAAC;AAAA,EACjE;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,6BAA6B;AAAA,EAC/B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,mBAAmB,SAAU,OAAO,MAAM,WAAW,UAAU;AACjE,MAAI,KAAK,YAAY,MAAM,GAAG;AAC5B,WAAO,2BAA2B,KAAK;AAAA,EACzC,OAAO;AACL,WAAO,uBAAuB,KAAK;AAAA,EACrC;AACF;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,MAAM,IAAI;AAAA,EACnB,aAAa,CAAC,MAAM,IAAI;AAAA,EACxB,MAAM,CAAC,mBAAmB,mBAAmB;AAC/C;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AACvE;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,SAAS,WAAW,SAAS,SAAS,QAAQ,SAAS,SAAS,UAAU,cAAc,WAAW,aAAa,WAAW;AACpI;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,WAAW,SAAS,UAAU,aAAa,UAAU,WAAW,QAAQ;AACjF;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,SAAU,aAAa,UAAU;AACrD,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,QAAQ,MAAM;AAAA,EACpB,MAAM,CAAC,gDAAgD,uCAAuC;AAChG;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AACjH;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAC9D;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAcA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,SAAS;AACtD,MAAI;AACJ,MAAI,OAAO,uBAAuB,KAAK;AACvC,MAAI,OAAO,SAAS,UAAU;AAC5B,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,KAAK;AAAA,EAChB,OAAO;AACL,aAAS,KAAK,MAAM,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EACxD;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,YAAY;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,WAAW,UAAU;AAClE,SAAO,uBAAuB,KAAK;AACrC;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,YAAY,UAAU;AAAA,EAC/B,aAAa,CAAC,YAAY,UAAU;AAAA,EACpC,MAAM,CAAC,sBAAsB,oBAAoB;AACnD;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EAC/B,aAAa,CAAC,aAAa,cAAc,cAAc,YAAY;AAAA,EACnE,MAAM,CAAC,iBAAiB,kBAAkB,kBAAkB,gBAAgB;AAC9E;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,SAAS,SAAS,QAAQ,QAAQ,OAAO,QAAQ,SAAS,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,EAC/G,MAAM,CAAC,WAAW,WAAW,QAAQ,SAAS,OAAO,QAAQ,WAAW,QAAQ,aAAa,WAAW,YAAY,UAAU;AAChI;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACpE,MAAM,CAAC,YAAY,SAAS,SAAS,YAAY,SAAS,YAAY,QAAQ;AAChF;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,SAAU,aAAa,SAAS;AACpD,MAAI,SAAS,OAAO,WAAW;AAC/B,MAAI,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACrE,MAAI,WAAW,EAAG,QAAO;AACzB,MAAI,gBAAgB,CAAC,QAAQ,QAAQ,QAAQ,UAAU,QAAQ;AAC/D,MAAI;AACJ,MAAI,WAAW,GAAG;AAChB,aAAS,QAAQ,cAAc,SAAS,IAAI,IAAI,QAAQ;AAAA,EAC1D,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,QAAQ,MAAM;AACtB;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,OAAO,SAAS,QAAQ,QAAQ,UAAU,UAAU,QAAQ,OAAO,OAAO,OAAO,KAAK;AACtG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAC9D;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAYA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,SAAS;AACtD,MAAI;AACJ,MAAI,aAAa,uBAAuB,KAAK;AAC7C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,MAAM,SAAS,CAAC;AAAA,EACjE;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,WAAW,CAAC,YAAY,UAAU,WAAW,aAAa,WAAW,WAAW,QAAQ;AAC5F,SAAS,SAAS,KAAK;AACrB,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO,MAAM,SAAS,GAAG,IAAI;AAAA,EACjC;AACF;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,MAAM,SAAS,GAAG,IAAI;AAC/B;AACA,SAAS,SAAS,KAAK;AACrB,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO,MAAM,SAAS,GAAG,IAAI;AAAA,EACjC;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU,SAAU,MAAM,UAAU,SAAS;AAC3C,QAAI,MAAM,KAAK,UAAU;AACzB,QAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU,SAAU,MAAM,UAAU,SAAS;AAC3C,QAAI,MAAM,KAAK,UAAU;AACzB,QAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,aAAO,SAAS,GAAG;AAAA,IACrB,OAAO;AACL,aAAO,SAAS,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACT;AACA,IAAI,mBAAmB,SAAU,OAAO,MAAM,UAAU,SAAS;AAC/D,MAAIA,UAAS,uBAAuB,KAAK;AACzC,MAAI,OAAOA,YAAW,YAAY;AAChC,WAAOA,QAAO,MAAM,UAAU,OAAO;AAAA,EACvC;AACA,SAAOA;AACT;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,MAAM,IAAI;AAAA,EACnB,aAAa,CAAC,QAAQ,MAAM;AAAA,EAC5B,MAAM,CAAC,iBAAiB,aAAa;AACvC;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AACvE;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,WAAW,YAAY,SAAS,UAAU,UAAU,UAAU,UAAU,UAAU,aAAa,WAAW,YAAY,UAAU;AACzI;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACvD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,YAAY,UAAU,WAAW,aAAa,WAAW,WAAW,QAAQ;AACrF;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,SAAU,aAAa,UAAU;AACrD,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,UAAU;AAAA,IACnB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,OAAO,SAAS;AACxB;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,OAAO,SAAS,QAAQ,SAAS,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK;AAClG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,OAAO,OAAO,QAAQ,QAAQ,OAAO,OAAO,KAAK;AACzD;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAaA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,SAAS,gBAAgB,QAAQ,OAAO;AACtC,MAAI,UAAU,GAAG;AACf,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,SAAS,QAAQ;AAErB,MAAI,UAAU,MAAM,SAAS,IAAI;AAC/B,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,QAAQ,SAAS;AAErB,MAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,OAAO;AAChB;AACA,SAAS,WAAW,QAAQ,OAAO,MAAM;AACvC,SAAO,QAAQ;AACf,MAAI,QAAQ,gBAAgB,QAAQ,KAAK;AACzC,MAAI,YAAY,MAAM,IAAI,KAAK;AAC/B,SAAO,UAAU,QAAQ,aAAa,KAAK;AAC7C;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,MACH,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,OAAO,OAAO,SAAS;AAC/C,YAAU,WAAW,CAAC;AACtB,MAAI,SAAS,uBAAuB,KAAK;AACzC,MAAI,CAAC,QAAQ,WAAW;AACtB,WAAO,WAAW,QAAQ,KAAK;AAAA,EACjC;AACA,MAAI,QAAQ,aAAa,GAAG;AAC1B,WAAO,QAAQ,WAAW,QAAQ,OAAO,QAAQ;AAAA,EACnD,OAAO;AACL,WAAO,WAAW,QAAQ,OAAO,MAAM,IAAI;AAAA,EAC7C;AACF;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,qBAAqB;AAAA,EACvB,WAAW;AAAA,EACX,UAAU;AACZ;AACA,IAAI,qBAAqB;AAAA,EACvB,WAAW;AAAA,EACX,UAAU;AACZ;AACA,IAAI,qBAAqB;AAAA,EACvB,WAAW;AAAA,EACX,UAAU;AACZ;AACA,IAAI,uBAAuB;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,SAAS,cAAc,OAAO,MAAM,UAAU,SAAS;AACrD,MAAI,cAAc,MAAM,UAAU,OAAO,GAAG;AAC1C,WAAO;AAAA,EACT,WAAW,UAAU,YAAY;AAC/B,WAAO;AAAA,EACT,WAAW,UAAU,YAAY;AAC/B,WAAO;AAAA,EACT,OAAO;AACL,UAAM,IAAI,MAAM,yCAAyC,OAAO,KAAK,CAAC;AAAA,EACxE;AACF;AACA,SAAS,aAAa,OAAO,MAAM,UAAU,SAAS;AACpD,MAAI,MAAM,KAAK,UAAU;AACzB,MAAI,aAAa,cAAc,OAAO,MAAM,UAAU,OAAO;AAC7D,MAAI,oBAAoB,qBAAqB,GAAG;AAChD,SAAO,WAAW,iBAAiB;AACrC;AACA,SAAS,wBAAwB,OAAO,MAAM,UAAU,SAAS;AAC/D,MAAI,YAAY,aAAa,OAAO,MAAM,UAAU,OAAO;AAC3D,SAAO,IAAI,OAAO,WAAW,cAAc;AAC7C;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,SAAS,iBAAiB,OAAO,MAAM,UAAU,SAAS;AACxD,MAAIA,UAAS,uBAAuB,KAAK;AACzC,MAAI,OAAOA,YAAW,YAAY;AAChC,WAAOA,QAAO,OAAO,MAAM,UAAU,OAAO;AAAA,EAC9C;AACA,SAAOA;AACT;AACA,SAAS,gBAAgB,aAAa;AACpC,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,OAAO,MAAM;AACtB;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,UAAU,MAAM;AAAA,EACzB,aAAa,CAAC,UAAU,MAAM;AAAA,EAC9B,MAAM,CAAC,mBAAmB,YAAY;AACxC;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,SAAS,UAAU,WAAW,QAAQ;AAAA,EACpD,MAAM,CAAC,aAAa,cAAc,eAAe,YAAY;AAC/D;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,WAAW,QAAQ,UAAU,YAAY,OAAO,YAAY,UAAU,YAAY,YAAY,eAAe,YAAY,UAAU;AAC5I;AACA,IAAI,wBAAwB;AAAA,EAC1B,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,YAAY,UAAU,SAAS,YAAY,QAAQ,WAAW,SAAS,YAAY,YAAY,gBAAgB,aAAa,SAAS;AAC9I;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACvD,aAAa,CAAC,UAAU,QAAQ,OAAO,OAAO,QAAQ,OAAO,MAAM;AAAA,EACnE,MAAM,CAAC,aAAa,gBAAgB,UAAU,SAAS,YAAY,UAAU,QAAQ;AACvF;AACA,IAAI,sBAAsB;AAAA,EACxB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACvD,aAAa,CAAC,UAAU,QAAQ,OAAO,OAAO,QAAQ,OAAO,MAAM;AAAA,EACnE,MAAM,CAAC,aAAa,gBAAgB,UAAU,SAAS,YAAY,UAAU,QAAQ;AACvF;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,OAAO,KAAK;AACpB;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EAC/B,KAAK,CAAC,UAAU,WAAW,YAAY,SAAS;AAClD;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,QAAQ,SAAS,OAAO,SAAS,OAAO,SAAS,QAAQ,OAAO,OAAO,SAAS,KAAK;AACrG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,aAAa,CAAC,OAAO,QAAQ,OAAO,YAAY,OAAO,QAAQ,MAAM;AAAA,EACrE,KAAK,CAAC,OAAO,QAAQ,OAAO,YAAY,OAAO,QAAQ,MAAM;AAC/D;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAcA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB,OAAO,OAAO,SAAS;AAC/C,YAAU,WAAW,CAAC;AACtB,MAAI;AACJ,MAAI,OAAO,uBAAuB,KAAK,MAAM,UAAU;AACrD,aAAS,uBAAuB,KAAK;AAAA,EACvC,WAAW,UAAU,GAAG;AACtB,aAAS,uBAAuB,KAAK,EAAE;AAAA,EACzC,OAAO;AACL,aAAS,uBAAuB,KAAK,EAAE,MAAM,QAAQ,aAAa,KAAK;AAAA,EACzE;AACA,MAAI,QAAQ,WAAW;AACrB,QAAI,QAAQ,aAAa,GAAG;AAC1B,aAAO,aAAa;AAAA,IACtB,OAAO;AACL,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,SAAS,iBAAiB,OAAO,OAAO,WAAW,UAAU;AAC3D,SAAO,uBAAuB,KAAK;AACrC;AACA,SAAS,gBAAgB,aAAa;AACpC,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,MAAM,IAAI;AAAA,EACnB,aAAa,CAAC,QAAQ,MAAM;AAAA,EAC5B,MAAM,CAAC,mBAAmB,kBAAkB;AAC9C;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc;AACvE;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,WAAW,aAAa,SAAS,SAAS,QAAQ,SAAS,SAAS,UAAU,YAAY,WAAW,YAAY,UAAU;AACpI;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACvD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,WAAW,iBAAiB,eAAe,gBAAgB,gBAAgB,eAAe,QAAQ;AAC3G;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,QAAQ,MAAM;AAAA,EACpB,MAAM,CAAC,0CAA0C,gCAAgC;AACnF;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAC9B;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,QAAQ,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK;AACrG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,OAAO,SAAS,OAAO,SAAS,SAAS,SAAS,SAAS;AACnE;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAYA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,yBAAyB;AAAA,EAC3B,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,SAAS;AACtD,MAAI;AACJ,MAAI,aAAa,uBAAuB,KAAK;AAC7C,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS,WAAW;AAAA,EACtB,OAAO;AACL,aAAS,WAAW,MAAM,QAAQ,aAAa,MAAM,SAAS,CAAC;AAAA,EACjE;AACA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,QAAI,QAAQ,cAAc,QAAQ,aAAa,GAAG;AAChD,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,oBAAoB;AAAA,EACtB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,IAAI,yBAAyB;AAAA,EAC3B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAI,mBAAmB,SAAU,OAAO,OAAO,WAAW,UAAU;AAClE,SAAO,uBAAuB,KAAK;AACrC;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,MAAM,IAAI;AAAA,EACnB,aAAa,CAAC,MAAM,IAAI;AAAA,EACxB,MAAM,CAAC,iBAAiB,gBAAgB;AAC1C;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EACpC,MAAM,CAAC,cAAc,iBAAiB,iBAAiB,YAAY;AACrE;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACnE,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAChG,MAAM,CAAC,QAAQ,SAAS,QAAQ,SAAS,SAAS,WAAW,UAAU,WAAW,SAAS,QAAQ,SAAS,QAAQ;AACtH;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EAChD,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC7D,MAAM,CAAC,SAAS,aAAa,QAAQ,YAAY,YAAY,QAAQ,WAAW;AAClF;AACA,IAAI,oBAAoB;AAAA,EACtB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,8BAA8B;AAAA,EAChC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,kBAAkB,SAAU,aAAa,UAAU;AACrD,MAAI,SAAS,OAAO,WAAW;AAC/B,SAAO,SAAS;AAClB;AACA,IAAI,aAAa;AAAA,EACf,eAAe;AAAA,EACf,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,8BAA8B;AAClC,IAAI,8BAA8B;AAClC,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,KAAK,CAAC,yBAAyB,wBAAwB;AACzD;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,EAC5B,aAAa,CAAC,OAAO,OAAO,OAAO,KAAK;AAAA,EACxC,MAAM,CAAC,oBAAoB,sBAAsB,kBAAkB,aAAa;AAClF;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EAC3F,KAAK,CAAC,OAAO,OAAO,SAAS,OAAO,SAAS,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,MAAM;AAClG;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,EACxD,KAAK,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EAC5D,MAAM,CAAC,WAAW,eAAe,UAAU,cAAc,cAAc,UAAU,YAAY;AAC/F;AACA,IAAI,2BAA2B;AAAA,EAC7B,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,2BAA2B;AAAA,EAC7B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,UAAU;AAAA,EACZ,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAiBA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,uBAAuB;AAAA,EACzB,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,kBAAkB;AAAA,IAChB,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,KAAK;AAAA,IACL,OAAO;AAAA,EACT;AACF;AACA,SAAS,eAAe,OAAO,OAAO,SAAS;AAC7C,YAAU,WAAW,CAAC;AACtB,MAAI;AACJ,MAAI,OAAO,qBAAqB,KAAK,MAAM,UAAU;AACnD,aAAS,qBAAqB,KAAK;AAAA,EACrC,WAAW,UAAU,GAAG;AACtB,aAAS,qBAAqB,KAAK,EAAE;AAAA,EACvC,OAAO;AACL,aAAS,qBAAqB,KAAK,EAAE,MAAM,QAAQ,aAAa,KAAK;AAAA,EACvE;AACA,MAAI,QAAQ,WAAW;AACrB,QAAI,QAAQ,aAAa,GAAG;AAC1B,aAAO,SAAS;AAAA,IAClB,OAAO;AACL,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,kBAAkB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,MAAM,kBAAkB;AAAA,IACtB,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,UAAU,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,cAAc;AAAA,EAChB,CAAC;AACH;AACA,SAAS,UAAU,OAAO,WAAW,UAAU,YAAY;AACzD,MAAI,cAAc,OAAO,WAAW,QAAQ,GAAG;AAC7C,WAAO;AAAA,EACT,WAAW,MAAM,QAAQ,IAAI,UAAU,QAAQ,GAAG;AAChD,WAAO,SAAS;AAAA,EAClB;AACA,SAAO,SAAS;AAClB;AACA,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA;AAAA,EAEV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA;AAAA,EAEV,OAAO;AACT;AACA,SAAS,eAAe,OAAO,OAAO,WAAW,UAAU;AACzD,MAAIA,UAAS,qBAAqB,KAAK;AACvC,MAAI,OAAOA,YAAW,YAAY;AAChC,WAAOA,QAAO,OAAO,WAAW,UAAU,QAAQ;AAAA,EACpD;AACA,SAAOA;AACT;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,IAAI;AAAA,EAClB,aAAa,CAAC,KAAK,IAAI;AAAA,EACvB,MAAM,CAAC,OAAO,IAAI;AACpB;AACA,IAAI,gBAAgB;AAAA,EAClB,QAAQ,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC3B,aAAa,CAAC,OAAO,OAAO,OAAO,KAAK;AAAA,EACxC,MAAM,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AACvC;AACA,IAAI,cAAc;AAAA,EAChB,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI;AAAA,EACrE,aAAa,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK;AAAA,EACvF,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,KAAK;AACjF;AACA,IAAI,YAAY;AAAA,EACd,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAC1C,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EACzC,aAAa,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,EACtD,MAAM,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AACxD;AACA,IAAI,kBAAkB;AAAA,EACpB,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,4BAA4B;AAAA,EAC9B,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,aAAa,cAAc;AAUhD,MAAI,SAAS,OAAO,WAAW;AAC/B,MAAI,UAAU,gBAAgB,CAAC;AAC/B,MAAI,OAAO,OAAO,QAAQ,IAAI;AAC9B,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B,KAAK;AACH,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B,KAAK;AACH,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B,KAAK;AACH,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B;AACE,aAAO,OAAO,OAAO,SAAS;AAAA,EAClC;AACF;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,SAAS,gBAAgB;AAAA,IACvB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB,SAAU,SAAS;AACnC,aAAO,OAAO,OAAO,IAAI;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,OAAO,gBAAgB;AAAA,IACrB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,KAAK,gBAAgB;AAAA,IACnB,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB,CAAC;AAAA,EACD,WAAW,gBAAgB;AAAA,IACzB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AACA,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,SAAS,QAAQ;AACzB;AACA,IAAI,uBAAuB;AAAA,EACzB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,uBAAuB;AAAA,EACzB,KAAK,CAAC,UAAU,UAAU,UAAU,QAAQ;AAC9C;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,gBAAgB,QAAQ,MAAM;AAAA,EACtG,KAAK,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,mBAAmB,WAAW,SAAS;AAChI;AACA,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,mBAAmB;AAAA,EACrB,KAAK,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAChD;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AACP;AACA,IAAI,yBAAyB;AAAA,EAC3B,KAAK;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,IAAI,QAAQ;AAAA,EACV,eAAe,oBAAoB;AAAA,IACjC,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe,SAAU,OAAO;AAC9B,aAAO,SAAS,OAAO,EAAE;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,SAAS,aAAa;AAAA,IACpB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe,SAAU,OAAO;AAC9B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,CAAC;AAAA,EACD,OAAO,aAAa;AAAA,IAClB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,KAAK,aAAa;AAAA,IAChB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAAA,EACD,WAAW,aAAa;AAAA,IACtB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AACH;AAeA,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,IACP,cAAc;AAAA,IAGd,uBAAuB;AAAA,EACzB;AACF;AAKA,IAAM,iBAAiB;AAAA,EACrB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AACd;AACA,IAAM,aAAa;AAAA,EACjB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,eAAe;AAAA,EACnB,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,gBAAgB;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,0BAA0B;AAAA,IACxB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,4BAA4B;AAAA,IAC1B,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,kBAAkB;AAAA,IAChB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,gBAAgB;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,gBAAgB;AAAA,IACd,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,iBAAiB;AAAA,IACf,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,eAAe;AAAA,IACb,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,aAAa;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,SAAS;AAAA,IACP,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,kBAAkB;AAAA,IAChB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,YAAY;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,kBAAkB;AAAA,IAChB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,MAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,mBAAmB;AAAA,IACjB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,WAAW;AAAA,IACT,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,iBAAiB;AAAA,IACf,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,iBAAiB;AAAA,IACf,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,cAAc;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACF;AAKA,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI,YAAY;AAAA,EACd,UAAU,eAAe;AAAA,EACzB,YAAY,eAAe;AAAA,EAC3B,YAAY,eAAe;AAAA,EAC3B,cAAc,CAAC;AAAA,EACf,wBAAwB;AAAA,EACxB,YAAY;AACd,CAAC;AAED,SAAS,uBAAuB,UAAU;AACxC,WAAS,YAAY,MAAM,SAAS,CAAC;AACvC;AACA,SAAe,YAAY;AAAA;AACzB,QAAI,MAAM,YAAY;AACpB;AAAA,IACF;AACA,4BAAwB;AACxB,WAAO,QAAQ,IAAI,CAAC,YAAY,GAAG,cAAc,GAAG,cAAc,CAAC,CAAC,EAAE,KAAK,CAAO,OAAuC,eAAvC,KAAuC,WAAvC,CAAC,UAAU,YAAY,UAAU,GAAM;AACvH,YAAM,aAAa;AACnB,YAAM,WAAW;AACjB,YAAM,aAAa;AACnB,YAAM,aAAa;AACnB;AAAA,IACF,EAAC;AAAA,EACH;AAAA;AAKA,SAAe,cAAc;AAAA;AAC3B,QAAI,WAAW,SAAS,gBAAgB,aAAa,MAAM;AAC3D,QAAI,CAAC,YAAY,aAAa,aAAa;AACzC,UAAI;AACF,mBAAW,MAAM,iBAAiB,EAAE,kBAAkB,qBAAqB;AAAA,MAC7E,SAAS,IAAI;AACX,mBAAW,eAAe;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAKA,SAAe,gBAAgB;AAAA;AAC7B,QAAI;AACF,aAAO,MAAM,iBAAiB,EAAE,kBAAkB,uBAAuB;AAAA,IAC3E,SAAS,IAAI;AACX,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAAA;AAKA,SAAe,gBAAgB;AAAA;AAC7B,QAAI;AACF,aAAO,MAAM,iBAAiB,EAAE,kBAAkB,uBAAuB;AAAA,IAC3E,SAAS,IAAI;AACX,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAAA;AACA,SAAS,0BAA0B;AACjC,MAAI,MAAM,wBAAwB;AAChC;AAAA,EACF;AACA,QAAM,WAAW,IAAI,iBAAiB,eAAa;AAEjD,QAAI,UAAU,OAAO,UAAQ,KAAK,kBAAkB,MAAM,EAAE,SAAS,GAAG;AACtE,YAAM,WAAW,SAAS,gBAAgB,aAAa,MAAM;AAAA,IAC/D;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,SAAS,iBAAiB;AAAA,IACzC,YAAY;AAAA,EACd,CAAC;AACD,QAAM,yBAAyB;AACjC;AACA,SAAS,mBAAmB;AAC1B,SAAO,OAAO,WAAW;AAC3B;AAYA,SAAS,UAAU,KAAK;AACtB,MAAI;AACF,QAAI,aAAa,GAAG,GAAG;AACrB,aAAO,aAAa,GAAG,EAAE,MAAM,QAAQ,KAAK,aAAa,GAAG,EAAE,IAAI,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT,SAAS,IAAI;AACX,WAAO;AAAA,EACT;AACF;AAMA,SAAS,WAAW,WAAW,YAAY,YAAY;AACrD,MAAI;AACF,WAAO,OAAO,IAAI,KAAK,SAAS,IAAI,cAAc,MAAM,cAAc,OAAO,cAAc,MAAM,WAAW;AAAA,EAC9G,SAAS,IAAI;AACX,WAAO,OAAO,IAAI,KAAK,SAAS,GAAG,eAAe,aAAa,MAAM,eAAe,UAAU;AAAA,EAChG;AACF;AAMA,SAAS,sBAAsB,WAAW,aAAa,eAAe,YAAY;AAChF,MAAI,CAAC,WAAW;AACd;AAAA,EACF;AACA,MAAI;AACF,UAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,QAAI,QAAQ,IAAI,GAAG;AACjB,aAAO,UAAU,OAAO;AAAA,IAC1B,WAAW,YAAY,IAAI,GAAG;AAC5B,aAAO,UAAU,WAAW;AAAA,IAC9B,WAAW,iBAAiB,oBAAI,KAAK,GAAG,IAAI,IAAI,GAAG;AACjD,aAAO,UAAU,MAAM,IAAI,MAAM,OAAO,IAAI,KAAK,SAAS,GAAG,QAAQ;AAAA,QACnE,QAAQ,WAAW,MAAM,YAAY,eAAe,QAAQ;AAAA,MAC9D,CAAC;AAAA,IACH;AACA,WAAO,OAAO,IAAI,KAAK,SAAS,GAAG,cAAc,MAAM,UAAU;AAAA,EACnE,SAAS,IAAI;AACX,WAAO,OAAO,IAAI,KAAK,SAAS,GAAG,eAAe,UAAU;AAAA,EAC9D;AACF;AAIA,SAAS,YAAY;AACnB,QAAM,gBAAgB;AACtB,SAAO;AAAA,IACL,MAAM,CAAC,KAAK,WAAW;AACrB,eAAS,QAAQ,IAAI,GAAG,aAAa,IAAI,GAAG,IAAI,MAAM,IAAI,QAAQ,IAAI,GAAG,aAAa,IAAI,GAAG,EAAE;AAAA,IACjG;AAAA,IACA,MAAM,CAAC,KAAK,WAAW;AACrB,eAAS,QAAQ,KAAK,GAAG,aAAa,IAAI,GAAG,IAAI,MAAM,IAAI,QAAQ,KAAK,GAAG,aAAa,IAAI,GAAG,EAAE;AAAA,IACnG;AAAA,IACA,OAAO,CAAC,KAAK,WAAW;AACtB,eAAS,QAAQ,MAAM,GAAG,aAAa,IAAI,GAAG,IAAI,MAAM,IAAI,QAAQ,MAAM,GAAG,aAAa,IAAI,GAAG,EAAE;AAAA,IACrG;AAAA,EACF;AACF;AAQA,SAAS,WAAW,cAAc,MAAM,OAAO,MAAM,MAAM;AACzD,iBAAe,QAAQ,cAAc,IAAI;AACzC,iBAAe,SAAS,cAAc,KAAK;AAC3C,MAAI,QAAQ,MAAM;AAChB,mBAAe,QAAQ,cAAc,GAAG;AAAA,EAC1C;AACA,SAAO;AACT;AAKA,SAAS,aAAa,OAAO;AAC3B,SAAO,CAAC,EAAE,OAAO,MAAM,CAAC,GAAG,KAAK;AAClC;AAIA,SAAS,mBAAmB;AAC1B,QAAM,eAAe,OAAO;AAC5B,MAAI,eAAe,KAAK;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,YAAY;AAEzC,SAAO,WAAW,SAAS,IAAI;AACjC;AAEA,SAAS,cAAc,YAAY;AACjC,SAAO,WAAW,SAAS,KAAK;AAClC;AACA,SAAS,IAAI,UAAU;AACrB,SAAO,SAAU,OAAO;AACtB,aAAS,KAAK;AACd,WAAO;AAAA,EACT;AACF;", "names": ["format", "locale", "localize", "formatLong", "state", "onChange", "unsubs", "forceUpdate"]}