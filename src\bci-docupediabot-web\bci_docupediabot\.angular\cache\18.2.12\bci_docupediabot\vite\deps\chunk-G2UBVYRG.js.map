{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/tooltip.mjs", "../../../../../../node_modules/@angular/material/fesm2022/paginator.mjs"], "sourcesContent": ["import { takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injector, ElementRef, afterNextRender, Directive, Inject, Optional, Input, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { DOCUMENT, NgClass, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst _c0 = [\"tooltip\"];\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition({\n      scrollThrottle: SCROLL_THROTTLE_MS\n    });\n  }\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  get positionAtOrigin() {\n    return this._positionAtOrigin;\n  }\n  set positionAtOrigin(value) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n  /** Disables the display of the tooltip. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    const isDisabled = coerceBooleanProperty(value);\n    if (this._disabled !== isDisabled) {\n      this._disabled = isDisabled;\n      // If tooltip is disabled, hide immediately.\n      if (isDisabled) {\n        this.hide(0);\n      } else {\n        this._setupPointerEnterEventsIfNeeded();\n      }\n      this._syncAriaDescription(this.message);\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n  get showDelay() {\n    return this._showDelay;\n  }\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  get hideDelay() {\n    return this._hideDelay;\n  }\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  /** The message to be displayed in the tooltip */\n  get message() {\n    return this._message;\n  }\n  set message(value) {\n    const oldMessage = this._message;\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n    }\n    this._syncAriaDescription(oldMessage);\n  }\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n  constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n    this._overlay = _overlay;\n    this._elementRef = _elementRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewContainerRef = _viewContainerRef;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._ariaDescriber = _ariaDescriber;\n    this._focusMonitor = _focusMonitor;\n    this._dir = _dir;\n    this._defaultOptions = _defaultOptions;\n    this._position = 'below';\n    this._positionAtOrigin = false;\n    this._disabled = false;\n    this._viewInitialized = false;\n    this._pointerExitEventsInitialized = false;\n    this._tooltipComponent = TooltipComponent;\n    this._viewportMargin = 8;\n    this._cssClassPrefix = 'mat-mdc';\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n    this.touchGestures = 'auto';\n    this._message = '';\n    /** Manually-bound passive event listeners. */\n    this._passiveListeners = [];\n    /** Timer started at the last `touchstart` event. */\n    this._touchstartTimeout = null;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    this._injector = inject(Injector);\n    this._scrollStrategy = scrollStrategy;\n    this._document = _document;\n    if (_defaultOptions) {\n      this._showDelay = _defaultOptions.showDelay;\n      this._hideDelay = _defaultOptions.hideDelay;\n      if (_defaultOptions.position) {\n        this.position = _defaultOptions.position;\n      }\n      if (_defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n      }\n      if (_defaultOptions.touchGestures) {\n        this.touchGestures = _defaultOptions.touchGestures;\n      }\n      if (_defaultOptions.tooltipClass) {\n        this.tooltipClass = _defaultOptions.tooltipClass;\n      }\n    }\n    _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n      }\n    });\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    // Optimization: Do not call clearTimeout unless there is an active timer.\n    if (this._touchstartTimeout) {\n      clearTimeout(this._touchstartTimeout);\n    }\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay = this.showDelay, origin) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n  toggle(origin) {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n  _createOverlay(origin) {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig().positionStrategy;\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n      this._detach();\n    }\n    const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = this._overlay.position().flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = this._overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._scrollStrategy()\n    });\n    this._updatePosition(this._overlayRef);\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n    position.withPositions([this._addOffset({\n      ...origin.main,\n      ...overlay.main\n    }), this._addOffset({\n      ...origin.fallback,\n      ...overlay.fallback\n    })]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  _addOffset(position) {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n      afterNextRender(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  /** Updates the tooltip class */\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition;\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    }\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', event => {\n        this._setupPointerExitEventsIfNeeded();\n        let point = undefined;\n        if (event.x !== undefined && event.y !== undefined) {\n          point = event;\n        }\n        this.show(undefined, point);\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      this._passiveListeners.push(['touchstart', event => {\n        const touch = event.targetTouches?.[0];\n        const origin = touch ? {\n          x: touch.clientX,\n          y: touch.clientY\n        } : undefined;\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        const DEFAULT_LONGPRESS_DELAY = 500;\n        this._touchstartTimeout = setTimeout(() => {\n          this._touchstartTimeout = null;\n          this.show(undefined, origin);\n        }, this._defaultOptions.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n      }]);\n    }\n    this._addListeners(this._passiveListeners);\n  }\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        const newTarget = event.relatedTarget;\n        if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        this.hide(this._defaultOptions.touchendHideDelay);\n      };\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      }\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n  /** Updates the tooltip's ARIA description based on it current state. */\n  _syncAriaDescription(oldMessage) {\n    if (this._ariaDescriptionPending) {\n      return;\n    }\n    this._ariaDescriptionPending = true;\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n    this._ngZone.runOutsideAngular(() => {\n      // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n      // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n      // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n      // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n      Promise.resolve().then(() => {\n        this._ariaDescriptionPending = false;\n        if (this.message && !this.disabled) {\n          this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function MatTooltip_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatTooltip)(i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(i3.AriaDescriber), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(MAT_TOOLTIP_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i4.Directionality), i0.ɵɵdirectiveInject(MAT_TOOLTIP_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTooltip,\n      selectors: [[\"\", \"matTooltip\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-tooltip-trigger\"],\n      hostVars: 2,\n      hostBindings: function MatTooltip_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-tooltip-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        position: [0, \"matTooltipPosition\", \"position\"],\n        positionAtOrigin: [0, \"matTooltipPositionAtOrigin\", \"positionAtOrigin\"],\n        disabled: [0, \"matTooltipDisabled\", \"disabled\"],\n        showDelay: [0, \"matTooltipShowDelay\", \"showDelay\"],\n        hideDelay: [0, \"matTooltipHideDelay\", \"hideDelay\"],\n        touchGestures: [0, \"matTooltipTouchGestures\", \"touchGestures\"],\n        message: [0, \"matTooltip\", \"message\"],\n        tooltipClass: [0, \"matTooltipClass\", \"tooltipClass\"]\n      },\n      exportAs: [\"matTooltip\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-mdc-tooltip-trigger',\n        '[class.mat-mdc-tooltip-disabled]': 'disabled'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Overlay\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.ScrollDispatcher\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.Platform\n  }, {\n    type: i3.AriaDescriber\n  }, {\n    type: i3.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: i4.Directionality\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    positionAtOrigin: [{\n      type: Input,\n      args: ['matTooltipPositionAtOrigin']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n  constructor(_changeDetectorRef, _elementRef, animationMode) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    /* Whether the tooltip text overflows to multiple lines */\n    this._isMultiline = false;\n    /** Whether interactions on the page should close the tooltip */\n    this._closeOnInteraction = false;\n    /** Whether the tooltip is currently visible. */\n    this._isVisible = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n    this._onHide = new Subject();\n    /** Name of the show animation and the class that toggles it. */\n    this._showAnimation = 'mat-mdc-tooltip-show';\n    /** Name of the hide animation and the class that toggles it. */\n    this._hideAnimation = 'mat-mdc-tooltip-hide';\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n  isVisible() {\n    return this._isVisible;\n  }\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  _onShow() {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n  /** Whether the tooltip text has overflown to the next line */\n  _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n    if (isVisible) {\n      this._onShow();\n    }\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n  static {\n    this.ɵfac = function TooltipComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TooltipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TooltipComponent,\n      selectors: [[\"mat-tooltip-component\"]],\n      viewQuery: function TooltipComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n        }\n      },\n      hostAttrs: [\"aria-hidden\", \"true\"],\n      hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseleave\", function TooltipComponent_mouseleave_HostBindingHandler($event) {\n            return ctx._handleMouseLeave($event);\n          });\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[\"tooltip\", \"\"], [1, \"mdc-tooltip\", \"mat-mdc-tooltip\", 3, \"animationend\", \"ngClass\"], [1, \"mat-mdc-tooltip-surface\", \"mdc-tooltip__surface\"]],\n      template: function TooltipComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵlistener(\"animationend\", function TooltipComponent_Template_div_animationend_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleAnimationEnd($event));\n          });\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-tooltip--multiline\", ctx._isMultiline);\n          i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.message);\n        }\n      },\n      dependencies: [NgClass],\n      styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-app-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-app-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-app-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-app-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-app-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-app-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-app-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-app-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      standalone: true,\n      imports: [NgClass],\n      template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-app-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-app-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-app-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-app-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-app-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-app-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-app-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-app-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: trigger('state', [\n  // TODO(crisbeto): these values are based on MDC's CSS.\n  // We should be able to use their styles directly once we land #19432.\n  state('initial, void, hidden', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), state('visible', style({\n    transform: 'scale(1)'\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)'))])\n};\nclass MatTooltipModule {\n  static {\n    this.ɵfac = function MatTooltipModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatTooltipModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTooltipModule,\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, getMatTooltipInvalidPositionError, matTooltipAnimations };\n", "import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { MatIconButton, MatButtonModule } from '@angular/material/button';\nimport { MatSelect, MatSelectModule } from '@angular/material/select';\nimport { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';\nimport { MatOption } from '@angular/material/core';\nimport { MatFormField } from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_Conditional_2_Conditional_3_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r3, \" \");\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 14)(1, \"mat-select\", 16, 0);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._changePageSize($event.value));\n    });\n    i0.ɵɵrepeaterCreate(3, MatPaginator_Conditional_2_Conditional_3_For_4_Template, 2, 2, \"mat-option\", 17, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_2_Conditional_3_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const selectRef_r4 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(selectRef_r4.open());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1._formFieldAppearance)(\"color\", ctx_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.pageSize)(\"disabled\", ctx_r1.disabled)(\"aria-labelledby\", ctx_r1._pageSizeLabelId)(\"panelClass\", ctx_r1.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r1.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pageSize);\n  }\n}\nfunction MatPaginator_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_Conditional_2_Conditional_3_Template, 6, 7, \"mat-form-field\", 14)(4, MatPaginator_Conditional_2_Conditional_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1._pageSizeLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length > 1 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length <= 1 ? 4 : -1);\n  }\n}\nfunction MatPaginator_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.firstPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._previousButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.lastPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r1._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._nextButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.lastPageLabel);\n  }\n}\nclass MatPaginatorIntl {\n  constructor() {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    this.changes = new Subject();\n    /** A label for the page size selector. */\n    this.itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    this.nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    this.previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    this.firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    this.lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    this.getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n  }\n  static {\n    this.ɵfac = function MatPaginatorIntl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginatorIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatPaginatorIntl,\n      factory: MatPaginatorIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  get pageIndex() {\n    return this._pageIndex;\n  }\n  set pageIndex(value) {\n    this._pageIndex = Math.max(value || 0, 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  get length() {\n    return this._length;\n  }\n  set length(value) {\n    this._length = value || 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Number of items to display on a page. By default set to 50. */\n  get pageSize() {\n    return this._pageSize;\n  }\n  set pageSize(value) {\n    this._pageSize = Math.max(value || 0, 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** The set of provided page size options to display to the user. */\n  get pageSizeOptions() {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value) {\n    this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n    this._updateDisplayedPageSizeOptions();\n  }\n  constructor(_intl, _changeDetectorRef, defaults) {\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n    this._isInitialized = false;\n    this._initializedStream = new ReplaySubject(1);\n    this._pageIndex = 0;\n    this._length = 0;\n    this._pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    this.hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    this.showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    this.selectConfig = {};\n    /** Whether the paginator is disabled. */\n    this.disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    this.page = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    this.initialized = this._initializedStream;\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n    if (defaults) {\n      const {\n        pageSize,\n        pageSizeOptions,\n        hidePageSize,\n        showFirstLastButtons\n      } = defaults;\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n      if (hidePageSize != null) {\n        this.hidePageSize = hidePageSize;\n      }\n      if (showFirstLastButtons != null) {\n        this.showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._initializedStream.next();\n  }\n  ngOnDestroy() {\n    this._initializedStream.complete();\n    this._intlChanges.unsubscribe();\n  }\n  /** Advances to the next page if it exists. */\n  nextPage() {\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move back to the previous page if it exists. */\n  previousPage() {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the first page if not already there. */\n  firstPage() {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the last page if not already there. */\n  lastPage() {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Whether there is a previous page. */\n  hasPreviousPage() {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n  /** Whether there is a next page. */\n  hasNextPage() {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n  /** Calculate the number of pages */\n  getNumberOfPages() {\n    if (!this.pageSize) {\n      return 0;\n    }\n    return Math.ceil(this.length / this.pageSize);\n  }\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  _updateDisplayedPageSizeOptions() {\n    if (!this._isInitialized) {\n      return;\n    }\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  _emitPageEvent(previousPageIndex) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length\n    });\n  }\n  static {\n    this.ɵfac = function MatPaginator_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginator)(i0.ɵɵdirectiveInject(MatPaginatorIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_PAGINATOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPaginator,\n      selectors: [[\"mat-paginator\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n      inputs: {\n        color: \"color\",\n        pageIndex: [2, \"pageIndex\", \"pageIndex\", numberAttribute],\n        length: [2, \"length\", \"length\", numberAttribute],\n        pageSize: [2, \"pageSize\", \"pageSize\", numberAttribute],\n        pageSizeOptions: \"pageSizeOptions\",\n        hidePageSize: [2, \"hidePageSize\", \"hidePageSize\", booleanAttribute],\n        showFirstLastButtons: [2, \"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute],\n        selectConfig: \"selectConfig\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        page: \"page\"\n      },\n      exportAs: [\"matPaginator\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 14,\n      consts: [[\"selectRef\", \"\"], [1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [1, \"mat-mdc-paginator-page-size-label\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"selectionChange\", \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\"], [3, \"value\"], [1, \"mat-mdc-paginator-touch-target\", 3, \"click\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n      template: function MatPaginator_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, MatPaginator_Conditional_2_Template, 5, 4, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatPaginator_Conditional_6_Template, 3, 5, \"button\", 6);\n          i0.ɵɵelementStart(7, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 8);\n          i0.ɵɵelement(9, \"path\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(11, \"svg\", 8);\n          i0.ɵɵelement(12, \"path\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, MatPaginator_Conditional_13_Template, 3, 5, \"button\", 12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.hidePageSize ? 2 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showFirstLastButtons ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._previousButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._nextButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.showFirstLastButtons ? 13 : -1);\n        }\n      },\n      dependencies: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-app-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-app-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-app-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-app-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-app-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-app-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-app-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-app-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginator, [{\n    type: Component,\n    args: [{\n      selector: 'mat-paginator',\n      exportAs: 'matPaginator',\n      host: {\n        'class': 'mat-mdc-paginator',\n        'role': 'group'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              #selectRef\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          <div class=\\\"mat-mdc-paginator-touch-target\\\" (click)=\\\"selectRef.open()\\\"></div>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"firstPage()\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"lastPage()\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-app-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-app-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-app-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-app-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-app-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-app-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-app-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-app-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\"]\n    }]\n  }], () => [{\n    type: MatPaginatorIntl\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n    }]\n  }], {\n    color: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    length: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    hidePageSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showFirstLastButtons: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectConfig: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\nclass MatPaginatorModule {\n  static {\n    this.ɵfac = function MatPaginatorModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginatorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPaginatorModule,\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n      exports: [MatPaginator]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_PAGINATOR_INTL_PROVIDER],\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n      exports: [MatPaginator],\n      providers: [MAT_PAGINATOR_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,qBAAqB;AAK3B,SAAS,kCAAkC,UAAU;AACnD,SAAO,MAAM,qBAAqB,QAAQ,eAAe;AAC3D;AAEA,IAAM,8BAA8B,IAAI,eAAe,+BAA+B;AAAA,EACpF,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,MAC/C,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,oCAAoC,SAAS;AACpD,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,IAC/C,gBAAgB;AAAA,EAClB,CAAC;AACH;AAEA,IAAM,+CAA+C;AAAA,EACnD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAEA,SAAS,sCAAsC;AAC7C,SAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,IACX,mBAAmB;AAAA,EACrB;AACF;AAEA,IAAM,8BAA8B,IAAI,eAAe,+BAA+B;AAAA,EACpF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAOD,IAAM,cAAc;AAEpB,IAAM,yBAAyB,gCAAgC;AAAA,EAC7D,SAAS;AACX,CAAC;AAGD,IAAM,iCAAiC;AACvC,IAAM,uBAAuB;AAC7B,IAAM,aAAa;AACnB,IAAM,YAAY;AAOlB,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA,EAEf,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,UAAI,KAAK,aAAa;AACpB,aAAK,gBAAgB,KAAK,WAAW;AACrC,aAAK,kBAAkB,KAAK,CAAC;AAC7B,aAAK,YAAY,eAAe;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,oBAAoB,sBAAsB,KAAK;AACpD,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,aAAa,sBAAsB,KAAK;AAC9C,QAAI,KAAK,cAAc,YAAY;AACjC,WAAK,YAAY;AAEjB,UAAI,YAAY;AACd,aAAK,KAAK,CAAC;AAAA,MACb,OAAO;AACL,aAAK,iCAAiC;AAAA,MACxC;AACA,WAAK,qBAAqB,KAAK,OAAO;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,qBAAqB,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,qBAAqB,KAAK;AAC5C,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,uBAAuB,KAAK;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,UAAM,aAAa,KAAK;AAIxB,SAAK,WAAW,SAAS,OAAO,OAAO,KAAK,EAAE,KAAK,IAAI;AACvD,QAAI,CAAC,KAAK,YAAY,KAAK,kBAAkB,GAAG;AAC9C,WAAK,KAAK,CAAC;AAAA,IACb,OAAO;AACL,WAAK,iCAAiC;AACtC,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,qBAAqB,UAAU;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,OAAO;AACtB,SAAK,gBAAgB;AACrB,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,KAAK,aAAa;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,YAAY,UAAU,aAAa,mBAAmB,mBAAmB,SAAS,WAAW,gBAAgB,eAAe,gBAAgB,MAAM,iBAAiB,WAAW;AAC5K,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,gCAAgC;AACrC,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAevB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAEhB,SAAK,oBAAoB,CAAC;AAE1B,SAAK,qBAAqB;AAE1B,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,QAAI,iBAAiB;AACnB,WAAK,aAAa,gBAAgB;AAClC,WAAK,aAAa,gBAAgB;AAClC,UAAI,gBAAgB,UAAU;AAC5B,aAAK,WAAW,gBAAgB;AAAA,MAClC;AACA,UAAI,gBAAgB,kBAAkB;AACpC,aAAK,mBAAmB,gBAAgB;AAAA,MAC1C;AACA,UAAI,gBAAgB,eAAe;AACjC,aAAK,gBAAgB,gBAAgB;AAAA,MACvC;AACA,UAAI,gBAAgB,cAAc;AAChC,aAAK,eAAe,gBAAgB;AAAA,MACtC;AAAA,IACF;AACA,SAAK,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC3D,UAAI,KAAK,aAAa;AACpB,aAAK,gBAAgB,KAAK,WAAW;AAAA,MACvC;AAAA,IACF,CAAC;AACD,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,kBAAkB;AAEhB,SAAK,mBAAmB;AACxB,SAAK,iCAAiC;AACtC,SAAK,cAAc,QAAQ,KAAK,WAAW,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAEhG,UAAI,CAAC,QAAQ;AACX,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,MACrC,WAAW,WAAW,YAAY;AAChC,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,UAAM,gBAAgB,KAAK,YAAY;AAEvC,QAAI,KAAK,oBAAoB;AAC3B,mBAAa,KAAK,kBAAkB;AAAA,IACtC;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAQ;AACzB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,SAAK,kBAAkB,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM;AACpD,oBAAc,oBAAoB,OAAO,UAAU,sBAAsB;AAAA,IAC3E,CAAC;AACD,SAAK,kBAAkB,SAAS;AAChC,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,eAAe,kBAAkB,eAAe,KAAK,SAAS,SAAS;AAC5E,SAAK,cAAc,eAAe,aAAa;AAAA,EACjD;AAAA;AAAA,EAEA,KAAK,QAAQ,KAAK,WAAW,QAAQ;AACnC,QAAI,KAAK,YAAY,CAAC,KAAK,WAAW,KAAK,kBAAkB,GAAG;AAC9D,WAAK,kBAAkB,yBAAyB;AAChD;AAAA,IACF;AACA,UAAM,aAAa,KAAK,eAAe,MAAM;AAC7C,SAAK,QAAQ;AACb,SAAK,UAAU,KAAK,WAAW,IAAI,gBAAgB,KAAK,mBAAmB,KAAK,iBAAiB;AACjG,UAAM,WAAW,KAAK,mBAAmB,WAAW,OAAO,KAAK,OAAO,EAAE;AACzE,aAAS,kBAAkB,KAAK,YAAY;AAC5C,aAAS,uBAAuB,KAAK;AACrC,aAAS,YAAY,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AACtF,SAAK,iBAAiB,KAAK,aAAa;AACxC,SAAK,sBAAsB;AAC3B,aAAS,KAAK,KAAK;AAAA,EACrB;AAAA;AAAA,EAEA,KAAK,QAAQ,KAAK,WAAW;AAC3B,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,UAAI,SAAS,UAAU,GAAG;AACxB,iBAAS,KAAK,KAAK;AAAA,MACrB,OAAO;AACL,iBAAS,yBAAyB;AAClC,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,SAAK,kBAAkB,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAW,MAAM;AAAA,EACtE;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,CAAC,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,UAAU;AAAA,EACpE;AAAA;AAAA,EAEA,eAAe,QAAQ;AACrB,QAAI,KAAK,aAAa;AACpB,YAAM,mBAAmB,KAAK,YAAY,UAAU,EAAE;AACtD,WAAK,CAAC,KAAK,oBAAoB,CAAC,WAAW,iBAAiB,mBAAmB,YAAY;AACzF,eAAO,KAAK;AAAA,MACd;AACA,WAAK,QAAQ;AAAA,IACf;AACA,UAAM,sBAAsB,KAAK,kBAAkB,4BAA4B,KAAK,WAAW;AAE/F,UAAM,WAAW,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,mBAAmB,UAAU,KAAK,cAAc,KAAK,WAAW,EAAE,sBAAsB,IAAI,KAAK,eAAe,UAAU,EAAE,uBAAuB,KAAK,EAAE,mBAAmB,KAAK,eAAe,EAAE,yBAAyB,mBAAmB;AAClT,aAAS,gBAAgB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAC5E,WAAK,4BAA4B,OAAO,cAAc;AACtD,UAAI,KAAK,kBAAkB;AACzB,YAAI,OAAO,yBAAyB,oBAAoB,KAAK,iBAAiB,UAAU,GAAG;AAGzF,eAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,cAAc,KAAK,SAAS,OAAO;AAAA,MACtC,WAAW,KAAK;AAAA,MAChB,kBAAkB;AAAA,MAClB,YAAY,GAAG,KAAK,eAAe,IAAI,WAAW;AAAA,MAClD,gBAAgB,KAAK,gBAAgB;AAAA,IACvC,CAAC;AACD,SAAK,gBAAgB,KAAK,WAAW;AACrC,SAAK,YAAY,YAAY,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AAC9F,SAAK,YAAY,qBAAqB,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,kBAAkB,uBAAuB,CAAC;AACxI,SAAK,YAAY,cAAc,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AACnF,UAAI,KAAK,kBAAkB,KAAK,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AAClF,cAAM,eAAe;AACrB,cAAM,gBAAgB;AACtB,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,QAAI,KAAK,iBAAiB,6BAA6B;AACrD,WAAK,YAAY,cAAc,GAAG,KAAK,eAAe,gCAAgC;AAAA,IACxF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,eAAe,KAAK,YAAY,YAAY,GAAG;AACtD,WAAK,YAAY,OAAO;AAAA,IAC1B;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,gBAAgB,YAAY;AAC1B,UAAM,WAAW,WAAW,UAAU,EAAE;AACxC,UAAM,SAAS,KAAK,WAAW;AAC/B,UAAM,UAAU,KAAK,oBAAoB;AACzC,aAAS,cAAc,CAAC,KAAK,WAAW,kCACnC,OAAO,OACP,QAAQ,KACZ,GAAG,KAAK,WAAW,kCACf,OAAO,WACP,QAAQ,SACZ,CAAC,CAAC;AAAA,EACL;AAAA;AAAA,EAEA,WAAW,UAAU;AACnB,UAAM,SAAS;AACf,UAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC/C,QAAI,SAAS,YAAY,OAAO;AAC9B,eAAS,UAAU,CAAC;AAAA,IACtB,WAAW,SAAS,YAAY,UAAU;AACxC,eAAS,UAAU;AAAA,IACrB,WAAW,SAAS,YAAY,SAAS;AACvC,eAAS,UAAU,QAAQ,CAAC,SAAS;AAAA,IACvC,WAAW,SAAS,YAAY,OAAO;AACrC,eAAS,UAAU,QAAQ,SAAS,CAAC;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC/C,UAAM,WAAW,KAAK;AACtB,QAAI;AACJ,QAAI,YAAY,WAAW,YAAY,SAAS;AAC9C,uBAAiB;AAAA,QACf,SAAS;AAAA,QACT,SAAS,YAAY,UAAU,QAAQ;AAAA,MACzC;AAAA,IACF,WAAW,YAAY,YAAY,YAAY,UAAU,SAAS,YAAY,WAAW,CAAC,OAAO;AAC/F,uBAAiB;AAAA,QACf,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF,WAAW,YAAY,WAAW,YAAY,WAAW,SAAS,YAAY,UAAU,CAAC,OAAO;AAC9F,uBAAiB;AAAA,QACf,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,YAAM,kCAAkC,QAAQ;AAAA,IAClD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,eAAe,SAAS,eAAe,OAAO;AACvE,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC/C,UAAM,WAAW,KAAK;AACtB,QAAI;AACJ,QAAI,YAAY,SAAS;AACvB,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,YAAY,SAAS;AAC9B,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,YAAY,YAAY,YAAY,UAAU,SAAS,YAAY,WAAW,CAAC,OAAO;AAC/F,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,YAAY,WAAW,YAAY,WAAW,SAAS,YAAY,UAAU,CAAC,OAAO;AAC9F,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,YAAM,kCAAkC,QAAQ;AAAA,IAClD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,gBAAgB,UAAU,gBAAgB,QAAQ;AAC3E,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAGtB,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,UAAU,KAAK;AACrC,WAAK,iBAAiB,cAAc;AACpC,sBAAgB,MAAM;AACpB,YAAI,KAAK,kBAAkB;AACzB,eAAK,YAAY,eAAe;AAAA,QAClC;AAAA,MACF,GAAG;AAAA,QACD,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,cAAc;AAC7B,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,eAAe;AACrC,WAAK,iBAAiB,cAAc;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,GAAG,GAAG;AACpB,QAAI,KAAK,aAAa,WAAW,KAAK,aAAa,SAAS;AAC1D,UAAI,MAAM,OAAO;AACf,YAAI;AAAA,MACN,WAAW,MAAM,UAAU;AACzB,YAAI;AAAA,MACN;AAAA,IACF,OAAO;AACL,UAAI,MAAM,OAAO;AACf,YAAI;AAAA,MACN,WAAW,MAAM,SAAS;AACxB,YAAI;AAAA,MACN;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,4BAA4B,gBAAgB;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AAGJ,QAAI,aAAa,UAAU;AAIzB,UAAI,KAAK,QAAQ,KAAK,KAAK,UAAU,OAAO;AAC1C,sBAAc,YAAY,QAAQ,SAAS;AAAA,MAC7C,OAAO;AACL,sBAAc,YAAY,UAAU,SAAS;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,oBAAc,aAAa,YAAY,YAAY,QAAQ,UAAU;AAAA,IACvE;AACA,QAAI,gBAAgB,KAAK,kBAAkB;AACzC,YAAM,aAAa,KAAK;AACxB,UAAI,YAAY;AACd,cAAM,cAAc,GAAG,KAAK,eAAe,IAAI,WAAW;AAC1D,mBAAW,iBAAiB,cAAc,KAAK,gBAAgB;AAC/D,mBAAW,cAAc,cAAc,WAAW;AAAA,MACpD;AACA,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,mCAAmC;AAEjC,QAAI,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,KAAK,oBAAoB,KAAK,kBAAkB,QAAQ;AAC9F;AAAA,IACF;AAGA,QAAI,KAAK,6BAA6B,GAAG;AACvC,WAAK,kBAAkB,KAAK,CAAC,cAAc,WAAS;AAClD,aAAK,gCAAgC;AACrC,YAAI,QAAQ;AACZ,YAAI,MAAM,MAAM,UAAa,MAAM,MAAM,QAAW;AAClD,kBAAQ;AAAA,QACV;AACA,aAAK,KAAK,QAAW,KAAK;AAAA,MAC5B,CAAC,CAAC;AAAA,IACJ,WAAW,KAAK,kBAAkB,OAAO;AACvC,WAAK,kCAAkC;AACvC,WAAK,kBAAkB,KAAK,CAAC,cAAc,WAAS;AAClD,cAAM,QAAQ,MAAM,gBAAgB,CAAC;AACrC,cAAM,SAAS,QAAQ;AAAA,UACrB,GAAG,MAAM;AAAA,UACT,GAAG,MAAM;AAAA,QACX,IAAI;AAGJ,aAAK,gCAAgC;AACrC,YAAI,KAAK,oBAAoB;AAC3B,uBAAa,KAAK,kBAAkB;AAAA,QACtC;AACA,cAAM,0BAA0B;AAChC,aAAK,qBAAqB,WAAW,MAAM;AACzC,eAAK,qBAAqB;AAC1B,eAAK,KAAK,QAAW,MAAM;AAAA,QAC7B,GAAG,KAAK,gBAAgB,2BAA2B,uBAAuB;AAAA,MAC5E,CAAC,CAAC;AAAA,IACJ;AACA,SAAK,cAAc,KAAK,iBAAiB;AAAA,EAC3C;AAAA,EACA,kCAAkC;AAChC,QAAI,KAAK,+BAA+B;AACtC;AAAA,IACF;AACA,SAAK,gCAAgC;AACrC,UAAM,gBAAgB,CAAC;AACvB,QAAI,KAAK,6BAA6B,GAAG;AACvC,oBAAc,KAAK,CAAC,cAAc,WAAS;AACzC,cAAM,YAAY,MAAM;AACxB,YAAI,CAAC,aAAa,CAAC,KAAK,aAAa,eAAe,SAAS,SAAS,GAAG;AACvE,eAAK,KAAK;AAAA,QACZ;AAAA,MACF,CAAC,GAAG,CAAC,SAAS,WAAS,KAAK,eAAe,KAAK,CAAC,CAAC;AAAA,IACpD,WAAW,KAAK,kBAAkB,OAAO;AACvC,WAAK,kCAAkC;AACvC,YAAM,mBAAmB,MAAM;AAC7B,YAAI,KAAK,oBAAoB;AAC3B,uBAAa,KAAK,kBAAkB;AAAA,QACtC;AACA,aAAK,KAAK,KAAK,gBAAgB,iBAAiB;AAAA,MAClD;AACA,oBAAc,KAAK,CAAC,YAAY,gBAAgB,GAAG,CAAC,eAAe,gBAAgB,CAAC;AAAA,IACtF;AACA,SAAK,cAAc,aAAa;AAChC,SAAK,kBAAkB,KAAK,GAAG,aAAa;AAAA,EAC9C;AAAA,EACA,cAAc,WAAW;AACvB,cAAU,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM;AACvC,WAAK,YAAY,cAAc,iBAAiB,OAAO,UAAU,sBAAsB;AAAA,IACzF,CAAC;AAAA,EACH;AAAA,EACA,+BAA+B;AAC7B,WAAO,CAAC,KAAK,UAAU,OAAO,CAAC,KAAK,UAAU;AAAA,EAChD;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,KAAK,kBAAkB,GAAG;AAC5B,YAAM,sBAAsB,KAAK,UAAU,iBAAiB,MAAM,SAAS,MAAM,OAAO;AACxF,YAAM,UAAU,KAAK,YAAY;AAKjC,UAAI,wBAAwB,WAAW,CAAC,QAAQ,SAAS,mBAAmB,GAAG;AAC7E,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,oCAAoC;AAClC,UAAM,WAAW,KAAK;AACtB,QAAI,aAAa,OAAO;AACtB,YAAM,UAAU,KAAK,YAAY;AACjC,YAAMA,SAAQ,QAAQ;AAGtB,UAAI,aAAa,QAAQ,QAAQ,aAAa,WAAW,QAAQ,aAAa,YAAY;AACxF,QAAAA,OAAM,aAAaA,OAAM,eAAeA,OAAM,mBAAmBA,OAAM,gBAAgB;AAAA,MACzF;AAGA,UAAI,aAAa,QAAQ,CAAC,QAAQ,WAAW;AAC3C,QAAAA,OAAM,iBAAiB;AAAA,MACzB;AACA,MAAAA,OAAM,cAAc;AACpB,MAAAA,OAAM,0BAA0B;AAAA,IAClC;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,YAAY;AAC/B,QAAI,KAAK,yBAAyB;AAChC;AAAA,IACF;AACA,SAAK,0BAA0B;AAC/B,SAAK,eAAe,kBAAkB,KAAK,YAAY,eAAe,YAAY,SAAS;AAC3F,SAAK,QAAQ,kBAAkB,MAAM;AAKnC,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,0BAA0B;AAC/B,YAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,eAAK,eAAe,SAAS,KAAK,YAAY,eAAe,KAAK,SAAS,SAAS;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAe,kBAAqB,OAAO,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,aAAa,GAAM,kBAAqB,YAAY,GAAM,kBAAkB,2BAA2B,GAAM,kBAAqB,cAAc,GAAM,kBAAkB,6BAA6B,CAAC,GAAM,kBAAkB,QAAQ,CAAC;AAAA,IAC/gB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,WAAW,CAAC,GAAG,yBAAyB;AAAA,MACxC,UAAU;AAAA,MACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,4BAA4B,IAAI,QAAQ;AAAA,QACzD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,QAC9C,kBAAkB,CAAC,GAAG,8BAA8B,kBAAkB;AAAA,QACtE,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,QAC9C,WAAW,CAAC,GAAG,uBAAuB,WAAW;AAAA,QACjD,WAAW,CAAC,GAAG,uBAAuB,WAAW;AAAA,QACjD,eAAe,CAAC,GAAG,2BAA2B,eAAe;AAAA,QAC7D,SAAS,CAAC,GAAG,cAAc,SAAS;AAAA,QACpC,cAAc,CAAC,GAAG,mBAAmB,cAAc;AAAA,MACrD;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,oCAAoC;AAAA,MACtC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,oBAAoB,aAAa,eAAe;AAC1D,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AAEnB,SAAK,eAAe;AAEpB,SAAK,sBAAsB;AAE3B,SAAK,aAAa;AAElB,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,iBAAiB;AAEtB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB,kBAAkB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO;AAEV,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,SAAK,iBAAiB,WAAW,MAAM;AACrC,WAAK,kBAAkB,IAAI;AAC3B,WAAK,iBAAiB;AAAA,IACxB,GAAG,KAAK;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO;AAEV,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,SAAK,iBAAiB,WAAW,MAAM;AACrC,WAAK,kBAAkB,KAAK;AAC5B,WAAK,iBAAiB;AAAA,IACxB,GAAG,KAAK;AAAA,EACV;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,SAAK,yBAAyB;AAC9B,SAAK,QAAQ,SAAS;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB;AACvB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,KAAK,CAAC;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,EACF,GAAG;AACD,QAAI,CAAC,iBAAiB,CAAC,KAAK,gBAAgB,SAAS,aAAa,GAAG;AACnE,UAAI,KAAK,UAAU,GAAG;AACpB,aAAK,KAAK,KAAK,oBAAoB;AAAA,MACrC,OAAO;AACL,aAAK,mBAAmB,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,SAAK,eAAe,KAAK,oBAAoB;AAC7C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,OAAO,KAAK,YAAY,cAAc,sBAAsB;AAClE,WAAO,KAAK,SAAS,cAAc,KAAK,SAAS;AAAA,EACnD;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAClB;AAAA,EACF,GAAG;AACD,QAAI,kBAAkB,KAAK,kBAAkB,kBAAkB,KAAK,gBAAgB;AAClF,WAAK,mBAAmB,kBAAkB,KAAK,cAAc;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,SAAK,iBAAiB,KAAK,iBAAiB;AAAA,EAC9C;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,QAAI,WAAW;AACb,WAAK,sBAAsB;AAAA,IAC7B,WAAW,CAAC,KAAK,UAAU,GAAG;AAC5B,WAAK,QAAQ,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAI3B,UAAM,UAAU,KAAK,SAAS;AAC9B,UAAM,YAAY,KAAK;AACvB,UAAM,YAAY,KAAK;AACvB,YAAQ,UAAU,OAAO,YAAY,YAAY,SAAS;AAC1D,YAAQ,UAAU,IAAI,YAAY,YAAY,SAAS;AACvD,QAAI,KAAK,eAAe,WAAW;AACjC,WAAK,aAAa;AAClB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAGA,QAAI,aAAa,CAAC,KAAK,uBAAuB,OAAO,qBAAqB,YAAY;AACpF,YAAM,SAAS,iBAAiB,OAAO;AAEvC,UAAI,OAAO,iBAAiB,oBAAoB,MAAM,QAAQ,OAAO,iBAAiB,gBAAgB,MAAM,QAAQ;AAClH,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,WAAW;AACb,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,qBAAqB;AAC5B,cAAQ,UAAU,IAAI,yBAAyB;AAC/C,WAAK,mBAAmB,SAAS;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACpL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,eAAe,MAAM;AAAA,MACjC,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,SAAS,+CAA+C,QAAQ;AAC1F,mBAAO,IAAI,kBAAkB,MAAM;AAAA,UACrC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,eAAe,mBAAmB,GAAG,gBAAgB,SAAS,GAAG,CAAC,GAAG,2BAA2B,sBAAsB,CAAC;AAAA,MACrJ,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,gBAAgB,SAAS,sDAAsD,QAAQ;AACnG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,UACvD,CAAC;AACD,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,OAAO,CAAC;AACX,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,YAAY;AACzD,UAAG,WAAW,WAAW,IAAI,YAAY;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,kBAAkB,IAAI,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO;AAAA,MACtB,QAAQ,CAAC,6uEAAivE;AAAA,MAC1vE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,OAAO;AAAA,MACjB,UAAU;AAAA,MACV,QAAQ,CAAC,6uEAAivE;AAAA,IAC5vE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA;AAAA;AAAA,QAGhB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAuB;AAAA;AAAA,EAE3B,cAAc,QAAQ,SAAS;AAAA;AAAA;AAAA,IAG/B,MAAM,yBAAyB,MAAM;AAAA,MACnC,SAAS;AAAA,MACT,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,IAAG,MAAM,WAAW,MAAM;AAAA,MAC1B,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,IAAG,WAAW,gBAAgB,QAAQ,kCAAkC,CAAC;AAAA,IAAG,WAAW,eAAe,QAAQ,iCAAiC,CAAC;AAAA,EAAC,CAAC;AACtJ;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY,cAAc,eAAe,iBAAiB,YAAY,gBAAgB;AAAA,MAChG,SAAS,CAAC,YAAY,kBAAkB,iBAAiB,mBAAmB;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,4CAA4C;AAAA,MACxD,SAAS,CAAC,YAAY,cAAc,eAAe,iBAAiB,iBAAiB,mBAAmB;AAAA,IAC1G,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,cAAc,eAAe,iBAAiB,YAAY,gBAAgB;AAAA,MAChG,SAAS,CAAC,YAAY,kBAAkB,iBAAiB,mBAAmB;AAAA,MAC5E,WAAW,CAAC,4CAA4C;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3jCH,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE;AACrC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAoB,IAAI;AAC9B,IAAG,WAAW,SAAS,iBAAiB;AACxC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,mBAAmB,GAAG;AAAA,EACnD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,EAAE,EAAE,GAAG,cAAc,IAAI,CAAC;AACjE,IAAG,WAAW,mBAAmB,SAAS,wFAAwF,QAAQ;AACxI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,OAAO,KAAK,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,iBAAiB,GAAG,yDAAyD,GAAG,GAAG,cAAc,IAAO,yBAAyB;AACpI,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,eAAkB,YAAY,CAAC;AACrC,aAAU,YAAY,aAAa,KAAK,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,oBAAoB,EAAE,SAAS,OAAO,KAAK;AAC9E,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ,EAAE,mBAAmB,OAAO,gBAAgB,EAAE,cAAc,OAAO,aAAa,cAAc,EAAE,EAAE,0BAA0B,OAAO,aAAa,sBAAsB;AACzO,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,yBAAyB;AAAA,EAChD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE;AAC3C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,kBAAkB,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,OAAO,EAAE;AACrK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,MAAM,OAAO,gBAAgB;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,MAAM,mBAAmB,GAAG;AAC9D,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,0BAA0B,SAAS,IAAI,IAAI,EAAE;AACrE,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,0BAA0B,UAAU,IAAI,IAAI,EAAE;AAAA,EACxE;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,8DAA8D;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,MAAM,cAAc,EAAE,sBAAsB,OAAO,yBAAyB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,OAAO,yBAAyB,CAAC;AAC9L,IAAG,YAAY,cAAc,OAAO,MAAM,cAAc;AAAA,EAC1D;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,+DAA+D;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,MAAM,aAAa,EAAE,sBAAsB,OAAO,qBAAqB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,OAAO,qBAAqB,CAAC;AACrL,IAAG,YAAY,cAAc,OAAO,MAAM,aAAa;AAAA,EACzD;AACF;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAKZ,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB;AAErB,SAAK,oBAAoB;AAEzB,SAAK,iBAAiB;AAEtB,SAAK,gBAAgB;AAErB,SAAK,gBAAgB,CAAC,MAAM,UAAU,WAAW;AAC/C,UAAI,UAAU,KAAK,YAAY,GAAG;AAChC,eAAO,QAAQ,MAAM;AAAA,MACvB;AACA,eAAS,KAAK,IAAI,QAAQ,CAAC;AAC3B,YAAM,aAAa,OAAO;AAE1B,YAAM,WAAW,aAAa,SAAS,KAAK,IAAI,aAAa,UAAU,MAAM,IAAI,aAAa;AAC9F,aAAO,GAAG,aAAa,CAAC,MAAM,QAAQ,OAAO,MAAM;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,SAAS,oCAAoC,YAAY;AACvD,SAAO,cAAc,IAAI,iBAAiB;AAC5C;AAEA,IAAM,8BAA8B;AAAA;AAAA,EAElC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,gBAAgB,CAAC;AAAA,EACzD,YAAY;AACd;AAGA,IAAM,oBAAoB;AAK1B,IAAM,YAAN,MAAgB;AAAC;AAEjB,IAAM,gCAAgC,IAAI,eAAe,+BAA+B;AACxF,IAAI,eAAe;AAMnB,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA,EAEjB,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,KAAK,IAAI,SAAS,GAAG,CAAC;AACxC,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU,SAAS;AACxB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC;AACvC,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,oBAAoB,SAAS,CAAC,GAAG,IAAI,OAAK,gBAAgB,GAAG,CAAC,CAAC;AACpE,SAAK,gCAAgC;AAAA,EACvC;AAAA,EACA,YAAY,OAAO,oBAAoB,UAAU;AAC/C,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAE1B,SAAK,mBAAmB,iCAAiC,cAAc;AACvE,SAAK,iBAAiB;AACtB,SAAK,qBAAqB,IAAI,cAAc,CAAC;AAC7C,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,mBAAmB,CAAC;AAEzB,SAAK,eAAe;AAEpB,SAAK,uBAAuB;AAE5B,SAAK,eAAe,CAAC;AAErB,SAAK,WAAW;AAEhB,SAAK,OAAO,IAAI,aAAa;AAE7B,SAAK,cAAc,KAAK;AACxB,SAAK,eAAe,MAAM,QAAQ,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AACxF,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,YAAY,MAAM;AACpB,aAAK,YAAY;AAAA,MACnB;AACA,UAAI,mBAAmB,MAAM;AAC3B,aAAK,mBAAmB;AAAA,MAC1B;AACA,UAAI,gBAAgB,MAAM;AACxB,aAAK,eAAe;AAAA,MACtB;AACA,UAAI,wBAAwB,MAAM;AAChC,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AACA,SAAK,uBAAuB,UAAU,uBAAuB;AAAA,EAC/D;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AACtB,SAAK,gCAAgC;AACrC,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,SAAS;AACjC,SAAK,aAAa,YAAY;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW;AACT,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,YAAY;AAClC,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,YAAY;AAClC,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,YAAY;AAEV,QAAI,CAAC,KAAK,gBAAgB,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY;AACjB,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,WAAW;AAET,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,iBAAiB,IAAI;AAC3C,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,aAAa,KAAK,KAAK,YAAY;AAAA,EACjD;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,eAAe,KAAK,iBAAiB,IAAI;AAC/C,WAAO,KAAK,YAAY,gBAAgB,KAAK,YAAY;AAAA,EAC3D;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,KAAK,SAAS,KAAK,QAAQ;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,UAAU;AAGxB,UAAM,aAAa,KAAK,YAAY,KAAK;AACzC,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,MAAM,aAAa,QAAQ,KAAK;AACtD,SAAK,WAAW;AAChB,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK,YAAY,CAAC,KAAK,YAAY;AAAA,EAC5C;AAAA;AAAA,EAEA,2BAA2B;AACzB,WAAO,KAAK,YAAY,CAAC,KAAK,gBAAgB;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kCAAkC;AAChC,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,gBAAgB,UAAU,IAAI,KAAK,gBAAgB,CAAC,IAAI;AAAA,IAChF;AACA,SAAK,4BAA4B,KAAK,gBAAgB,MAAM;AAC5D,QAAI,KAAK,0BAA0B,QAAQ,KAAK,QAAQ,MAAM,IAAI;AAChE,WAAK,0BAA0B,KAAK,KAAK,QAAQ;AAAA,IACnD;AAEA,SAAK,0BAA0B,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnD,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,eAAe,mBAAmB;AAChC,SAAK,KAAK,KAAK;AAAA,MACb;AAAA,MACA,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAiB,kBAAkB,gBAAgB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,+BAA+B,CAAC,CAAC;AAAA,IAC3L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,QAAQ,SAAS,GAAG,mBAAmB;AAAA,MACnD,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,WAAW,CAAC,GAAG,aAAa,aAAa,eAAe;AAAA,QACxD,QAAQ,CAAC,GAAG,UAAU,UAAU,eAAe;AAAA,QAC/C,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,QACrD,iBAAiB;AAAA,QACjB,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,QAClE,sBAAsB,CAAC,GAAG,wBAAwB,wBAAwB,gBAAgB;AAAA,QAC1F,cAAc;AAAA,QACd,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACxD;AAAA,MACA,SAAS;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,iCAAiC,GAAG,CAAC,aAAa,UAAU,GAAG,+BAA+B,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,sCAAsC,GAAG,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,yCAAyC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,WAAW,aAAa,aAAa,SAAS,eAAe,QAAQ,GAAG,wBAAwB,GAAG,CAAC,KAAK,+CAA+C,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,qCAAqC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,KAAK,gDAAgD,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,qCAAqC,GAAG,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,GAAG,sCAAsC,GAAG,cAAc,OAAO,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,gCAAgC,IAAI,GAAG,mBAAmB,SAAS,YAAY,mBAAmB,cAAc,wBAAwB,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,kCAAkC,GAAG,OAAO,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,sCAAsC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,KAAK,4DAA4D,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,qCAAqC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,KAAK,4DAA4D,CAAC;AAAA,MACx4D,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,OAAO,CAAC;AACpE,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,OAAO,CAAC;AACX,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,UAAU,CAAC;AACvE,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,WAAW,SAAS,SAAS,gDAAgD;AAC9E,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC;AACD,UAAG,eAAe;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,gBAAgB;AACnB,UAAG,eAAe,IAAI,UAAU,EAAE;AAClC,UAAG,WAAW,SAAS,SAAS,iDAAiD;AAC/E,mBAAO,IAAI,SAAS;AAAA,UACtB,CAAC;AACD,UAAG,eAAe;AAClB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,UAAU,IAAI,QAAQ,EAAE;AAC3B,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,IAAI,sCAAsC,GAAG,GAAG,UAAU,EAAE;AAC1E,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,CAAC,IAAI,eAAe,IAAI,EAAE;AAC3C,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAK,IAAI,MAAM,cAAc,IAAI,WAAW,IAAI,UAAU,IAAI,MAAM,GAAG,GAAG;AAChG,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,uBAAuB,IAAI,EAAE;AAClD,UAAG,UAAU;AACb,UAAG,WAAW,cAAc,IAAI,MAAM,iBAAiB,EAAE,sBAAsB,IAAI,yBAAyB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,IAAI,yBAAyB,CAAC;AACxL,UAAG,YAAY,cAAc,IAAI,MAAM,iBAAiB;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,cAAc,IAAI,MAAM,aAAa,EAAE,sBAAsB,IAAI,qBAAqB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,IAAI,qBAAqB,CAAC;AAC5K,UAAG,YAAY,cAAc,IAAI,MAAM,aAAa;AACpD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,uBAAuB,KAAK,EAAE;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAc,WAAW,WAAW,eAAe,UAAU;AAAA,MAC5E,QAAQ,CAAC,i+EAAi+E;AAAA,MAC1+E,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,WAAW,WAAW,eAAe,UAAU;AAAA,MACvE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,i+EAAi+E;AAAA,IAC5+E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,iBAAiB,kBAAkB,YAAY;AAAA,MAC1E,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,2BAA2B;AAAA,MACvC,SAAS,CAAC,iBAAiB,iBAAiB,kBAAkB,YAAY;AAAA,IAC5E,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,kBAAkB,YAAY;AAAA,MAC1E,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,2BAA2B;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["style"]}