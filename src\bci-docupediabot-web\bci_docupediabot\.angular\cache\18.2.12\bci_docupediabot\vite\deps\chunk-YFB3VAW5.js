import {
  Host,
  h,
  registerInstance
} from "./chunk-SKBP5EI5.js";
import {
  __async,
  __objRest,
  __spreadProps,
  __spreadValues,
  __superGet
} from "./chunk-Y4T55RDF.js";

// node_modules/@bci-web-core/web-components/dist/esm/bci-overlay-2df7d606.js
var t$2 = globalThis;
var e$7 = t$2.ShadowRoot && (void 0 === t$2.ShadyCSS || t$2.ShadyCSS.nativeShadow) && "adoptedStyleSheets" in Document.prototype && "replace" in CSSStyleSheet.prototype;
var s$2 = Symbol();
var o$8 = /* @__PURE__ */ new WeakMap();
var n$5 = class {
  constructor(t2, e2, o2) {
    if (this._$cssResult$ = true, o2 !== s$2) throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");
    this.cssText = t2, this.t = e2;
  }
  get styleSheet() {
    let t2 = this.o;
    const s2 = this.t;
    if (e$7 && void 0 === t2) {
      const e2 = void 0 !== s2 && 1 === s2.length;
      e2 && (t2 = o$8.get(s2)), void 0 === t2 && ((this.o = t2 = new CSSStyleSheet()).replaceSync(this.cssText), e2 && o$8.set(s2, t2));
    }
    return t2;
  }
  toString() {
    return this.cssText;
  }
};
var r$6 = (t2) => new n$5("string" == typeof t2 ? t2 : t2 + "", void 0, s$2);
var i$5 = (t2, ...e2) => {
  const o2 = 1 === t2.length ? t2[0] : e2.reduce((e3, s2, o3) => e3 + ((t3) => {
    if (true === t3._$cssResult$) return t3.cssText;
    if ("number" == typeof t3) return t3;
    throw Error("Value passed to 'css' function must be a 'css' function result: " + t3 + ". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.");
  })(s2) + t2[o3 + 1], t2[0]);
  return new n$5(o2, t2, s$2);
};
var S$1 = (s2, o2) => {
  if (e$7) s2.adoptedStyleSheets = o2.map((t2) => t2 instanceof CSSStyleSheet ? t2 : t2.styleSheet);
  else for (const e2 of o2) {
    const o3 = document.createElement("style"), n2 = t$2.litNonce;
    void 0 !== n2 && o3.setAttribute("nonce", n2), o3.textContent = e2.cssText, s2.appendChild(o3);
  }
};
var c$5 = e$7 ? (t2) => t2 : (t2) => t2 instanceof CSSStyleSheet ? ((t3) => {
  let e2 = "";
  for (const s2 of t3.cssRules) e2 += s2.cssText;
  return r$6(e2);
})(t2) : t2;
var {
  is: i$4,
  defineProperty: e$6,
  getOwnPropertyDescriptor: r$5,
  getOwnPropertyNames: h$2,
  getOwnPropertySymbols: o$7,
  getPrototypeOf: n$4
} = Object;
var a$1 = globalThis;
var c$4 = a$1.trustedTypes;
var l$1 = c$4 ? c$4.emptyScript : "";
var p$4 = a$1.reactiveElementPolyfillSupport;
var d$2 = (t2, s2) => t2;
var u$1 = {
  toAttribute(t2, s2) {
    switch (s2) {
      case Boolean:
        t2 = t2 ? l$1 : null;
        break;
      case Object:
      case Array:
        t2 = null == t2 ? t2 : JSON.stringify(t2);
    }
    return t2;
  },
  fromAttribute(t2, s2) {
    let i3 = t2;
    switch (s2) {
      case Boolean:
        i3 = null !== t2;
        break;
      case Number:
        i3 = null === t2 ? null : Number(t2);
        break;
      case Object:
      case Array:
        try {
          i3 = JSON.parse(t2);
        } catch (t3) {
          i3 = null;
        }
    }
    return i3;
  }
};
var f$2 = (t2, s2) => !i$4(t2, s2);
var y$1 = {
  attribute: true,
  type: String,
  converter: u$1,
  reflect: false,
  hasChanged: f$2
};
Symbol.metadata ??= Symbol("metadata"), a$1.litPropertyMetadata ??= /* @__PURE__ */ new WeakMap();
var b$1 = class extends HTMLElement {
  static addInitializer(t2) {
    this._$Ei(), (this.l ??= []).push(t2);
  }
  static get observedAttributes() {
    return this.finalize(), this._$Eh && [...this._$Eh.keys()];
  }
  static createProperty(t2, s2 = y$1) {
    if (s2.state && (s2.attribute = false), this._$Ei(), this.elementProperties.set(t2, s2), !s2.noAccessor) {
      const i3 = Symbol(), r2 = this.getPropertyDescriptor(t2, i3, s2);
      void 0 !== r2 && e$6(this.prototype, t2, r2);
    }
  }
  static getPropertyDescriptor(t2, s2, i3) {
    const {
      get: e2,
      set: h3
    } = r$5(this.prototype, t2) ?? {
      get() {
        return this[s2];
      },
      set(t3) {
        this[s2] = t3;
      }
    };
    return {
      get() {
        return e2?.call(this);
      },
      set(s3) {
        const r2 = e2?.call(this);
        h3.call(this, s3), this.requestUpdate(t2, r2, i3);
      },
      configurable: true,
      enumerable: true
    };
  }
  static getPropertyOptions(t2) {
    return this.elementProperties.get(t2) ?? y$1;
  }
  static _$Ei() {
    if (this.hasOwnProperty(d$2("elementProperties"))) return;
    const t2 = n$4(this);
    t2.finalize(), void 0 !== t2.l && (this.l = [...t2.l]), this.elementProperties = new Map(t2.elementProperties);
  }
  static finalize() {
    if (this.hasOwnProperty(d$2("finalized"))) return;
    if (this.finalized = true, this._$Ei(), this.hasOwnProperty(d$2("properties"))) {
      const t3 = this.properties, s2 = [...h$2(t3), ...o$7(t3)];
      for (const i3 of s2) this.createProperty(i3, t3[i3]);
    }
    const t2 = this[Symbol.metadata];
    if (null !== t2) {
      const s2 = litPropertyMetadata.get(t2);
      if (void 0 !== s2) for (const [t3, i3] of s2) this.elementProperties.set(t3, i3);
    }
    this._$Eh = /* @__PURE__ */ new Map();
    for (const [t3, s2] of this.elementProperties) {
      const i3 = this._$Eu(t3, s2);
      void 0 !== i3 && this._$Eh.set(i3, t3);
    }
    this.elementStyles = this.finalizeStyles(this.styles);
  }
  static finalizeStyles(s2) {
    const i3 = [];
    if (Array.isArray(s2)) {
      const e2 = new Set(s2.flat(1 / 0).reverse());
      for (const s3 of e2) i3.unshift(c$5(s3));
    } else void 0 !== s2 && i3.push(c$5(s2));
    return i3;
  }
  static _$Eu(t2, s2) {
    const i3 = s2.attribute;
    return false === i3 ? void 0 : "string" == typeof i3 ? i3 : "string" == typeof t2 ? t2.toLowerCase() : void 0;
  }
  constructor() {
    super(), this._$Ep = void 0, this.isUpdatePending = false, this.hasUpdated = false, this._$Em = null, this._$Ev();
  }
  _$Ev() {
    this._$ES = new Promise((t2) => this.enableUpdating = t2), this._$AL = /* @__PURE__ */ new Map(), this._$E_(), this.requestUpdate(), this.constructor.l?.forEach((t2) => t2(this));
  }
  addController(t2) {
    (this._$EO ??= /* @__PURE__ */ new Set()).add(t2), void 0 !== this.renderRoot && this.isConnected && t2.hostConnected?.();
  }
  removeController(t2) {
    this._$EO?.delete(t2);
  }
  _$E_() {
    const t2 = /* @__PURE__ */ new Map(), s2 = this.constructor.elementProperties;
    for (const i3 of s2.keys()) this.hasOwnProperty(i3) && (t2.set(i3, this[i3]), delete this[i3]);
    t2.size > 0 && (this._$Ep = t2);
  }
  createRenderRoot() {
    const t2 = this.shadowRoot ?? this.attachShadow(this.constructor.shadowRootOptions);
    return S$1(t2, this.constructor.elementStyles), t2;
  }
  connectedCallback() {
    this.renderRoot ??= this.createRenderRoot(), this.enableUpdating(true), this._$EO?.forEach((t2) => t2.hostConnected?.());
  }
  enableUpdating(t2) {
  }
  disconnectedCallback() {
    this._$EO?.forEach((t2) => t2.hostDisconnected?.());
  }
  attributeChangedCallback(t2, s2, i3) {
    this._$AK(t2, i3);
  }
  _$EC(t2, s2) {
    const i3 = this.constructor.elementProperties.get(t2), e2 = this.constructor._$Eu(t2, i3);
    if (void 0 !== e2 && true === i3.reflect) {
      const r2 = (void 0 !== i3.converter?.toAttribute ? i3.converter : u$1).toAttribute(s2, i3.type);
      this._$Em = t2, null == r2 ? this.removeAttribute(e2) : this.setAttribute(e2, r2), this._$Em = null;
    }
  }
  _$AK(t2, s2) {
    const i3 = this.constructor, e2 = i3._$Eh.get(t2);
    if (void 0 !== e2 && this._$Em !== e2) {
      const t3 = i3.getPropertyOptions(e2), r2 = "function" == typeof t3.converter ? {
        fromAttribute: t3.converter
      } : void 0 !== t3.converter?.fromAttribute ? t3.converter : u$1;
      this._$Em = e2, this[e2] = r2.fromAttribute(s2, t3.type), this._$Em = null;
    }
  }
  requestUpdate(t2, s2, i3) {
    if (void 0 !== t2) {
      if (i3 ??= this.constructor.getPropertyOptions(t2), !(i3.hasChanged ?? f$2)(this[t2], s2)) return;
      this.P(t2, s2, i3);
    }
    false === this.isUpdatePending && (this._$ES = this._$ET());
  }
  P(t2, s2, i3) {
    this._$AL.has(t2) || this._$AL.set(t2, s2), true === i3.reflect && this._$Em !== t2 && (this._$Ej ??= /* @__PURE__ */ new Set()).add(t2);
  }
  _$ET() {
    return __async(this, null, function* () {
      this.isUpdatePending = true;
      try {
        yield this._$ES;
      } catch (t3) {
        Promise.reject(t3);
      }
      const t2 = this.scheduleUpdate();
      return null != t2 && (yield t2), !this.isUpdatePending;
    });
  }
  scheduleUpdate() {
    return this.performUpdate();
  }
  performUpdate() {
    if (!this.isUpdatePending) return;
    if (!this.hasUpdated) {
      if (this.renderRoot ??= this.createRenderRoot(), this._$Ep) {
        for (const [t4, s3] of this._$Ep) this[t4] = s3;
        this._$Ep = void 0;
      }
      const t3 = this.constructor.elementProperties;
      if (t3.size > 0) for (const [s3, i3] of t3) true !== i3.wrapped || this._$AL.has(s3) || void 0 === this[s3] || this.P(s3, this[s3], i3);
    }
    let t2 = false;
    const s2 = this._$AL;
    try {
      t2 = this.shouldUpdate(s2), t2 ? (this.willUpdate(s2), this._$EO?.forEach((t3) => t3.hostUpdate?.()), this.update(s2)) : this._$EU();
    } catch (s3) {
      throw t2 = false, this._$EU(), s3;
    }
    t2 && this._$AE(s2);
  }
  willUpdate(t2) {
  }
  _$AE(t2) {
    this._$EO?.forEach((t3) => t3.hostUpdated?.()), this.hasUpdated || (this.hasUpdated = true, this.firstUpdated(t2)), this.updated(t2);
  }
  _$EU() {
    this._$AL = /* @__PURE__ */ new Map(), this.isUpdatePending = false;
  }
  get updateComplete() {
    return this.getUpdateComplete();
  }
  getUpdateComplete() {
    return this._$ES;
  }
  shouldUpdate(t2) {
    return true;
  }
  update(t2) {
    this._$Ej &&= this._$Ej.forEach((t3) => this._$EC(t3, this[t3])), this._$EU();
  }
  updated(t2) {
  }
  firstUpdated(t2) {
  }
};
b$1.elementStyles = [], b$1.shadowRootOptions = {
  mode: "open"
}, b$1[d$2("elementProperties")] = /* @__PURE__ */ new Map(), b$1[d$2("finalized")] = /* @__PURE__ */ new Map(), p$4?.({
  ReactiveElement: b$1
}), (a$1.reactiveElementVersions ??= []).push("2.0.4");
var t$1 = globalThis;
var i$3 = t$1.trustedTypes;
var s$1 = i$3 ? i$3.createPolicy("lit-html", {
  createHTML: (t2) => t2
}) : void 0;
var e$5 = "$lit$";
var h$1 = `lit$${Math.random().toFixed(9).slice(2)}$`;
var o$6 = "?" + h$1;
var n$3 = `<${o$6}>`;
var r$4 = document;
var l = () => r$4.createComment("");
var c$3 = (t2) => null === t2 || "object" != typeof t2 && "function" != typeof t2;
var a = Array.isArray;
var u = (t2) => a(t2) || "function" == typeof t2?.[Symbol.iterator];
var d$1 = "[ 	\n\f\r]";
var f$1 = /<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g;
var v = /-->/g;
var _ = />/g;
var m = RegExp(`>|${d$1}(?:([^\\s"'>=/]+)(${d$1}*=${d$1}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`, "g");
var p$3 = /'/g;
var g$2 = /"/g;
var $ = /^(?:script|style|textarea|title)$/i;
var y = (t2) => (i3, ...s2) => ({
  _$litType$: t2,
  strings: i3,
  values: s2
});
var x = y(1);
var w$1 = Symbol.for("lit-noChange");
var T$2 = Symbol.for("lit-nothing");
var A = /* @__PURE__ */ new WeakMap();
var E$1 = r$4.createTreeWalker(r$4, 129);
function C$2(t2, i3) {
  if (!Array.isArray(t2) || !t2.hasOwnProperty("raw")) throw Error("invalid template strings array");
  return void 0 !== s$1 ? s$1.createHTML(i3) : i3;
}
var P = (t2, i3) => {
  const s2 = t2.length - 1, o2 = [];
  let r2, l2 = 2 === i3 ? "<svg>" : "", c2 = f$1;
  for (let i4 = 0; i4 < s2; i4++) {
    const s3 = t2[i4];
    let a2, u2, d2 = -1, y2 = 0;
    for (; y2 < s3.length && (c2.lastIndex = y2, u2 = c2.exec(s3), null !== u2); ) y2 = c2.lastIndex, c2 === f$1 ? "!--" === u2[1] ? c2 = v : void 0 !== u2[1] ? c2 = _ : void 0 !== u2[2] ? ($.test(u2[2]) && (r2 = RegExp("</" + u2[2], "g")), c2 = m) : void 0 !== u2[3] && (c2 = m) : c2 === m ? ">" === u2[0] ? (c2 = r2 ?? f$1, d2 = -1) : void 0 === u2[1] ? d2 = -2 : (d2 = c2.lastIndex - u2[2].length, a2 = u2[1], c2 = void 0 === u2[3] ? m : '"' === u2[3] ? g$2 : p$3) : c2 === g$2 || c2 === p$3 ? c2 = m : c2 === v || c2 === _ ? c2 = f$1 : (c2 = m, r2 = void 0);
    const x2 = c2 === m && t2[i4 + 1].startsWith("/>") ? " " : "";
    l2 += c2 === f$1 ? s3 + n$3 : d2 >= 0 ? (o2.push(a2), s3.slice(0, d2) + e$5 + s3.slice(d2) + h$1 + x2) : s3 + h$1 + (-2 === d2 ? i4 : x2);
  }
  return [C$2(t2, l2 + (t2[s2] || "<?>") + (2 === i3 ? "</svg>" : "")), o2];
};
var V = class _V {
  constructor({
    strings: t2,
    _$litType$: s2
  }, n2) {
    let r2;
    this.parts = [];
    let c2 = 0, a2 = 0;
    const u2 = t2.length - 1, d2 = this.parts, [f2, v2] = P(t2, s2);
    if (this.el = _V.createElement(f2, n2), E$1.currentNode = this.el.content, 2 === s2) {
      const t3 = this.el.content.firstChild;
      t3.replaceWith(...t3.childNodes);
    }
    for (; null !== (r2 = E$1.nextNode()) && d2.length < u2; ) {
      if (1 === r2.nodeType) {
        if (r2.hasAttributes()) for (const t3 of r2.getAttributeNames()) if (t3.endsWith(e$5)) {
          const i3 = v2[a2++], s3 = r2.getAttribute(t3).split(h$1), e2 = /([.?@])?(.*)/.exec(i3);
          d2.push({
            type: 1,
            index: c2,
            name: e2[2],
            strings: s3,
            ctor: "." === e2[1] ? k : "?" === e2[1] ? H : "@" === e2[1] ? I : R
          }), r2.removeAttribute(t3);
        } else t3.startsWith(h$1) && (d2.push({
          type: 6,
          index: c2
        }), r2.removeAttribute(t3));
        if ($.test(r2.tagName)) {
          const t3 = r2.textContent.split(h$1), s3 = t3.length - 1;
          if (s3 > 0) {
            r2.textContent = i$3 ? i$3.emptyScript : "";
            for (let i3 = 0; i3 < s3; i3++) r2.append(t3[i3], l()), E$1.nextNode(), d2.push({
              type: 2,
              index: ++c2
            });
            r2.append(t3[s3], l());
          }
        }
      } else if (8 === r2.nodeType) if (r2.data === o$6) d2.push({
        type: 2,
        index: c2
      });
      else {
        let t3 = -1;
        for (; -1 !== (t3 = r2.data.indexOf(h$1, t3 + 1)); ) d2.push({
          type: 7,
          index: c2
        }), t3 += h$1.length - 1;
      }
      c2++;
    }
  }
  static createElement(t2, i3) {
    const s2 = r$4.createElement("template");
    return s2.innerHTML = t2, s2;
  }
};
function N(t2, i3, s2 = t2, e2) {
  if (i3 === w$1) return i3;
  let h3 = void 0 !== e2 ? s2._$Co?.[e2] : s2._$Cl;
  const o2 = c$3(i3) ? void 0 : i3._$litDirective$;
  return h3?.constructor !== o2 && (h3?._$AO?.(false), void 0 === o2 ? h3 = void 0 : (h3 = new o2(t2), h3._$AT(t2, s2, e2)), void 0 !== e2 ? (s2._$Co ??= [])[e2] = h3 : s2._$Cl = h3), void 0 !== h3 && (i3 = N(t2, h3._$AS(t2, i3.values), h3, e2)), i3;
}
var S = class {
  constructor(t2, i3) {
    this._$AV = [], this._$AN = void 0, this._$AD = t2, this._$AM = i3;
  }
  get parentNode() {
    return this._$AM.parentNode;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  u(t2) {
    const {
      el: {
        content: i3
      },
      parts: s2
    } = this._$AD, e2 = (t2?.creationScope ?? r$4).importNode(i3, true);
    E$1.currentNode = e2;
    let h3 = E$1.nextNode(), o2 = 0, n2 = 0, l2 = s2[0];
    for (; void 0 !== l2; ) {
      if (o2 === l2.index) {
        let i4;
        2 === l2.type ? i4 = new M(h3, h3.nextSibling, this, t2) : 1 === l2.type ? i4 = new l2.ctor(h3, l2.name, l2.strings, this, t2) : 6 === l2.type && (i4 = new L(h3, this, t2)), this._$AV.push(i4), l2 = s2[++n2];
      }
      o2 !== l2?.index && (h3 = E$1.nextNode(), o2++);
    }
    return E$1.currentNode = r$4, e2;
  }
  p(t2) {
    let i3 = 0;
    for (const s2 of this._$AV) void 0 !== s2 && (void 0 !== s2.strings ? (s2._$AI(t2, s2, i3), i3 += s2.strings.length - 2) : s2._$AI(t2[i3])), i3++;
  }
};
var M = class _M {
  get _$AU() {
    return this._$AM?._$AU ?? this._$Cv;
  }
  constructor(t2, i3, s2, e2) {
    this.type = 2, this._$AH = T$2, this._$AN = void 0, this._$AA = t2, this._$AB = i3, this._$AM = s2, this.options = e2, this._$Cv = e2?.isConnected ?? true;
  }
  get parentNode() {
    let t2 = this._$AA.parentNode;
    const i3 = this._$AM;
    return void 0 !== i3 && 11 === t2?.nodeType && (t2 = i3.parentNode), t2;
  }
  get startNode() {
    return this._$AA;
  }
  get endNode() {
    return this._$AB;
  }
  _$AI(t2, i3 = this) {
    t2 = N(this, t2, i3), c$3(t2) ? t2 === T$2 || null == t2 || "" === t2 ? (this._$AH !== T$2 && this._$AR(), this._$AH = T$2) : t2 !== this._$AH && t2 !== w$1 && this._(t2) : void 0 !== t2._$litType$ ? this.$(t2) : void 0 !== t2.nodeType ? this.T(t2) : u(t2) ? this.k(t2) : this._(t2);
  }
  S(t2) {
    return this._$AA.parentNode.insertBefore(t2, this._$AB);
  }
  T(t2) {
    this._$AH !== t2 && (this._$AR(), this._$AH = this.S(t2));
  }
  _(t2) {
    this._$AH !== T$2 && c$3(this._$AH) ? this._$AA.nextSibling.data = t2 : this.T(r$4.createTextNode(t2)), this._$AH = t2;
  }
  $(t2) {
    const {
      values: i3,
      _$litType$: s2
    } = t2, e2 = "number" == typeof s2 ? this._$AC(t2) : (void 0 === s2.el && (s2.el = V.createElement(C$2(s2.h, s2.h[0]), this.options)), s2);
    if (this._$AH?._$AD === e2) this._$AH.p(i3);
    else {
      const t3 = new S(e2, this), s3 = t3.u(this.options);
      t3.p(i3), this.T(s3), this._$AH = t3;
    }
  }
  _$AC(t2) {
    let i3 = A.get(t2.strings);
    return void 0 === i3 && A.set(t2.strings, i3 = new V(t2)), i3;
  }
  k(t2) {
    a(this._$AH) || (this._$AH = [], this._$AR());
    const i3 = this._$AH;
    let s2, e2 = 0;
    for (const h3 of t2) e2 === i3.length ? i3.push(s2 = new _M(this.S(l()), this.S(l()), this, this.options)) : s2 = i3[e2], s2._$AI(h3), e2++;
    e2 < i3.length && (this._$AR(s2 && s2._$AB.nextSibling, e2), i3.length = e2);
  }
  _$AR(t2 = this._$AA.nextSibling, i3) {
    for (this._$AP?.(false, true, i3); t2 && t2 !== this._$AB; ) {
      const i4 = t2.nextSibling;
      t2.remove(), t2 = i4;
    }
  }
  setConnected(t2) {
    void 0 === this._$AM && (this._$Cv = t2, this._$AP?.(t2));
  }
};
var R = class {
  get tagName() {
    return this.element.tagName;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  constructor(t2, i3, s2, e2, h3) {
    this.type = 1, this._$AH = T$2, this._$AN = void 0, this.element = t2, this.name = i3, this._$AM = e2, this.options = h3, s2.length > 2 || "" !== s2[0] || "" !== s2[1] ? (this._$AH = Array(s2.length - 1).fill(new String()), this.strings = s2) : this._$AH = T$2;
  }
  _$AI(t2, i3 = this, s2, e2) {
    const h3 = this.strings;
    let o2 = false;
    if (void 0 === h3) t2 = N(this, t2, i3, 0), o2 = !c$3(t2) || t2 !== this._$AH && t2 !== w$1, o2 && (this._$AH = t2);
    else {
      const e3 = t2;
      let n2, r2;
      for (t2 = h3[0], n2 = 0; n2 < h3.length - 1; n2++) r2 = N(this, e3[s2 + n2], i3, n2), r2 === w$1 && (r2 = this._$AH[n2]), o2 ||= !c$3(r2) || r2 !== this._$AH[n2], r2 === T$2 ? t2 = T$2 : t2 !== T$2 && (t2 += (r2 ?? "") + h3[n2 + 1]), this._$AH[n2] = r2;
    }
    o2 && !e2 && this.j(t2);
  }
  j(t2) {
    t2 === T$2 ? this.element.removeAttribute(this.name) : this.element.setAttribute(this.name, t2 ?? "");
  }
};
var k = class extends R {
  constructor() {
    super(...arguments), this.type = 3;
  }
  j(t2) {
    this.element[this.name] = t2 === T$2 ? void 0 : t2;
  }
};
var H = class extends R {
  constructor() {
    super(...arguments), this.type = 4;
  }
  j(t2) {
    this.element.toggleAttribute(this.name, !!t2 && t2 !== T$2);
  }
};
var I = class extends R {
  constructor(t2, i3, s2, e2, h3) {
    super(t2, i3, s2, e2, h3), this.type = 5;
  }
  _$AI(t2, i3 = this) {
    if ((t2 = N(this, t2, i3, 0) ?? T$2) === w$1) return;
    const s2 = this._$AH, e2 = t2 === T$2 && s2 !== T$2 || t2.capture !== s2.capture || t2.once !== s2.once || t2.passive !== s2.passive, h3 = t2 !== T$2 && (s2 === T$2 || e2);
    e2 && this.element.removeEventListener(this.name, this, s2), h3 && this.element.addEventListener(this.name, this, t2), this._$AH = t2;
  }
  handleEvent(t2) {
    "function" == typeof this._$AH ? this._$AH.call(this.options?.host ?? this.element, t2) : this._$AH.handleEvent(t2);
  }
};
var L = class {
  constructor(t2, i3, s2) {
    this.element = t2, this.type = 6, this._$AN = void 0, this._$AM = i3, this.options = s2;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2) {
    N(this, t2);
  }
};
var Z = t$1.litHtmlPolyfillSupport;
Z?.(V, M), (t$1.litHtmlVersions ??= []).push("3.1.3");
var j = (t2, i3, s2) => {
  const e2 = s2?.renderBefore ?? i3;
  let h3 = e2._$litPart$;
  if (void 0 === h3) {
    const t3 = s2?.renderBefore ?? null;
    e2._$litPart$ = h3 = new M(i3.insertBefore(l(), t3), t3, void 0, s2 ?? {});
  }
  return h3._$AI(t2), h3;
};
var s = class extends b$1 {
  constructor() {
    super(...arguments), this.renderOptions = {
      host: this
    }, this._$Do = void 0;
  }
  createRenderRoot() {
    const t2 = super.createRenderRoot();
    return this.renderOptions.renderBefore ??= t2.firstChild, t2;
  }
  update(t2) {
    const i3 = this.render();
    this.hasUpdated || (this.renderOptions.isConnected = this.isConnected), super.update(t2), this._$Do = j(i3, this.renderRoot, this.renderOptions);
  }
  connectedCallback() {
    super.connectedCallback(), this._$Do?.setConnected(true);
  }
  disconnectedCallback() {
    super.disconnectedCallback(), this._$Do?.setConnected(false);
  }
  render() {
    return w$1;
  }
};
s._$litElement$ = true, s["finalized"] = true, globalThis.litElementHydrateSupport?.({
  LitElement: s
});
var r$3 = globalThis.litElementPolyfillSupport;
r$3?.({
  LitElement: s
});
(globalThis.litElementVersions ??= []).push("4.0.5");
var version = "0.42.3";
var c$2 = /* @__PURE__ */ new Set();
var g$1 = () => {
  const s2 = document.documentElement.dir === "rtl" ? document.documentElement.dir : "ltr";
  c$2.forEach((o2) => {
    o2.setAttribute("dir", s2);
  });
};
var w = new MutationObserver(g$1);
w.observe(document.documentElement, {
  attributes: true,
  attributeFilter: ["dir"]
});
var p$2 = (s2) => typeof s2.startManagingContentDirection != "undefined" || s2.tagName === "SP-THEME";
function SpectrumMixin(s2) {
  class o2 extends s2 {
    get isLTR() {
      return this.dir === "ltr";
    }
    hasVisibleFocusInTree() {
      const n2 = ((r2 = document) => {
        var l2;
        let t2 = r2.activeElement;
        for (; t2 != null && t2.shadowRoot && t2.shadowRoot.activeElement; ) t2 = t2.shadowRoot.activeElement;
        const a2 = t2 ? [t2] : [];
        for (; t2; ) {
          const i3 = t2.assignedSlot || t2.parentElement || ((l2 = t2.getRootNode()) == null ? void 0 : l2.host);
          i3 && a2.push(i3), t2 = i3;
        }
        return a2;
      })(this.getRootNode())[0];
      if (!n2) return false;
      try {
        return n2.matches(":focus-visible") || n2.matches(".focus-visible");
      } catch (r2) {
        return n2.matches(".focus-visible");
      }
    }
    connectedCallback() {
      if (!this.hasAttribute("dir")) {
        let e2 = this.assignedSlot || this.parentNode;
        for (; e2 !== document.documentElement && !p$2(e2); ) e2 = e2.assignedSlot || e2.parentNode || e2.host;
        if (this.dir = e2.dir === "rtl" ? e2.dir : this.dir || "ltr", e2 === document.documentElement) c$2.add(this);
        else {
          const {
            localName: n2
          } = e2;
          n2.search("-") > -1 && !customElements.get(n2) ? customElements.whenDefined(n2).then(() => {
            e2.startManagingContentDirection(this);
          }) : e2.startManagingContentDirection(this);
        }
        this._dirParent = e2;
      }
      super.connectedCallback();
    }
    disconnectedCallback() {
      super.disconnectedCallback(), this._dirParent && (this._dirParent === document.documentElement ? c$2.delete(this) : this._dirParent.stopManagingContentDirection(this), this.removeAttribute("dir"));
    }
  }
  return o2;
}
var SpectrumElement = class extends SpectrumMixin(s) {
};
SpectrumElement.VERSION = version;
var o$5 = {
  attribute: true,
  type: String,
  converter: u$1,
  reflect: false,
  hasChanged: f$2
};
var r$2 = (t2 = o$5, e2, r2) => {
  const {
    kind: n2,
    metadata: i3
  } = r2;
  let s2 = globalThis.litPropertyMetadata.get(i3);
  if (void 0 === s2 && globalThis.litPropertyMetadata.set(i3, s2 = /* @__PURE__ */ new Map()), s2.set(r2.name, t2), "accessor" === n2) {
    const {
      name: o2
    } = r2;
    return {
      set(r3) {
        const n3 = e2.get.call(this);
        e2.set.call(this, r3), this.requestUpdate(o2, n3, t2);
      },
      init(e3) {
        return void 0 !== e3 && this.P(o2, void 0, t2), e3;
      }
    };
  }
  if ("setter" === n2) {
    const {
      name: o2
    } = r2;
    return function(r3) {
      const n3 = this[o2];
      e2.call(this, r3), this.requestUpdate(o2, n3, t2);
    };
  }
  throw Error("Unsupported decorator location: " + n2);
};
function n$2(t2) {
  return (e2, o2) => "object" == typeof o2 ? r$2(t2, e2, o2) : ((t3, e3, o3) => {
    const r2 = e3.hasOwnProperty(o3);
    return e3.constructor.createProperty(o3, r2 ? __spreadProps(__spreadValues({}, t3), {
      wrapped: true
    }) : t3), r2 ? Object.getOwnPropertyDescriptor(e3, o3) : void 0;
  })(t2, e2, o2);
}
function r$1(r2) {
  return n$2(__spreadProps(__spreadValues({}, r2), {
    state: true,
    attribute: false
  }));
}
var e$4 = (e2, t2, c2) => (c2.configurable = true, c2.enumerable = true, Reflect.decorate && "object" != typeof t2 && Object.defineProperty(e2, t2, c2), c2);
function e$3(e2, r2) {
  return (n2, s2, i3) => {
    const o2 = (t2) => t2.renderRoot?.querySelector(e2) ?? null;
    if (r2) {
      const {
        get: e3,
        set: r3
      } = "object" == typeof s2 ? n2 : i3 ?? (() => {
        const t2 = Symbol();
        return {
          get() {
            return this[t2];
          },
          set(e4) {
            this[t2] = e4;
          }
        };
      })();
      return e$4(n2, s2, {
        get() {
          let t2 = e3.call(this);
          return void 0 === t2 && (t2 = o2(this), (null !== t2 || this.hasUpdated) && r3.call(this, t2)), t2;
        }
      });
    }
    return e$4(n2, s2, {
      get() {
        return o2(this);
      }
    });
  };
}
function o$4(o2) {
  return (e2, n2) => {
    const {
      slot: r2,
      selector: s2
    } = o2 ?? {}, c2 = "slot" + (r2 ? `[name=${r2}]` : ":not([name])");
    return e$4(e2, n2, {
      get() {
        const t2 = this.renderRoot?.querySelector(c2), e3 = t2?.assignedElements(o2) ?? [];
        return void 0 === s2 ? e3 : e3.filter((t3) => t3.matches(s2));
      }
    });
  };
}
var elementResolverUpdatedSymbol = Symbol("element resolver updated");
var ElementResolutionController = class {
  constructor(e2, {
    selector: t2
  } = {
    selector: ""
  }) {
    this._element = null;
    this._selector = "";
    this.mutationCallback = (e3) => {
      let t3 = false;
      e3.forEach((s2) => {
        if (!t3) {
          if (s2.type === "childList") {
            const r2 = this.element && [...s2.removedNodes].includes(this.element), l2 = !!this.selector && [...s2.addedNodes].some(this.elementIsSelected);
            t3 = t3 || r2 || l2;
          }
          if (s2.type === "attributes") {
            const r2 = s2.target === this.element, l2 = !!this.selector && this.elementIsSelected(s2.target);
            t3 = t3 || r2 || l2;
          }
        }
      }), t3 && this.resolveElement();
    };
    this.elementIsSelected = (e3) => {
      var t3;
      return this.selectorIsId ? (e3 == null ? void 0 : e3.id) === this.selectorAsId : (t3 = e3 == null ? void 0 : e3.matches) == null ? void 0 : t3.call(e3, this.selector);
    };
    this.host = e2, this.selector = t2, this.observer = new MutationObserver(this.mutationCallback), this.host.addController(this);
  }
  get element() {
    return this._element;
  }
  set element(e2) {
    if (e2 === this.element) return;
    const t2 = this.element;
    this._element = e2, this.host.requestUpdate(elementResolverUpdatedSymbol, t2);
  }
  get selector() {
    return this._selector;
  }
  set selector(e2) {
    e2 !== this.selector && (this.releaseElement(), this._selector = e2, this.resolveElement());
  }
  get selectorAsId() {
    return this.selector.slice(1);
  }
  get selectorIsId() {
    return !!this.selector && this.selector.startsWith("#");
  }
  hostConnected() {
    this.resolveElement(), this.observer.observe(this.host.getRootNode(), {
      subtree: true,
      childList: true,
      attributes: true
    });
  }
  hostDisconnected() {
    this.releaseElement(), this.observer.disconnect();
  }
  resolveElement() {
    if (!this.selector) {
      this.releaseElement();
      return;
    }
    const e2 = this.host.getRootNode();
    this.element = this.selectorIsId ? e2.getElementById(this.selectorAsId) : e2.querySelector(this.selector);
  }
  releaseElement() {
    this.element = null;
  }
};
var o$3 = (o2) => o2 ?? T$2;
var t = {
  ATTRIBUTE: 1,
  CHILD: 2,
  PROPERTY: 3,
  BOOLEAN_ATTRIBUTE: 4,
  EVENT: 5,
  ELEMENT: 6
};
var e$2 = (t2) => (...e2) => ({
  _$litDirective$: t2,
  values: e2
});
var i$2 = class {
  constructor(t2) {
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AT(t2, e2, i3) {
    this._$Ct = t2, this._$AM = e2, this._$Ci = i3;
  }
  _$AS(t2, e2) {
    return this.update(t2, e2);
  }
  update(t2, e2) {
    return this.render(...e2);
  }
};
var n$1 = "important";
var i$1 = " !" + n$1;
var o$2 = e$2(class extends i$2 {
  constructor(t$12) {
    if (super(t$12), t$12.type !== t.ATTRIBUTE || "style" !== t$12.name || t$12.strings?.length > 2) throw Error("The `styleMap` directive must be used in the `style` attribute and must be the only part in the attribute.");
  }
  render(t2) {
    return Object.keys(t2).reduce((e2, r2) => {
      const s2 = t2[r2];
      return null == s2 ? e2 : e2 + `${r2 = r2.includes("-") ? r2 : r2.replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g, "-$&").toLowerCase()}:${s2};`;
    }, "");
  }
  update(e2, [r2]) {
    const {
      style: s2
    } = e2.element;
    if (void 0 === this.ft) return this.ft = new Set(Object.keys(r2)), this.render(r2);
    for (const t2 of this.ft) null == r2[t2] && (this.ft.delete(t2), t2.includes("-") ? s2.removeProperty(t2) : s2[t2] = null);
    for (const t2 in r2) {
      const e3 = r2[t2];
      if (null != e3) {
        this.ft.add(t2);
        const r3 = "string" == typeof e3 && e3.endsWith(i$1);
        t2.includes("-") || r3 ? s2.setProperty(t2, r3 ? e3.slice(0, -11) : e3, r3 ? n$1 : "") : s2[t2] = e3;
      }
    }
    return w$1;
  }
});
function randomID() {
  return Array.from(crypto.getRandomValues(new Uint8Array(4)), (r2) => `0${(r2 & 255).toString(16)}`.slice(-2)).join("");
}
function T$1(o2, i3, l2 = []) {
  for (let e2 = 0; e2 < i3.length; ++e2) {
    const n2 = i3[e2], r2 = o2[e2], t2 = r2.parentElement || r2.getRootNode();
    l2[e2] && l2[e2](n2), t2 && t2 !== r2 && t2.replaceChild(n2, r2), delete o2[e2];
  }
  return i3;
}
var reparentChildren = (o2, i3, {
  position: l2,
  prepareCallback: e2
} = {
  position: "beforeend"
}) => {
  let {
    length: n2
  } = o2;
  if (n2 === 0) return () => o2;
  let r2 = 1, t2 = 0;
  (l2 === "afterbegin" || l2 === "afterend") && (r2 = -1, t2 = n2 - 1);
  const a2 = new Array(n2), c2 = new Array(n2), p2 = document.createComment("placeholder for reparented element");
  do {
    const d2 = o2[t2];
    e2 && (c2[t2] = e2(d2)), a2[t2] = p2.cloneNode();
    const m2 = d2.parentElement || d2.getRootNode();
    m2 && m2 !== d2 && m2.replaceChild(a2[t2], d2), i3.insertAdjacentElement(l2, d2), t2 += r2;
  } while (--n2 > 0);
  return function() {
    return T$1(a2, o2, c2);
  };
};
var OverlayTimer = class {
  constructor(e2 = {}) {
    this.warmUpDelay = 1e3;
    this.coolDownDelay = 1e3;
    this.isWarm = false;
    this.timeout = 0;
    Object.assign(this, e2);
  }
  openTimer(e2) {
    return __async(this, null, function* () {
      if (this.cancelCooldownTimer(), !this.component || e2 !== this.component) return this.component && (this.close(this.component), this.cancelCooldownTimer()), this.component = e2, this.isWarm ? false : (this.promise = new Promise((o2) => {
        this.resolve = o2, this.timeout = window.setTimeout(() => {
          this.resolve && (this.resolve(false), this.isWarm = true);
        }, this.warmUpDelay);
      }), this.promise);
      if (this.promise) return this.promise;
      throw new Error("Inconsistent state");
    });
  }
  close(e2) {
    this.component && this.component === e2 && (this.resetCooldownTimer(), this.timeout > 0 && (clearTimeout(this.timeout), this.timeout = 0), this.resolve && (this.resolve(true), delete this.resolve), delete this.promise, delete this.component);
  }
  resetCooldownTimer() {
    this.isWarm && (this.cooldownTimeout && window.clearTimeout(this.cooldownTimeout), this.cooldownTimeout = window.setTimeout(() => {
      this.isWarm = false, delete this.cooldownTimeout;
    }, this.coolDownDelay));
  }
  cancelCooldownTimer() {
    this.cooldownTimeout && window.clearTimeout(this.cooldownTimeout), delete this.cooldownTimeout;
  }
};
var overlayTimer = new OverlayTimer();
var noop = () => {
};
var guaranteedAllTransitionend = (i3, v2, e2) => {
  const r2 = new AbortController(), n2 = /* @__PURE__ */ new Map(), a2 = () => {
    r2.abort(), e2();
  };
  let m2, l2;
  const t2 = requestAnimationFrame(() => {
    m2 = requestAnimationFrame(() => {
      l2 = requestAnimationFrame(() => {
        a2();
      });
    });
  }), p2 = (o2) => {
    o2.target === i3 && (n2.set(o2.propertyName, n2.get(o2.propertyName) - 1), n2.get(o2.propertyName) || n2.delete(o2.propertyName), n2.size === 0 && a2());
  }, d2 = (o2) => {
    o2.target === i3 && (n2.has(o2.propertyName) || n2.set(o2.propertyName, 0), n2.set(o2.propertyName, n2.get(o2.propertyName) + 1), cancelAnimationFrame(t2), cancelAnimationFrame(m2), cancelAnimationFrame(l2));
  };
  i3.addEventListener("transitionrun", d2, {
    signal: r2.signal
  }), i3.addEventListener("transitionend", p2, {
    signal: r2.signal
  }), i3.addEventListener("transitioncancel", p2, {
    signal: r2.signal
  }), v2();
};
function nextFrame() {
  return new Promise((i3) => requestAnimationFrame(() => i3()));
}
var AbstractOverlay = class _AbstractOverlay extends SpectrumElement {
  constructor() {
    super(...arguments);
    this.dispose = noop;
    this.offset = 0;
    this.willPreventClose = false;
  }
  applyFocus(e2, r2) {
    return __async(this, null, function* () {
    });
  }
  get delayed() {
    return false;
  }
  set delayed(e2) {
  }
  get disabled() {
    return false;
  }
  set disabled(e2) {
  }
  get elementResolver() {
    return this._elementResolver;
  }
  set elementResolver(e2) {
    this._elementResolver = e2;
  }
  ensureOnDOM(e2) {
    return __async(this, null, function* () {
    });
  }
  makeTransition(e2) {
    return __async(this, null, function* () {
      return null;
    });
  }
  manageDelay(e2) {
    return __async(this, null, function* () {
    });
  }
  manageDialogOpen() {
    return __async(this, null, function* () {
    });
  }
  managePopoverOpen() {
    return __async(this, null, function* () {
    });
  }
  managePosition() {
  }
  get open() {
    return false;
  }
  set open(e2) {
  }
  get placementController() {
    return this._placementController;
  }
  set placementController(e2) {
    this._placementController = e2;
  }
  requestSlottable() {
  }
  returnFocus() {
  }
  get state() {
    return "closed";
  }
  set state(e2) {
  }
  manuallyKeepOpen() {
  }
  static update() {
    const e2 = new CustomEvent("sp-update-overlays", {
      bubbles: true,
      composed: true,
      cancelable: true
    });
    document.dispatchEvent(e2);
  }
  static open(_0, _1, _2, _3) {
    return __async(this, arguments, function* (e2, r2, n2, a2) {
      yield import("./sp-overlay-1351366a-G4KATWUU.js");
      const m2 = arguments.length === 2, l2 = n2 || e2, t2 = new this();
      let p2 = false;
      t2.dispose = () => {
        t2.addEventListener("sp-closed", () => {
          p2 || (d2(), p2 = true), requestAnimationFrame(() => {
            t2.remove();
          });
        }), t2.open = false, t2.dispose = noop;
      };
      const d2 = reparentChildren([l2], t2, {
        position: "beforeend",
        prepareCallback: (s2) => {
          const c2 = s2.slot;
          return s2.removeAttribute("slot"), () => {
            s2.slot = c2;
          };
        }
      });
      if (!m2 && l2 && a2) {
        const s2 = e2, c2 = r2, u2 = a2;
        return _AbstractOverlay.applyOptions(t2, __spreadProps(__spreadValues({}, u2), {
          delayed: u2.delayed || l2.hasAttribute("delayed"),
          trigger: u2.virtualTrigger || s2,
          type: c2 === "modal" ? "modal" : c2 === "hover" ? "hint" : "auto"
        })), s2.insertAdjacentElement("afterend", t2), yield t2.updateComplete, t2.open = true, t2.dispose;
      }
      const y2 = r2;
      return t2.append(l2), _AbstractOverlay.applyOptions(t2, __spreadProps(__spreadValues({}, y2), {
        delayed: y2.delayed || l2.hasAttribute("delayed")
      })), t2.updateComplete.then(() => {
        t2.open = true;
      }), t2;
    });
  }
  static applyOptions(e2, r2) {
    var n2, a2;
    e2.delayed = !!r2.delayed, e2.receivesFocus = (n2 = r2.receivesFocus) != null ? n2 : "auto", e2.triggerElement = r2.trigger || null, e2.type = r2.type || "modal", e2.offset = (a2 = r2.offset) != null ? a2 : 0, e2.placement = r2.placement, e2.willPreventClose = !!r2.notImmediatelyClosable;
  }
};
var e$1 = ["button", "[focusable]", "[href]", "input", "label", "select", "textarea", "[tabindex]"];
var o$1 = ':not([tabindex="-1"])';
var userFocusableSelector = e$1.join(`${o$1}, `) + o$1;
var firstFocusableIn = (e2) => e2.querySelector(userFocusableSelector);
var firstFocusableSlottedIn = (e2) => e2.assignedElements().find((o2) => o2.matches(userFocusableSelector));
var VirtualTrigger = class {
  constructor(t2, i3) {
    this.x = 0;
    this.y = 0;
    this.x = t2, this.y = i3;
  }
  updateBoundingClientRect(t2, i3) {
    this.x = t2, this.y = i3, AbstractOverlay.update();
  }
  getBoundingClientRect() {
    return {
      width: 0,
      height: 0,
      top: this.y,
      right: this.x,
      y: this.y,
      x: this.x,
      bottom: this.y,
      left: this.x,
      toJSON() {
      }
    };
  }
};
var BeforetoggleClosedEvent = class extends Event {
  constructor() {
    super("beforetoggle", {
      bubbles: false,
      composed: false
    });
    this.currentState = "open";
    this.newState = "closed";
  }
};
var BeforetoggleOpenEvent = class extends Event {
  constructor() {
    super("beforetoggle", {
      bubbles: false,
      composed: false
    });
    this.currentState = "closed";
    this.newState = "open";
  }
};
var OverlayStateEvent = class extends Event {
  constructor(r2, l2, {
    publish: o2,
    interaction: s2,
    reason: n2
  }) {
    super(r2, {
      bubbles: o2,
      composed: o2
    });
    this.overlay = l2;
    this.detail = {
      interaction: s2,
      reason: n2
    };
  }
};
function n(o2) {
  return typeof window != "undefined" && window.navigator != null ? o2.test(window.navigator.userAgent) : false;
}
function e(o2) {
  return typeof window != "undefined" && window.navigator != null ? o2.test(window.navigator.platform) : false;
}
function isMac() {
  return e(/^Mac/);
}
function isIPhone() {
  return e(/^iPhone/);
}
function isIPad() {
  return e(/^iPad/) || isMac() && navigator.maxTouchPoints > 1;
}
function isIOS() {
  return isIPhone() || isIPad();
}
function isAndroid() {
  return n(/Android/);
}
function OverlayDialog(h3) {
  class p2 extends h3 {
    manageDialogOpen() {
      return __async(this, null, function* () {
        const e2 = this.open;
        if (yield this.managePosition(), this.open !== e2) return;
        const i3 = yield this.dialogMakeTransition(e2);
        this.open === e2 && (yield this.dialogApplyFocus(e2, i3));
      });
    }
    dialogMakeTransition(e2) {
      return __async(this, null, function* () {
        let i3 = null;
        const m2 = (t2, s2) => () => __async(this, null, function* () {
          if (t2.open = e2, !e2) {
            const n2 = () => {
              t2.removeEventListener("close", n2), a2(t2, s2);
            };
            t2.addEventListener("close", n2);
          }
          if (s2 > 0) return;
          const o2 = e2 ? BeforetoggleOpenEvent : BeforetoggleClosedEvent;
          this.dispatchEvent(new o2()), e2 && (t2.matches(userFocusableSelector) && (i3 = t2), i3 = i3 || firstFocusableIn(t2), i3 || t2.querySelectorAll("slot").forEach((r2) => {
            i3 || (i3 = firstFocusableSlottedIn(r2));
          }), !(!this.isConnected || this.dialogEl.open) && this.dialogEl.showModal());
        }), a2 = (t2, s2) => () => {
          if (this.open !== e2) return;
          const o2 = e2 ? "sp-opened" : "sp-closed";
          if (s2 > 0) {
            t2.dispatchEvent(new OverlayStateEvent(o2, this, {
              interaction: this.type,
              publish: false
            }));
            return;
          }
          if (!this.isConnected || e2 !== this.open) return;
          const n2 = () => __async(this, null, function* () {
            const r2 = this.triggerElement instanceof VirtualTrigger;
            this.dispatchEvent(new OverlayStateEvent(o2, this, {
              interaction: this.type,
              publish: r2
            })), t2.dispatchEvent(new OverlayStateEvent(o2, this, {
              interaction: this.type,
              publish: false
            })), this.triggerElement && !r2 && this.triggerElement.dispatchEvent(new OverlayStateEvent(o2, this, {
              interaction: this.type,
              publish: true
            })), this.state = e2 ? "opened" : "closed", this.returnFocus(), yield nextFrame(), yield nextFrame(), e2 === this.open && e2 === false && this.requestSlottable();
          });
          !e2 && this.dialogEl.open ? (this.dialogEl.addEventListener("close", () => {
            n2();
          }, {
            once: true
          }), this.dialogEl.close()) : n2();
        };
        return this.elements.forEach((t2, s2) => {
          guaranteedAllTransitionend(t2, m2(t2, s2), a2(t2, s2));
        }), i3;
      });
    }
    dialogApplyFocus(e2, i3) {
      return __async(this, null, function* () {
        this.applyFocus(e2, i3);
      });
    }
  }
  return p2;
}
var C$1 = CSS.supports("(overlay: auto)");
function f(a2) {
  let c2 = false;
  try {
    c2 = a2.matches(":popover-open");
  } catch (e2) {
  }
  let p2 = false;
  try {
    p2 = a2.matches(":open");
  } catch (e2) {
  }
  return c2 || p2;
}
function OverlayPopover(a2) {
  class c2 extends a2 {
    manageDelay(e2) {
      return __async(this, null, function* () {
        if (e2 === false || e2 !== this.open) {
          overlayTimer.close(this);
          return;
        }
        this.delayed && (yield overlayTimer.openTimer(this)) && (this.open = !e2);
      });
    }
    shouldHidePopover(e2) {
      return __async(this, null, function* () {
        if (e2 && this.open !== e2) return;
        const o2 = (..._0) => __async(this, [..._0], function* ({
          newState: i3
        } = {}) {
          i3 !== "open" && (yield this.placementController.resetOverlayPosition());
        });
        if (!f(this.dialogEl)) {
          o2();
          return;
        }
        this.dialogEl.addEventListener("toggle", o2, {
          once: true
        });
      });
    }
    shouldShowPopover(e2) {
      return __async(this, null, function* () {
        let o2 = false;
        try {
          o2 = this.dialogEl.matches(":popover-open");
        } catch (u2) {
        }
        let i3 = false;
        try {
          i3 = this.dialogEl.matches(":open");
        } catch (u2) {
        }
        e2 && this.open === e2 && !o2 && !i3 && this.isConnected && (this.dialogEl.showPopover(), yield this.managePosition());
      });
    }
    ensureOnDOM(e2) {
      return __async(this, null, function* () {
        yield nextFrame(), C$1 || (yield this.shouldHidePopover(e2)), yield this.shouldShowPopover(e2), yield nextFrame();
      });
    }
    makeTransition(e2) {
      return __async(this, null, function* () {
        if (this.open !== e2) return null;
        let o2 = null;
        const i3 = (t2, s2) => () => {
          if (t2.open = e2, s2 === 0) {
            const r2 = e2 ? BeforetoggleOpenEvent : BeforetoggleClosedEvent;
            this.dispatchEvent(new r2());
          }
          if (!e2 || (t2.matches(userFocusableSelector) && (o2 = t2), o2 = o2 || firstFocusableIn(t2), o2)) return;
          t2.querySelectorAll("slot").forEach((r2) => {
            o2 || (o2 = firstFocusableSlottedIn(r2));
          });
        }, u2 = (t2, s2) => () => __async(this, null, function* () {
          if (this.open !== e2) return;
          const n2 = e2 ? "sp-opened" : "sp-closed";
          if (s2 > 0) {
            t2.dispatchEvent(new OverlayStateEvent(n2, this, {
              interaction: this.type,
              publish: false
            }));
            return;
          }
          const r2 = () => __async(this, null, function* () {
            if (this.open !== e2) return;
            yield nextFrame();
            const d2 = this.triggerElement instanceof VirtualTrigger;
            this.dispatchEvent(new OverlayStateEvent(n2, this, {
              interaction: this.type,
              publish: d2
            })), t2.dispatchEvent(new OverlayStateEvent(n2, this, {
              interaction: this.type,
              publish: false
            })), this.triggerElement && !d2 && this.triggerElement.dispatchEvent(new OverlayStateEvent(n2, this, {
              interaction: this.type,
              publish: true
            })), this.state = e2 ? "opened" : "closed", this.returnFocus(), yield nextFrame(), yield nextFrame(), e2 === this.open && e2 === false && this.requestSlottable();
          });
          if (this.open !== e2) return;
          const v2 = f(this.dialogEl);
          e2 !== true && v2 && this.isConnected ? (this.dialogEl.addEventListener("beforetoggle", () => {
            r2();
          }, {
            once: true
          }), this.dialogEl.hidePopover()) : r2();
        });
        return this.elements.forEach((t2, s2) => {
          guaranteedAllTransitionend(t2, i3(t2, s2), u2(t2, s2));
        }), o2;
      });
    }
  }
  return c2;
}
function OverlayNoPopover(a2) {
  class m2 extends a2 {
    managePopoverOpen() {
      return __async(this, null, function* () {
        yield this.managePosition();
      });
    }
    manageDelay(e2) {
      return __async(this, null, function* () {
        if (e2 === false || e2 !== this.open) {
          overlayTimer.close(this);
          return;
        }
        this.delayed && (yield overlayTimer.openTimer(this)) && (this.open = !e2);
      });
    }
    ensureOnDOM(e2) {
      return __async(this, null, function* () {
      });
    }
    makeTransition(e2) {
      return __async(this, null, function* () {
        if (this.open !== e2) return null;
        let o2 = null;
        const h3 = (t2, r2) => () => {
          if (e2 !== this.open) return;
          if (t2.open = e2, r2 === 0) {
            const i3 = e2 ? BeforetoggleOpenEvent : BeforetoggleClosedEvent;
            this.dispatchEvent(new i3());
          }
          if (e2 !== true || (t2.matches(userFocusableSelector) && (o2 = t2), o2 = o2 || firstFocusableIn(t2), o2)) return;
          t2.querySelectorAll("slot").forEach((i3) => {
            o2 || (o2 = firstFocusableSlottedIn(i3));
          });
        }, u2 = (t2, r2) => () => __async(this, null, function* () {
          if (this.open !== e2) return;
          const n2 = e2 ? "sp-opened" : "sp-closed";
          if (t2.dispatchEvent(new OverlayStateEvent(n2, this, {
            interaction: this.type
          })), r2 > 0) return;
          const i3 = this.triggerElement instanceof VirtualTrigger;
          this.dispatchEvent(new OverlayStateEvent(n2, this, {
            interaction: this.type,
            publish: i3
          })), this.triggerElement && !i3 && this.triggerElement.dispatchEvent(new OverlayStateEvent(n2, this, {
            interaction: this.type,
            publish: true
          })), this.state = e2 ? "opened" : "closed", this.returnFocus(), yield nextFrame(), yield nextFrame(), e2 === this.open && e2 === false && this.requestSlottable();
        });
        return this.elements.forEach((t2, r2) => {
          guaranteedAllTransitionend(t2, h3(t2, r2), u2(t2, r2));
        }), o2;
      });
    }
  }
  return m2;
}
var h2 = "showPopover" in document.createElement("div");
var c$1 = class {
  constructor() {
    this.root = document.body;
    this.stack = [];
    this.handlePointerdown = (t2) => {
      this.pointerdownPath = t2.composedPath(), this.lastOverlay = this.stack.at(-1);
    };
    this.handlePointerup = () => {
      var r2;
      if (!this.stack.length || !((r2 = this.pointerdownPath) != null && r2.length)) return;
      const t2 = this.pointerdownPath;
      this.pointerdownPath = void 0;
      const e2 = this.stack.length - 1, s2 = this.stack.filter((n2, i3) => !t2.find((o2) => o2 === n2 || o2 === (n2 == null ? void 0 : n2.triggerElement) && (n2 == null ? void 0 : n2.type) === "hint" || i3 === e2 && n2 !== this.lastOverlay && n2.triggerInteraction === "longpress") && !n2.shouldPreventClose() && n2.type !== "manual");
      s2.reverse(), s2.forEach((n2) => {
        this.closeOverlay(n2);
        let i3 = n2.parentOverlayToForceClose;
        for (; i3; ) this.closeOverlay(i3), i3 = i3.parentOverlayToForceClose;
      });
    };
    this.handleBeforetoggle = (t2) => {
      const {
        target: e2,
        newState: s2
      } = t2;
      s2 !== "open" && this.closeOverlay(e2);
    };
    this.handleKeydown = (t2) => {
      if (t2.code !== "Escape" || !this.stack.length) return;
      const e2 = this.stack.at(-1);
      if ((e2 == null ? void 0 : e2.type) === "page") {
        t2.preventDefault();
        return;
      }
      h2 || (e2 == null ? void 0 : e2.type) !== "manual" && e2 && this.closeOverlay(e2);
    };
    this.bindEvents();
  }
  get document() {
    return this.root.ownerDocument || document;
  }
  bindEvents() {
    this.document.addEventListener("pointerdown", this.handlePointerdown), this.document.addEventListener("pointerup", this.handlePointerup), this.document.addEventListener("keydown", this.handleKeydown);
  }
  closeOverlay(t2) {
    const e2 = this.stack.indexOf(t2);
    e2 > -1 && this.stack.splice(e2, 1), t2.open = false;
  }
  overlaysByTriggerElement(t2) {
    return this.stack.filter((e2) => e2.triggerElement === t2);
  }
  add(t2) {
    if (this.stack.includes(t2)) {
      const e2 = this.stack.indexOf(t2);
      e2 > -1 && (this.stack.splice(e2, 1), this.stack.push(t2));
      return;
    }
    if (t2.type === "auto" || t2.type === "modal" || t2.type === "page") {
      const e2 = "sp-overlay-query-path", s2 = new Event(e2, {
        composed: true,
        bubbles: true
      });
      t2.addEventListener(e2, (r2) => {
        const n2 = r2.composedPath();
        this.stack.forEach((i3) => {
          !n2.find((o2) => o2 === i3) && i3.type !== "manual" && this.closeOverlay(i3);
        });
      }, {
        once: true
      }), t2.dispatchEvent(s2);
    } else if (t2.type === "hint") {
      if (this.stack.some((s2) => s2.type !== "manual" && s2.triggerElement && s2.triggerElement === t2.triggerElement)) {
        t2.open = false;
        return;
      }
      this.stack.forEach((s2) => {
        s2.type === "hint" && this.closeOverlay(s2);
      });
    }
    requestAnimationFrame(() => {
      this.stack.push(t2), t2.addEventListener("beforetoggle", this.handleBeforetoggle, {
        once: true
      });
    });
  }
  remove(t2) {
    this.closeOverlay(t2);
  }
};
var overlayStack = new c$1();
var min = Math.min;
var max = Math.max;
var round = Math.round;
var floor = Math.floor;
var createCoords = (v2) => ({
  x: v2,
  y: v2
});
var oppositeSideMap = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
var oppositeAlignmentMap = {
  start: "end",
  end: "start"
};
function clamp(start, value, end) {
  return max(start, min(value, end));
}
function evaluate(value, param) {
  return typeof value === "function" ? value(param) : value;
}
function getSide(placement) {
  return placement.split("-")[0];
}
function getAlignment(placement) {
  return placement.split("-")[1];
}
function getOppositeAxis(axis) {
  return axis === "x" ? "y" : "x";
}
function getAxisLength(axis) {
  return axis === "y" ? "height" : "width";
}
function getSideAxis(placement) {
  return ["top", "bottom"].includes(getSide(placement)) ? "y" : "x";
}
function getAlignmentAxis(placement) {
  return getOppositeAxis(getSideAxis(placement));
}
function getAlignmentSides(placement, rects, rtl) {
  if (rtl === void 0) {
    rtl = false;
  }
  const alignment = getAlignment(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const length = getAxisLength(alignmentAxis);
  let mainAlignmentSide = alignmentAxis === "x" ? alignment === (rtl ? "end" : "start") ? "right" : "left" : alignment === "start" ? "bottom" : "top";
  if (rects.reference[length] > rects.floating[length]) {
    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);
  }
  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];
}
function getExpandedPlacements(placement) {
  const oppositePlacement = getOppositePlacement(placement);
  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];
}
function getOppositeAlignmentPlacement(placement) {
  return placement.replace(/start|end/g, (alignment) => oppositeAlignmentMap[alignment]);
}
function getSideList(side, isStart, rtl) {
  const lr = ["left", "right"];
  const rl = ["right", "left"];
  const tb = ["top", "bottom"];
  const bt = ["bottom", "top"];
  switch (side) {
    case "top":
    case "bottom":
      if (rtl) return isStart ? rl : lr;
      return isStart ? lr : rl;
    case "left":
    case "right":
      return isStart ? tb : bt;
    default:
      return [];
  }
}
function getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {
  const alignment = getAlignment(placement);
  let list = getSideList(getSide(placement), direction === "start", rtl);
  if (alignment) {
    list = list.map((side) => side + "-" + alignment);
    if (flipAlignment) {
      list = list.concat(list.map(getOppositeAlignmentPlacement));
    }
  }
  return list;
}
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, (side) => oppositeSideMap[side]);
}
function expandPaddingObject(padding) {
  return __spreadValues({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }, padding);
}
function getPaddingObject(padding) {
  return typeof padding !== "number" ? expandPaddingObject(padding) : {
    top: padding,
    right: padding,
    bottom: padding,
    left: padding
  };
}
function rectToClientRect(rect) {
  const {
    x: x2,
    y: y2,
    width,
    height
  } = rect;
  return {
    width,
    height,
    top: y2,
    left: x2,
    right: x2 + width,
    bottom: y2 + height,
    x: x2,
    y: y2
  };
}
function computeCoordsFromPlacement(_ref, placement, rtl) {
  let {
    reference,
    floating
  } = _ref;
  const sideAxis = getSideAxis(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const alignLength = getAxisLength(alignmentAxis);
  const side = getSide(placement);
  const isVertical = sideAxis === "y";
  const commonX = reference.x + reference.width / 2 - floating.width / 2;
  const commonY = reference.y + reference.height / 2 - floating.height / 2;
  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;
  let coords;
  switch (side) {
    case "top":
      coords = {
        x: commonX,
        y: reference.y - floating.height
      };
      break;
    case "bottom":
      coords = {
        x: commonX,
        y: reference.y + reference.height
      };
      break;
    case "right":
      coords = {
        x: reference.x + reference.width,
        y: commonY
      };
      break;
    case "left":
      coords = {
        x: reference.x - floating.width,
        y: commonY
      };
      break;
    default:
      coords = {
        x: reference.x,
        y: reference.y
      };
  }
  switch (getAlignment(placement)) {
    case "start":
      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);
      break;
    case "end":
      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);
      break;
  }
  return coords;
}
var computePosition$1 = (reference, floating, config) => __async(void 0, null, function* () {
  const {
    placement = "bottom",
    strategy = "absolute",
    middleware = [],
    platform: platform2
  } = config;
  const validMiddleware = middleware.filter(Boolean);
  const rtl = yield platform2.isRTL == null ? void 0 : platform2.isRTL(floating);
  let rects = yield platform2.getElementRects({
    reference,
    floating,
    strategy
  });
  let {
    x: x2,
    y: y2
  } = computeCoordsFromPlacement(rects, placement, rtl);
  let statefulPlacement = placement;
  let middlewareData = {};
  let resetCount = 0;
  for (let i3 = 0; i3 < validMiddleware.length; i3++) {
    const {
      name,
      fn
    } = validMiddleware[i3];
    const {
      x: nextX,
      y: nextY,
      data,
      reset
    } = yield fn({
      x: x2,
      y: y2,
      initialPlacement: placement,
      placement: statefulPlacement,
      strategy,
      middlewareData,
      rects,
      platform: platform2,
      elements: {
        reference,
        floating
      }
    });
    x2 = nextX != null ? nextX : x2;
    y2 = nextY != null ? nextY : y2;
    middlewareData = __spreadProps(__spreadValues({}, middlewareData), {
      [name]: __spreadValues(__spreadValues({}, middlewareData[name]), data)
    });
    if (reset && resetCount <= 50) {
      resetCount++;
      if (typeof reset === "object") {
        if (reset.placement) {
          statefulPlacement = reset.placement;
        }
        if (reset.rects) {
          rects = reset.rects === true ? yield platform2.getElementRects({
            reference,
            floating,
            strategy
          }) : reset.rects;
        }
        ({
          x: x2,
          y: y2
        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));
      }
      i3 = -1;
    }
  }
  return {
    x: x2,
    y: y2,
    placement: statefulPlacement,
    strategy,
    middlewareData
  };
});
function detectOverflow(state, options) {
  return __async(this, null, function* () {
    var _await$platform$isEle;
    if (options === void 0) {
      options = {};
    }
    const {
      x: x2,
      y: y2,
      platform: platform2,
      rects,
      elements,
      strategy
    } = state;
    const {
      boundary = "clippingAncestors",
      rootBoundary = "viewport",
      elementContext = "floating",
      altBoundary = false,
      padding = 0
    } = evaluate(options, state);
    const paddingObject = getPaddingObject(padding);
    const altContext = elementContext === "floating" ? "reference" : "floating";
    const element = elements[altBoundary ? altContext : elementContext];
    const clippingClientRect = rectToClientRect(yield platform2.getClippingRect({
      element: ((_await$platform$isEle = yield platform2.isElement == null ? void 0 : platform2.isElement(element)) != null ? _await$platform$isEle : true) ? element : element.contextElement || (yield platform2.getDocumentElement == null ? void 0 : platform2.getDocumentElement(elements.floating)),
      boundary,
      rootBoundary,
      strategy
    }));
    const rect = elementContext === "floating" ? {
      x: x2,
      y: y2,
      width: rects.floating.width,
      height: rects.floating.height
    } : rects.reference;
    const offsetParent = yield platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(elements.floating);
    const offsetScale = (yield platform2.isElement == null ? void 0 : platform2.isElement(offsetParent)) ? (yield platform2.getScale == null ? void 0 : platform2.getScale(offsetParent)) || {
      x: 1,
      y: 1
    } : {
      x: 1,
      y: 1
    };
    const elementClientRect = rectToClientRect(platform2.convertOffsetParentRelativeRectToViewportRelativeRect ? yield platform2.convertOffsetParentRelativeRectToViewportRelativeRect({
      elements,
      rect,
      offsetParent,
      strategy
    }) : rect);
    return {
      top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,
      bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,
      left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,
      right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x
    };
  });
}
var arrow$1 = (options) => ({
  name: "arrow",
  options,
  fn(state) {
    return __async(this, null, function* () {
      const {
        x: x2,
        y: y2,
        placement,
        rects,
        platform: platform2,
        elements,
        middlewareData
      } = state;
      const {
        element,
        padding = 0
      } = evaluate(options, state) || {};
      if (element == null) {
        return {};
      }
      const paddingObject = getPaddingObject(padding);
      const coords = {
        x: x2,
        y: y2
      };
      const axis = getAlignmentAxis(placement);
      const length = getAxisLength(axis);
      const arrowDimensions = yield platform2.getDimensions(element);
      const isYAxis = axis === "y";
      const minProp = isYAxis ? "top" : "left";
      const maxProp = isYAxis ? "bottom" : "right";
      const clientProp = isYAxis ? "clientHeight" : "clientWidth";
      const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];
      const startDiff = coords[axis] - rects.reference[axis];
      const arrowOffsetParent = yield platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(element);
      let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;
      if (!clientSize || !(yield platform2.isElement == null ? void 0 : platform2.isElement(arrowOffsetParent))) {
        clientSize = elements.floating[clientProp] || rects.floating[length];
      }
      const centerToReference = endDiff / 2 - startDiff / 2;
      const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;
      const minPadding = min(paddingObject[minProp], largestPossiblePadding);
      const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);
      const min$1 = minPadding;
      const max2 = clientSize - arrowDimensions[length] - maxPadding;
      const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;
      const offset2 = clamp(min$1, center, max2);
      const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset2 && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;
      const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max2 : 0;
      return {
        [axis]: coords[axis] + alignmentOffset,
        data: __spreadValues({
          [axis]: offset2,
          centerOffset: center - offset2 - alignmentOffset
        }, shouldAddOffset && {
          alignmentOffset
        }),
        reset: shouldAddOffset
      };
    });
  }
});
var flip$1 = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "flip",
    options,
    fn(state) {
      return __async(this, null, function* () {
        var _middlewareData$arrow, _middlewareData$flip;
        const {
          placement,
          middlewareData,
          rects,
          initialPlacement,
          platform: platform2,
          elements
        } = state;
        const _a2 = evaluate(options, state), {
          mainAxis: checkMainAxis = true,
          crossAxis: checkCrossAxis = true,
          fallbackPlacements: specifiedFallbackPlacements,
          fallbackStrategy = "bestFit",
          fallbackAxisSideDirection = "none",
          flipAlignment = true
        } = _a2, detectOverflowOptions = __objRest(_a2, [
          "mainAxis",
          "crossAxis",
          "fallbackPlacements",
          "fallbackStrategy",
          "fallbackAxisSideDirection",
          "flipAlignment"
        ]);
        if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
          return {};
        }
        const side = getSide(placement);
        const isBasePlacement = getSide(initialPlacement) === initialPlacement;
        const rtl = yield platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating);
        const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));
        if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== "none") {
          fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));
        }
        const placements = [initialPlacement, ...fallbackPlacements];
        const overflow = yield detectOverflow(state, detectOverflowOptions);
        const overflows = [];
        let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];
        if (checkMainAxis) {
          overflows.push(overflow[side]);
        }
        if (checkCrossAxis) {
          const sides = getAlignmentSides(placement, rects, rtl);
          overflows.push(overflow[sides[0]], overflow[sides[1]]);
        }
        overflowsData = [...overflowsData, {
          placement,
          overflows
        }];
        if (!overflows.every((side2) => side2 <= 0)) {
          var _middlewareData$flip2, _overflowsData$filter;
          const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;
          const nextPlacement = placements[nextIndex];
          if (nextPlacement) {
            return {
              data: {
                index: nextIndex,
                overflows: overflowsData
              },
              reset: {
                placement: nextPlacement
              }
            };
          }
          let resetPlacement = (_overflowsData$filter = overflowsData.filter((d2) => d2.overflows[0] <= 0).sort((a2, b2) => a2.overflows[1] - b2.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;
          if (!resetPlacement) {
            switch (fallbackStrategy) {
              case "bestFit": {
                var _overflowsData$map$so;
                const placement2 = (_overflowsData$map$so = overflowsData.map((d2) => [d2.placement, d2.overflows.filter((overflow2) => overflow2 > 0).reduce((acc, overflow2) => acc + overflow2, 0)]).sort((a2, b2) => a2[1] - b2[1])[0]) == null ? void 0 : _overflowsData$map$so[0];
                if (placement2) {
                  resetPlacement = placement2;
                }
                break;
              }
              case "initialPlacement":
                resetPlacement = initialPlacement;
                break;
            }
          }
          if (placement !== resetPlacement) {
            return {
              reset: {
                placement: resetPlacement
              }
            };
          }
        }
        return {};
      });
    }
  };
};
function convertValueToCoords(state, options) {
  return __async(this, null, function* () {
    const {
      placement,
      platform: platform2,
      elements
    } = state;
    const rtl = yield platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating);
    const side = getSide(placement);
    const alignment = getAlignment(placement);
    const isVertical = getSideAxis(placement) === "y";
    const mainAxisMulti = ["left", "top"].includes(side) ? -1 : 1;
    const crossAxisMulti = rtl && isVertical ? -1 : 1;
    const rawValue = evaluate(options, state);
    let {
      mainAxis,
      crossAxis,
      alignmentAxis
    } = typeof rawValue === "number" ? {
      mainAxis: rawValue,
      crossAxis: 0,
      alignmentAxis: null
    } : __spreadValues({
      mainAxis: 0,
      crossAxis: 0,
      alignmentAxis: null
    }, rawValue);
    if (alignment && typeof alignmentAxis === "number") {
      crossAxis = alignment === "end" ? alignmentAxis * -1 : alignmentAxis;
    }
    return isVertical ? {
      x: crossAxis * crossAxisMulti,
      y: mainAxis * mainAxisMulti
    } : {
      x: mainAxis * mainAxisMulti,
      y: crossAxis * crossAxisMulti
    };
  });
}
var offset$1 = function(options) {
  if (options === void 0) {
    options = 0;
  }
  return {
    name: "offset",
    options,
    fn(state) {
      return __async(this, null, function* () {
        var _middlewareData$offse, _middlewareData$arrow;
        const {
          x: x2,
          y: y2,
          placement,
          middlewareData
        } = state;
        const diffCoords = yield convertValueToCoords(state, options);
        if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
          return {};
        }
        return {
          x: x2 + diffCoords.x,
          y: y2 + diffCoords.y,
          data: __spreadProps(__spreadValues({}, diffCoords), {
            placement
          })
        };
      });
    }
  };
};
var shift$1 = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "shift",
    options,
    fn(state) {
      return __async(this, null, function* () {
        const {
          x: x2,
          y: y2,
          placement
        } = state;
        const _a2 = evaluate(options, state), {
          mainAxis: checkMainAxis = true,
          crossAxis: checkCrossAxis = false,
          limiter = {
            fn: (_ref) => {
              let {
                x: x3,
                y: y3
              } = _ref;
              return {
                x: x3,
                y: y3
              };
            }
          }
        } = _a2, detectOverflowOptions = __objRest(_a2, [
          "mainAxis",
          "crossAxis",
          "limiter"
        ]);
        const coords = {
          x: x2,
          y: y2
        };
        const overflow = yield detectOverflow(state, detectOverflowOptions);
        const crossAxis = getSideAxis(getSide(placement));
        const mainAxis = getOppositeAxis(crossAxis);
        let mainAxisCoord = coords[mainAxis];
        let crossAxisCoord = coords[crossAxis];
        if (checkMainAxis) {
          const minSide = mainAxis === "y" ? "top" : "left";
          const maxSide = mainAxis === "y" ? "bottom" : "right";
          const min2 = mainAxisCoord + overflow[minSide];
          const max2 = mainAxisCoord - overflow[maxSide];
          mainAxisCoord = clamp(min2, mainAxisCoord, max2);
        }
        if (checkCrossAxis) {
          const minSide = crossAxis === "y" ? "top" : "left";
          const maxSide = crossAxis === "y" ? "bottom" : "right";
          const min2 = crossAxisCoord + overflow[minSide];
          const max2 = crossAxisCoord - overflow[maxSide];
          crossAxisCoord = clamp(min2, crossAxisCoord, max2);
        }
        const limitedCoords = limiter.fn(__spreadProps(__spreadValues({}, state), {
          [mainAxis]: mainAxisCoord,
          [crossAxis]: crossAxisCoord
        }));
        return __spreadProps(__spreadValues({}, limitedCoords), {
          data: {
            x: limitedCoords.x - x2,
            y: limitedCoords.y - y2
          }
        });
      });
    }
  };
};
var size$1 = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "size",
    options,
    fn(state) {
      return __async(this, null, function* () {
        const {
          placement,
          rects,
          platform: platform2,
          elements
        } = state;
        const _a2 = evaluate(options, state), {
          apply = () => {
          }
        } = _a2, detectOverflowOptions = __objRest(_a2, [
          "apply"
        ]);
        const overflow = yield detectOverflow(state, detectOverflowOptions);
        const side = getSide(placement);
        const alignment = getAlignment(placement);
        const isYAxis = getSideAxis(placement) === "y";
        const {
          width,
          height
        } = rects.floating;
        let heightSide;
        let widthSide;
        if (side === "top" || side === "bottom") {
          heightSide = side;
          widthSide = alignment === ((yield platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating)) ? "start" : "end") ? "left" : "right";
        } else {
          widthSide = side;
          heightSide = alignment === "end" ? "top" : "bottom";
        }
        const overflowAvailableHeight = height - overflow[heightSide];
        const overflowAvailableWidth = width - overflow[widthSide];
        const noShift = !state.middlewareData.shift;
        let availableHeight = overflowAvailableHeight;
        let availableWidth = overflowAvailableWidth;
        if (isYAxis) {
          const maximumClippingWidth = width - overflow.left - overflow.right;
          availableWidth = alignment || noShift ? min(overflowAvailableWidth, maximumClippingWidth) : maximumClippingWidth;
        } else {
          const maximumClippingHeight = height - overflow.top - overflow.bottom;
          availableHeight = alignment || noShift ? min(overflowAvailableHeight, maximumClippingHeight) : maximumClippingHeight;
        }
        if (noShift && !alignment) {
          const xMin = max(overflow.left, 0);
          const xMax = max(overflow.right, 0);
          const yMin = max(overflow.top, 0);
          const yMax = max(overflow.bottom, 0);
          if (isYAxis) {
            availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));
          } else {
            availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));
          }
        }
        yield apply(__spreadProps(__spreadValues({}, state), {
          availableWidth,
          availableHeight
        }));
        const nextDimensions = yield platform2.getDimensions(elements.floating);
        if (width !== nextDimensions.width || height !== nextDimensions.height) {
          return {
            reset: {
              rects: true
            }
          };
        }
        return {};
      });
    }
  };
};
function getNodeName(node) {
  if (isNode(node)) {
    return (node.nodeName || "").toLowerCase();
  }
  return "#document";
}
function getWindow(node) {
  var _node$ownerDocument;
  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;
}
function getDocumentElement(node) {
  var _ref;
  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;
}
function isNode(value) {
  return value instanceof Node || value instanceof getWindow(value).Node;
}
function isElement(value) {
  return value instanceof Element || value instanceof getWindow(value).Element;
}
function isHTMLElement(value) {
  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;
}
function isShadowRoot(value) {
  if (typeof ShadowRoot === "undefined") {
    return false;
  }
  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;
}
function isOverflowElement(element) {
  const {
    overflow,
    overflowX,
    overflowY,
    display
  } = getComputedStyle(element);
  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !["inline", "contents"].includes(display);
}
function isTableElement(element) {
  return ["table", "td", "th"].includes(getNodeName(element));
}
function isContainingBlock(element) {
  const webkit = isWebKit();
  const css = getComputedStyle(element);
  return css.transform !== "none" || css.perspective !== "none" || (css.containerType ? css.containerType !== "normal" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== "none" : false) || !webkit && (css.filter ? css.filter !== "none" : false) || ["transform", "perspective", "filter"].some((value) => (css.willChange || "").includes(value)) || ["paint", "layout", "strict", "content"].some((value) => (css.contain || "").includes(value));
}
function getContainingBlock(element) {
  let currentNode = getParentNode(element);
  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {
    if (isContainingBlock(currentNode)) {
      return currentNode;
    }
    currentNode = getParentNode(currentNode);
  }
  return null;
}
function isWebKit() {
  if (typeof CSS === "undefined" || !CSS.supports) return false;
  return CSS.supports("-webkit-backdrop-filter", "none");
}
function isLastTraversableNode(node) {
  return ["html", "body", "#document"].includes(getNodeName(node));
}
function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}
function getNodeScroll(element) {
  if (isElement(element)) {
    return {
      scrollLeft: element.scrollLeft,
      scrollTop: element.scrollTop
    };
  }
  return {
    scrollLeft: element.pageXOffset,
    scrollTop: element.pageYOffset
  };
}
function getParentNode(node) {
  if (getNodeName(node) === "html") {
    return node;
  }
  const result = (
    // Step into the shadow DOM of the parent of a slotted node.
    node.assignedSlot || // DOM Element detected.
    node.parentNode || // ShadowRoot detected.
    isShadowRoot(node) && node.host || // Fallback.
    getDocumentElement(node)
  );
  return isShadowRoot(result) ? result.host : result;
}
function getNearestOverflowAncestor(node) {
  const parentNode = getParentNode(node);
  if (isLastTraversableNode(parentNode)) {
    return node.ownerDocument ? node.ownerDocument.body : node.body;
  }
  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {
    return parentNode;
  }
  return getNearestOverflowAncestor(parentNode);
}
function getOverflowAncestors(node, list, traverseIframes) {
  var _node$ownerDocument2;
  if (list === void 0) {
    list = [];
  }
  if (traverseIframes === void 0) {
    traverseIframes = true;
  }
  const scrollableAncestor = getNearestOverflowAncestor(node);
  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);
  const win = getWindow(scrollableAncestor);
  if (isBody) {
    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);
  }
  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));
}
function getCssDimensions(element) {
  const css = getComputedStyle(element);
  let width = parseFloat(css.width) || 0;
  let height = parseFloat(css.height) || 0;
  const hasOffset = isHTMLElement(element);
  const offsetWidth = hasOffset ? element.offsetWidth : width;
  const offsetHeight = hasOffset ? element.offsetHeight : height;
  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;
  if (shouldFallback) {
    width = offsetWidth;
    height = offsetHeight;
  }
  return {
    width,
    height,
    $: shouldFallback
  };
}
function unwrapElement(element) {
  return !isElement(element) ? element.contextElement : element;
}
function getScale(element) {
  const domElement = unwrapElement(element);
  if (!isHTMLElement(domElement)) {
    return createCoords(1);
  }
  const rect = domElement.getBoundingClientRect();
  const {
    width,
    height,
    $: $2
  } = getCssDimensions(domElement);
  let x2 = ($2 ? round(rect.width) : rect.width) / width;
  let y2 = ($2 ? round(rect.height) : rect.height) / height;
  if (!x2 || !Number.isFinite(x2)) {
    x2 = 1;
  }
  if (!y2 || !Number.isFinite(y2)) {
    y2 = 1;
  }
  return {
    x: x2,
    y: y2
  };
}
var noOffsets = createCoords(0);
function getVisualOffsets(element) {
  const win = getWindow(element);
  if (!isWebKit() || !win.visualViewport) {
    return noOffsets;
  }
  return {
    x: win.visualViewport.offsetLeft,
    y: win.visualViewport.offsetTop
  };
}
function shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {
    return false;
  }
  return isFixed;
}
function getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  const clientRect = element.getBoundingClientRect();
  const domElement = unwrapElement(element);
  let scale = createCoords(1);
  if (includeScale) {
    if (offsetParent) {
      if (isElement(offsetParent)) {
        scale = getScale(offsetParent);
      }
    } else {
      scale = getScale(element);
    }
  }
  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);
  let x2 = (clientRect.left + visualOffsets.x) / scale.x;
  let y2 = (clientRect.top + visualOffsets.y) / scale.y;
  let width = clientRect.width / scale.x;
  let height = clientRect.height / scale.y;
  if (domElement) {
    const win = getWindow(domElement);
    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;
    let currentWin = win;
    let currentIFrame = currentWin.frameElement;
    while (currentIFrame && offsetParent && offsetWin !== currentWin) {
      const iframeScale = getScale(currentIFrame);
      const iframeRect = currentIFrame.getBoundingClientRect();
      const css = getComputedStyle(currentIFrame);
      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;
      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;
      x2 *= iframeScale.x;
      y2 *= iframeScale.y;
      width *= iframeScale.x;
      height *= iframeScale.y;
      x2 += left;
      y2 += top;
      currentWin = getWindow(currentIFrame);
      currentIFrame = currentWin.frameElement;
    }
  }
  return rectToClientRect({
    width,
    height,
    x: x2,
    y: y2
  });
}
var topLayerSelectors = [":popover-open", ":modal"];
function isTopLayer(element) {
  return topLayerSelectors.some((selector) => {
    try {
      return element.matches(selector);
    } catch (e2) {
      return false;
    }
  });
}
function convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {
  let {
    elements,
    rect,
    offsetParent,
    strategy
  } = _ref;
  const isFixed = strategy === "fixed";
  const documentElement = getDocumentElement(offsetParent);
  const topLayer = elements ? isTopLayer(elements.floating) : false;
  if (offsetParent === documentElement || topLayer && isFixed) {
    return rect;
  }
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  let scale = createCoords(1);
  const offsets = createCoords(0);
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      const offsetRect = getBoundingClientRect(offsetParent);
      scale = getScale(offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    }
  }
  return {
    width: rect.width * scale.x,
    height: rect.height * scale.y,
    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,
    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y
  };
}
function getClientRects(element) {
  return Array.from(element.getClientRects());
}
function getWindowScrollBarX(element) {
  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;
}
function getDocumentRect(element) {
  const html = getDocumentElement(element);
  const scroll = getNodeScroll(element);
  const body = element.ownerDocument.body;
  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);
  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);
  let x2 = -scroll.scrollLeft + getWindowScrollBarX(element);
  const y2 = -scroll.scrollTop;
  if (getComputedStyle(body).direction === "rtl") {
    x2 += max(html.clientWidth, body.clientWidth) - width;
  }
  return {
    width,
    height,
    x: x2,
    y: y2
  };
}
function getViewportRect(element, strategy) {
  const win = getWindow(element);
  const html = getDocumentElement(element);
  const visualViewport = win.visualViewport;
  let width = html.clientWidth;
  let height = html.clientHeight;
  let x2 = 0;
  let y2 = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    const visualViewportBased = isWebKit();
    if (!visualViewportBased || visualViewportBased && strategy === "fixed") {
      x2 = visualViewport.offsetLeft;
      y2 = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x2,
    y: y2
  };
}
function getInnerBoundingClientRect(element, strategy) {
  const clientRect = getBoundingClientRect(element, true, strategy === "fixed");
  const top = clientRect.top + element.clientTop;
  const left = clientRect.left + element.clientLeft;
  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);
  const width = element.clientWidth * scale.x;
  const height = element.clientHeight * scale.y;
  const x2 = left * scale.x;
  const y2 = top * scale.y;
  return {
    width,
    height,
    x: x2,
    y: y2
  };
}
function getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {
  let rect;
  if (clippingAncestor === "viewport") {
    rect = getViewportRect(element, strategy);
  } else if (clippingAncestor === "document") {
    rect = getDocumentRect(getDocumentElement(element));
  } else if (isElement(clippingAncestor)) {
    rect = getInnerBoundingClientRect(clippingAncestor, strategy);
  } else {
    const visualOffsets = getVisualOffsets(element);
    rect = __spreadProps(__spreadValues({}, clippingAncestor), {
      x: clippingAncestor.x - visualOffsets.x,
      y: clippingAncestor.y - visualOffsets.y
    });
  }
  return rectToClientRect(rect);
}
function hasFixedPositionAncestor(element, stopNode) {
  const parentNode = getParentNode(element);
  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {
    return false;
  }
  return getComputedStyle(parentNode).position === "fixed" || hasFixedPositionAncestor(parentNode, stopNode);
}
function getClippingElementAncestors(element, cache) {
  const cachedResult = cache.get(element);
  if (cachedResult) {
    return cachedResult;
  }
  let result = getOverflowAncestors(element, [], false).filter((el) => isElement(el) && getNodeName(el) !== "body");
  let currentContainingBlockComputedStyle = null;
  const elementIsFixed = getComputedStyle(element).position === "fixed";
  let currentNode = elementIsFixed ? getParentNode(element) : element;
  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {
    const computedStyle = getComputedStyle(currentNode);
    const currentNodeIsContaining = isContainingBlock(currentNode);
    if (!currentNodeIsContaining && computedStyle.position === "fixed") {
      currentContainingBlockComputedStyle = null;
    }
    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === "static" && !!currentContainingBlockComputedStyle && ["absolute", "fixed"].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);
    if (shouldDropCurrentNode) {
      result = result.filter((ancestor) => ancestor !== currentNode);
    } else {
      currentContainingBlockComputedStyle = computedStyle;
    }
    currentNode = getParentNode(currentNode);
  }
  cache.set(element, result);
  return result;
}
function getClippingRect(_ref) {
  let {
    element,
    boundary,
    rootBoundary,
    strategy
  } = _ref;
  const elementClippingAncestors = boundary === "clippingAncestors" ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);
  const clippingAncestors = [...elementClippingAncestors, rootBoundary];
  const firstClippingAncestor = clippingAncestors[0];
  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {
    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));
  return {
    width: clippingRect.right - clippingRect.left,
    height: clippingRect.bottom - clippingRect.top,
    x: clippingRect.left,
    y: clippingRect.top
  };
}
function getDimensions(element) {
  const {
    width,
    height
  } = getCssDimensions(element);
  return {
    width,
    height
  };
}
function getRectRelativeToOffsetParent(element, offsetParent, strategy) {
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  const documentElement = getDocumentElement(offsetParent);
  const isFixed = strategy === "fixed";
  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  const offsets = createCoords(0);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isOffsetParentAnElement) {
      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  const x2 = rect.left + scroll.scrollLeft - offsets.x;
  const y2 = rect.top + scroll.scrollTop - offsets.y;
  return {
    x: x2,
    y: y2,
    width: rect.width,
    height: rect.height
  };
}
function isStaticPositioned(element) {
  return getComputedStyle(element).position === "static";
}
function getTrueOffsetParent(element, polyfill) {
  if (!isHTMLElement(element) || getComputedStyle(element).position === "fixed") {
    return null;
  }
  if (polyfill) {
    return polyfill(element);
  }
  return element.offsetParent;
}
function getOffsetParent(element, polyfill) {
  const win = getWindow(element);
  if (isTopLayer(element)) {
    return win;
  }
  if (!isHTMLElement(element)) {
    let svgOffsetParent = getParentNode(element);
    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {
      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {
        return svgOffsetParent;
      }
      svgOffsetParent = getParentNode(svgOffsetParent);
    }
    return win;
  }
  let offsetParent = getTrueOffsetParent(element, polyfill);
  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {
    offsetParent = getTrueOffsetParent(offsetParent, polyfill);
  }
  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {
    return win;
  }
  return offsetParent || getContainingBlock(element) || win;
}
var getElementRects = function(data) {
  return __async(this, null, function* () {
    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;
    const getDimensionsFn = this.getDimensions;
    const floatingDimensions = yield getDimensionsFn(data.floating);
    return {
      reference: getRectRelativeToOffsetParent(data.reference, yield getOffsetParentFn(data.floating), data.strategy),
      floating: {
        x: 0,
        y: 0,
        width: floatingDimensions.width,
        height: floatingDimensions.height
      }
    };
  });
};
function isRTL(element) {
  return getComputedStyle(element).direction === "rtl";
}
var platform = {
  convertOffsetParentRelativeRectToViewportRelativeRect,
  getDocumentElement,
  getClippingRect,
  getOffsetParent,
  getElementRects,
  getClientRects,
  getDimensions,
  getScale,
  isElement,
  isRTL
};
function observeMove(element, onMove) {
  let io = null;
  let timeoutId;
  const root = getDocumentElement(element);
  function cleanup() {
    var _io;
    clearTimeout(timeoutId);
    (_io = io) == null || _io.disconnect();
    io = null;
  }
  function refresh(skip, threshold) {
    if (skip === void 0) {
      skip = false;
    }
    if (threshold === void 0) {
      threshold = 1;
    }
    cleanup();
    const {
      left,
      top,
      width,
      height
    } = element.getBoundingClientRect();
    if (!skip) {
      onMove();
    }
    if (!width || !height) {
      return;
    }
    const insetTop = floor(top);
    const insetRight = floor(root.clientWidth - (left + width));
    const insetBottom = floor(root.clientHeight - (top + height));
    const insetLeft = floor(left);
    const rootMargin = -insetTop + "px " + -insetRight + "px " + -insetBottom + "px " + -insetLeft + "px";
    const options = {
      rootMargin,
      threshold: max(0, min(1, threshold)) || 1
    };
    let isFirstUpdate = true;
    function handleObserve(entries) {
      const ratio = entries[0].intersectionRatio;
      if (ratio !== threshold) {
        if (!isFirstUpdate) {
          return refresh();
        }
        if (!ratio) {
          timeoutId = setTimeout(() => {
            refresh(false, 1e-7);
          }, 1e3);
        } else {
          refresh(false, ratio);
        }
      }
      isFirstUpdate = false;
    }
    try {
      io = new IntersectionObserver(handleObserve, __spreadProps(__spreadValues({}, options), {
        // Handle <iframe>s
        root: root.ownerDocument
      }));
    } catch (e2) {
      io = new IntersectionObserver(handleObserve, options);
    }
    io.observe(element);
  }
  refresh(true);
  return cleanup;
}
function autoUpdate(reference, floating, update, options) {
  if (options === void 0) {
    options = {};
  }
  const {
    ancestorScroll = true,
    ancestorResize = true,
    elementResize = typeof ResizeObserver === "function",
    layoutShift = typeof IntersectionObserver === "function",
    animationFrame = false
  } = options;
  const referenceEl = unwrapElement(reference);
  const ancestors = ancestorScroll || ancestorResize ? [...referenceEl ? getOverflowAncestors(referenceEl) : [], ...getOverflowAncestors(floating)] : [];
  ancestors.forEach((ancestor) => {
    ancestorScroll && ancestor.addEventListener("scroll", update, {
      passive: true
    });
    ancestorResize && ancestor.addEventListener("resize", update);
  });
  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;
  let reobserveFrame = -1;
  let resizeObserver = null;
  if (elementResize) {
    resizeObserver = new ResizeObserver((_ref) => {
      let [firstEntry] = _ref;
      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {
        resizeObserver.unobserve(floating);
        cancelAnimationFrame(reobserveFrame);
        reobserveFrame = requestAnimationFrame(() => {
          var _resizeObserver;
          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);
        });
      }
      update();
    });
    if (referenceEl && !animationFrame) {
      resizeObserver.observe(referenceEl);
    }
    resizeObserver.observe(floating);
  }
  let frameId;
  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;
  if (animationFrame) {
    frameLoop();
  }
  function frameLoop() {
    const nextRefRect = getBoundingClientRect(reference);
    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {
      update();
    }
    prevRefRect = nextRefRect;
    frameId = requestAnimationFrame(frameLoop);
  }
  update();
  return () => {
    var _resizeObserver2;
    ancestors.forEach((ancestor) => {
      ancestorScroll && ancestor.removeEventListener("scroll", update);
      ancestorResize && ancestor.removeEventListener("resize", update);
    });
    cleanupIo == null || cleanupIo();
    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();
    resizeObserver = null;
    if (animationFrame) {
      cancelAnimationFrame(frameId);
    }
  };
}
var offset = offset$1;
var shift = shift$1;
var flip = flip$1;
var size = size$1;
var arrow = arrow$1;
var computePosition = (reference, floating, options) => {
  const cache = /* @__PURE__ */ new Map();
  const mergedOptions = __spreadValues({
    platform
  }, options);
  const platformWithCache = __spreadProps(__spreadValues({}, mergedOptions.platform), {
    _c: cache
  });
  return computePosition$1(reference, floating, __spreadProps(__spreadValues({}, mergedOptions), {
    platform: platformWithCache
  }));
};
function c(o2) {
  if (typeof o2 == "undefined") return 0;
  const t2 = window.devicePixelRatio || 1;
  return Math.round(o2 * t2) / t2;
}
var p$1 = 8;
var C = 100;
var T = (o2) => {
  var e2;
  return (e2 = {
    left: ["right", "bottom", "top"],
    "left-start": ["right-start", "bottom", "top"],
    "left-end": ["right-end", "bottom", "top"],
    right: ["left", "bottom", "top"],
    "right-start": ["left-start", "bottom", "top"],
    "right-end": ["left-end", "bottom", "top"],
    top: ["bottom", "left", "right"],
    "top-start": ["bottom-start", "left", "right"],
    "top-end": ["bottom-end", "left", "right"],
    bottom: ["top", "left", "right"],
    "bottom-start": ["top-start", "left", "right"],
    "bottom-end": ["top-end", "left", "right"]
  }[o2]) != null ? e2 : [o2];
};
var PlacementController = class {
  constructor(t2) {
    this.originalPlacements = /* @__PURE__ */ new WeakMap();
    this.allowPlacementUpdate = false;
    this.closeForAncestorUpdate = () => {
      !this.allowPlacementUpdate && this.options.type !== "modal" && this.cleanup && this.target.dispatchEvent(new Event("close", {
        bubbles: true
      })), this.allowPlacementUpdate = false;
    };
    this.updatePlacement = () => {
      this.computePlacement();
    };
    this.resetOverlayPosition = () => {
      !this.target || !this.options || (this.clearOverlayPosition(), this.computePlacement());
    };
    this.host = t2, this.host.addController(this);
  }
  placeOverlay() {
    return __async(this, arguments, function* (t2 = this.target, e2 = this.options) {
      if (this.target = t2, this.options = e2, !t2 || !e2) return;
      const m2 = autoUpdate(e2.trigger, t2, this.closeForAncestorUpdate, {
        ancestorResize: false,
        elementResize: false,
        layoutShift: false
      }), h3 = autoUpdate(e2.trigger, t2, this.updatePlacement, {
        ancestorScroll: false
      });
      this.cleanup = () => {
        var n2;
        (n2 = this.host.elements) == null || n2.forEach((a2) => {
          a2.addEventListener("sp-closed", () => {
            const r2 = this.originalPlacements.get(a2);
            r2 && a2.setAttribute("placement", r2), this.originalPlacements.delete(a2);
          }, {
            once: true
          });
        }), m2(), h3();
      };
    });
  }
  computePlacement() {
    return __async(this, null, function* () {
      var g2, u2;
      const {
        options: t2,
        target: e2
      } = this;
      yield document.fonts ? document.fonts.ready : Promise.resolve();
      const m2 = t2.trigger instanceof HTMLElement ? flip() : flip({
        padding: p$1,
        fallbackPlacements: T(t2.placement)
      }), [h3 = 0, n2 = 0] = Array.isArray(t2 == null ? void 0 : t2.offset) ? t2.offset : [t2.offset, 0], a2 = (g2 = this.host.elements.find((i3) => i3.tipElement)) == null ? void 0 : g2.tipElement, r2 = [offset({
        mainAxis: h3,
        crossAxis: n2
      }), shift({
        padding: p$1
      }), m2, size({
        padding: p$1,
        apply: ({
          availableWidth: i3,
          availableHeight: d2,
          rects: {
            floating: x2
          }
        }) => {
          const b2 = Math.max(C, Math.floor(d2)), l2 = x2.height;
          this.initialHeight = this.isConstrained && this.initialHeight || l2, this.isConstrained = l2 < this.initialHeight || b2 <= l2;
          const O = this.isConstrained ? `${b2}px` : "";
          Object.assign(e2.style, {
            maxWidth: `${Math.floor(i3)}px`,
            maxHeight: O
          });
        }
      }), ...a2 ? [arrow({
        element: a2,
        padding: t2.tipPadding || p$1
      })] : []], {
        x: P2,
        y: E2,
        placement: s2,
        middlewareData: f2
      } = yield computePosition(t2.trigger, e2, {
        placement: t2.placement,
        middleware: r2,
        strategy: "fixed"
      });
      if (Object.assign(e2.style, {
        top: "0px",
        left: "0px",
        translate: `${c(P2)}px ${c(E2)}px`
      }), e2.setAttribute("actual-placement", s2), (u2 = this.host.elements) == null || u2.forEach((i3) => {
        this.originalPlacements.has(i3) || this.originalPlacements.set(i3, i3.getAttribute("placement")), i3.setAttribute("placement", s2);
      }), a2 && f2.arrow) {
        const {
          x: i3,
          y: d2
        } = f2.arrow;
        Object.assign(a2.style, {
          top: s2.startsWith("right") || s2.startsWith("left") ? "0px" : "",
          left: s2.startsWith("bottom") || s2.startsWith("top") ? "0px" : "",
          translate: `${c(i3)}px ${c(d2)}px`
        });
      }
    });
  }
  clearOverlayPosition() {
    this.target && (this.target.style.removeProperty("max-height"), this.target.style.removeProperty("max-width"), this.initialHeight = void 0, this.isConstrained = false);
  }
  hostConnected() {
    document.addEventListener("sp-update-overlays", this.resetOverlayPosition);
  }
  hostUpdated() {
    var t2;
    this.host.open || ((t2 = this.cleanup) == null || t2.call(this), this.cleanup = void 0);
  }
  hostDisconnected() {
    var t2;
    (t2 = this.cleanup) == null || t2.call(this), this.cleanup = void 0, document.removeEventListener("sp-update-overlays", this.resetOverlayPosition);
  }
};
function conditionAttributeWithoutId(t2, i3, n2) {
  const e2 = t2.getAttribute(i3);
  let r2 = e2 ? e2.split(/\s+/) : [];
  r2 = r2.filter((s2) => !n2.find((o2) => s2 === o2)), r2.length ? t2.setAttribute(i3, r2.join(" ")) : t2.removeAttribute(i3);
}
function conditionAttributeWithId(t2, i3, n2) {
  const e2 = Array.isArray(n2) ? n2 : [n2], r2 = t2.getAttribute(i3), s2 = r2 ? r2.split(/\s+/) : [];
  return e2.every((d2) => s2.indexOf(d2) > -1) ? () => {
  } : (s2.push(...e2), t2.setAttribute(i3, s2.join(" ")), () => conditionAttributeWithoutId(t2, i3, e2));
}
var InteractionTypes = ((r2) => (r2[r2.click = 0] = "click", r2[r2.hover = 1] = "hover", r2[r2.longpress = 2] = "longpress", r2))(InteractionTypes || {});
var InteractionController = class {
  constructor(e2, {
    overlay: t2,
    isPersistent: r2,
    handleOverlayReady: i3
  }) {
    this.target = e2;
    this.isPersistent = false;
    this.isPersistent = !!r2, this.handleOverlayReady = i3, this.isPersistent && this.init(), this.overlay = t2;
  }
  get activelyOpening() {
    return false;
  }
  get open() {
    var e2, t2;
    return (t2 = (e2 = this.overlay) == null ? void 0 : e2.open) != null ? t2 : false;
  }
  set open(e2) {
    if (this.overlay) {
      this.overlay.open = e2;
      return;
    }
    e2 && (customElements.whenDefined("sp-overlay").then(() => __async(this, null, function* () {
      const {
        Overlay: t2
      } = yield Promise.resolve().then(function() {
        return Overlay$1;
      });
      this.overlay = new t2(), this.overlay.open = true;
    })), import("./sp-overlay-1351366a-G4KATWUU.js"));
  }
  get overlay() {
    return this._overlay;
  }
  set overlay(e2) {
    var t2;
    e2 && this.overlay !== e2 && (this.overlay && this.overlay.removeController(this), this._overlay = e2, this.overlay.addController(this), this.initOverlay(), this.prepareDescription(this.target), (t2 = this.handleOverlayReady) == null || t2.call(this, this.overlay));
  }
  prepareDescription(e2) {
  }
  releaseDescription() {
  }
  shouldCompleteOpen() {
  }
  init() {
  }
  initOverlay() {
  }
  abort() {
    var e2;
    this.releaseDescription(), (e2 = this.abortController) == null || e2.abort();
  }
  hostConnected() {
    this.init();
  }
  hostDisconnected() {
    this.isPersistent || this.abort();
  }
};
var g = 300;
var LONGPRESS_INSTRUCTIONS = {
  touch: "Double tap and long press for additional options",
  keyboard: "Press Space or Alt+Down Arrow for additional options",
  mouse: "Click and hold for additional options"
};
var LongpressController = class extends InteractionController {
  constructor() {
    super(...arguments);
    this.type = InteractionTypes.longpress;
    this.longpressState = null;
    this.releaseDescription = noop;
    this.handlePointerup = () => {
      var e2;
      clearTimeout(this.timeout), this.target && (this.longpressState = ((e2 = this.overlay) == null ? void 0 : e2.state) === "opening" ? "pressed" : null, document.removeEventListener("pointerup", this.handlePointerup), document.removeEventListener("pointercancel", this.handlePointerup));
    };
  }
  get activelyOpening() {
    return this.longpressState === "opening" || this.longpressState === "pressed";
  }
  handleLongpress() {
    this.open = true, this.longpressState = this.longpressState === "potential" ? "opening" : "pressed";
  }
  handlePointerdown(e2) {
    !this.target || e2.button !== 0 || (this.longpressState = "potential", document.addEventListener("pointerup", this.handlePointerup), document.addEventListener("pointercancel", this.handlePointerup), "holdAffordance" in this.target) || (this.timeout = setTimeout(() => {
      this.target && this.target.dispatchEvent(new CustomEvent("longpress", {
        bubbles: true,
        composed: true,
        detail: {
          source: "pointer"
        }
      }));
    }, g));
  }
  handleKeydown(e2) {
    const {
      code: t2,
      altKey: o2
    } = e2;
    o2 && t2 === "ArrowDown" && (e2.stopPropagation(), e2.stopImmediatePropagation());
  }
  handleKeyup(e2) {
    const {
      code: t2,
      altKey: o2
    } = e2;
    if (t2 === "Space" || o2 && t2 === "ArrowDown") {
      if (!this.target) return;
      e2.stopPropagation(), this.target.dispatchEvent(new CustomEvent("longpress", {
        bubbles: true,
        composed: true,
        detail: {
          source: "keyboard"
        }
      })), setTimeout(() => {
        this.longpressState = null;
      });
    }
  }
  prepareDescription(e2) {
    if (this.releaseDescription !== noop || !this.overlay.elements.length) return;
    const t2 = document.createElement("div");
    t2.id = `longpress-describedby-descriptor-${randomID()}`;
    const o2 = isIOS() || isAndroid() ? "touch" : "keyboard";
    t2.textContent = LONGPRESS_INSTRUCTIONS[o2], t2.slot = "longpress-describedby-descriptor";
    const n2 = e2.getRootNode(), s2 = this.overlay.getRootNode();
    n2 === s2 ? this.overlay.append(t2) : (t2.hidden = !("host" in n2), e2.insertAdjacentElement("afterend", t2));
    const i3 = conditionAttributeWithId(e2, "aria-describedby", [t2.id]);
    this.releaseDescription = () => {
      i3(), t2.remove(), this.releaseDescription = noop;
    };
  }
  shouldCompleteOpen() {
    this.longpressState = this.longpressState === "pressed" ? null : this.longpressState;
  }
  init() {
    var t2;
    (t2 = this.abortController) == null || t2.abort(), this.abortController = new AbortController();
    const {
      signal: e2
    } = this.abortController;
    this.target.addEventListener("longpress", () => this.handleLongpress(), {
      signal: e2
    }), this.target.addEventListener("pointerdown", (o2) => this.handlePointerdown(o2), {
      signal: e2
    }), this.prepareDescription(this.target), !this.target.holdAffordance && (this.target.addEventListener("keydown", (o2) => this.handleKeydown(o2), {
      signal: e2
    }), this.target.addEventListener("keyup", (o2) => this.handleKeyup(o2), {
      signal: e2
    }));
  }
};
var ClickController = class extends InteractionController {
  constructor() {
    super(...arguments);
    this.type = InteractionTypes.click;
    this.preventNextToggle = false;
  }
  handleClick() {
    this.preventNextToggle || (this.open = !this.open), this.preventNextToggle = false;
  }
  handlePointerdown() {
    this.preventNextToggle = this.open;
  }
  init() {
    var t2;
    (t2 = this.abortController) == null || t2.abort(), this.abortController = new AbortController();
    const {
      signal: e2
    } = this.abortController;
    this.target.addEventListener("click", () => this.handleClick(), {
      signal: e2
    }), this.target.addEventListener("pointerdown", () => this.handlePointerdown(), {
      signal: e2
    });
  }
};
var d = 300;
var HoverController = class extends InteractionController {
  constructor() {
    super(...arguments);
    this.type = InteractionTypes.hover;
    this.elementIds = [];
    this.focusedin = false;
    this.pointerentered = false;
  }
  handleTargetFocusin() {
    var e2;
    (e2 = document.activeElement) != null && e2.matches(":focus-visible") && (this.open = true, this.focusedin = true);
  }
  handleTargetFocusout() {
    this.focusedin = false, !this.pointerentered && (this.open = false);
  }
  handleTargetPointerenter() {
    var e2;
    this.hoverTimeout && (clearTimeout(this.hoverTimeout), this.hoverTimeout = void 0), !((e2 = this.overlay) != null && e2.disabled) && (this.open = true, this.pointerentered = true);
  }
  handleTargetPointerleave() {
    this.doPointerleave();
  }
  handleHostPointerenter() {
    this.hoverTimeout && (clearTimeout(this.hoverTimeout), this.hoverTimeout = void 0);
  }
  handleHostPointerleave() {
    this.doPointerleave();
  }
  prepareDescription() {
    if (!this.overlay.elements.length) return;
    const e2 = this.target.getRootNode(), t2 = this.overlay.elements[0].getRootNode(), r2 = this.overlay.getRootNode();
    e2 === r2 ? this.prepareOverlayRelativeDescription() : e2 === t2 && this.prepareContentRelativeDescription();
  }
  prepareOverlayRelativeDescription() {
    const e2 = conditionAttributeWithId(this.target, "aria-describedby", [this.overlay.id]);
    this.releaseDescription = () => {
      e2(), this.releaseDescription = noop;
    };
  }
  prepareContentRelativeDescription() {
    const e2 = [], t2 = this.overlay.elements.map((i3) => (e2.push(i3.id), i3.id || (i3.id = `${this.overlay.tagName.toLowerCase()}-helper-${randomID()}`), i3.id));
    this.elementIds = e2;
    const r2 = conditionAttributeWithId(this.target, "aria-describedby", t2);
    this.releaseDescription = () => {
      r2(), this.overlay.elements.map((i3, n2) => {
        i3.id = this.elementIds[n2];
      }), this.releaseDescription = noop;
    };
  }
  doPointerleave() {
    this.pointerentered = false;
    const e2 = this.target;
    this.focusedin && e2.matches(":focus-visible") || (this.hoverTimeout = setTimeout(() => {
      this.open = false;
    }, d));
  }
  init() {
    var t2;
    (t2 = this.abortController) == null || t2.abort(), this.abortController = new AbortController();
    const {
      signal: e2
    } = this.abortController;
    this.target.addEventListener("focusin", () => this.handleTargetFocusin(), {
      signal: e2
    }), this.target.addEventListener("focusout", () => this.handleTargetFocusout(), {
      signal: e2
    }), this.target.addEventListener("pointerenter", () => this.handleTargetPointerenter(), {
      signal: e2
    }), this.target.addEventListener("pointerleave", () => this.handleTargetPointerleave(), {
      signal: e2
    }), this.overlay && this.initOverlay();
  }
  initOverlay() {
    if (!this.abortController) return;
    const {
      signal: e2
    } = this.abortController;
    this.overlay.addEventListener("pointerenter", () => this.handleHostPointerenter(), {
      signal: e2
    }), this.overlay.addEventListener("pointerleave", () => this.handleHostPointerleave(), {
      signal: e2
    });
  }
};
var strategies = {
  click: ClickController,
  longpress: LongpressController,
  hover: HoverController
};
var SlottableRequestEvent = class extends Event {
  constructor(e2, n2, t2) {
    super("slottable-request", {
      bubbles: false,
      cancelable: true,
      composed: false
    }), this.name = e2, this.data = n2, this.slotName = t2 !== void 0 ? `${e2}.${t2}` : e2;
  }
};
var removeSlottableRequest = Symbol("remove-slottable-request");
var o = i$5`
    :host{pointer-events:none;--swc-overlay-animation-distance:var(--spectrum-spacing-100);display:contents}:host(:has(>sp-tooltip)){--swc-overlay-animation-distance:var(--spectrum-tooltip-animation-distance)}.dialog{box-sizing:border-box;--sp-overlay-open:true;background:0 0;border:0;max-width:calc(100vw - 16px);height:auto;max-height:calc(100dvh - 16px);margin:0;padding:0;display:flex;position:fixed;inset:0 auto auto 0;overflow:visible;opacity:1!important}.dialog:not([is-visible]){display:none}.dialog:focus{outline:none}dialog:modal{--mod-popover-filter:var(--spectrum-popover-filter)}:host(:not([open])) .dialog{--sp-overlay-open:false}.dialog::backdrop{display:none}.dialog:before{content:"";position:absolute;inset:-999em;pointer-events:auto!important}.dialog:not(.not-immediately-closable):before{display:none}.dialog>div{width:100%}::slotted(*){pointer-events:auto;visibility:visible!important}::slotted(sp-popover){position:static}.dialog:not([actual-placement])[placement*=top]{padding-block:var(--swc-overlay-animation-distance);margin-top:var(--swc-overlay-animation-distance)}.dialog:not([actual-placement])[placement*=right]{padding-inline:var(--swc-overlay-animation-distance);margin-left:calc(-1*var(--swc-overlay-animation-distance))}.dialog:not([actual-placement])[placement*=bottom]{padding-block:var(--swc-overlay-animation-distance);margin-top:calc(-1*var(--swc-overlay-animation-distance))}.dialog:not([actual-placement])[placement*=left]{padding-inline:var(--swc-overlay-animation-distance);margin-left:var(--swc-overlay-animation-distance)}.dialog[actual-placement*=top]{padding-block:var(--swc-overlay-animation-distance);margin-top:var(--swc-overlay-animation-distance)}.dialog[actual-placement*=right]{padding-inline:var(--swc-overlay-animation-distance);margin-left:calc(-1*var(--swc-overlay-animation-distance))}.dialog[actual-placement*=bottom]{padding-block:var(--swc-overlay-animation-distance);margin-top:calc(-1*var(--swc-overlay-animation-distance))}.dialog[actual-placement*=left]{padding-inline:var(--swc-overlay-animation-distance);margin-left:var(--swc-overlay-animation-distance)}slot[name=longpress-describedby-descriptor]{display:none}@supports selector(:open){.dialog{opacity:0}.dialog:open{opacity:1;--mod-popover-filter:var(--spectrum-popover-filter)}}@supports selector(:popover-open){.dialog{opacity:0}.dialog:popover-open{opacity:1;--mod-popover-filter:var(--spectrum-popover-filter)}}@supports (overlay:auto){.dialog{transition:all var(--mod-overlay-animation-duration,var(--spectrum-animation-duration-100,.13s)),translate 0s,display var(--mod-overlay-animation-duration,var(--spectrum-animation-duration-100,.13s));transition-behavior:allow-discrete;display:none}.dialog:popover-open,.dialog:modal{display:flex}}@supports (not selector(:open)) and (not selector(:popover-open)){:host:not([open]) .dialog{pointer-events:none}.dialog[actual-placement]{z-index:calc(var(--swc-overlay-z-index-base,1000) + var(--swc-overlay-open-count))}}
`;
var b = Object.defineProperty;
var E = Object.getOwnPropertyDescriptor;
var r = (u2, a2, e2, t2) => {
  for (var o2 = t2 > 1 ? void 0 : t2 ? E(a2, e2) : a2, s2 = u2.length - 1, l2; s2 >= 0; s2--) (l2 = u2[s2]) && (o2 = (t2 ? l2(a2, e2, o2) : l2(o2)) || o2);
  return t2 && o2 && b(a2, e2, o2), o2;
};
var B = "showPopover" in document.createElement("div");
var p = OverlayDialog(AbstractOverlay);
B ? p = OverlayPopover(p) : p = OverlayNoPopover(p);
var i = class i2 extends p {
  constructor() {
    super(...arguments);
    this._delayed = false;
    this._disabled = false;
    this.offset = 0;
    this._open = false;
    this.lastRequestSlottableState = false;
    this.receivesFocus = "auto";
    this._state = "closed";
    this.triggerElement = null;
    this.type = "auto";
    this.wasOpen = false;
    this.closeOnFocusOut = (e2) => {
      if (!e2.relatedTarget) return;
      const t2 = new Event("overlay-relation-query", {
        bubbles: true,
        composed: true
      });
      e2.relatedTarget.addEventListener(t2.type, (o2) => {
        o2.composedPath().includes(this) || (this.open = false);
      }), e2.relatedTarget.dispatchEvent(t2);
    };
  }
  get delayed() {
    var e2;
    return ((e2 = this.elements.at(-1)) == null ? void 0 : e2.hasAttribute("delayed")) || this._delayed;
  }
  set delayed(e2) {
    this._delayed = e2;
  }
  get disabled() {
    return this._disabled;
  }
  set disabled(e2) {
    var t2;
    this._disabled = e2, e2 ? ((t2 = this.strategy) == null || t2.abort(), this.wasOpen = this.open, this.open = false) : (this.bindEvents(), this.open = this.open || this.wasOpen, this.wasOpen = false);
  }
  get hasNonVirtualTrigger() {
    return !!this.triggerElement && !(this.triggerElement instanceof VirtualTrigger);
  }
  get placementController() {
    return this._placementController || (this._placementController = new PlacementController(this)), this._placementController;
  }
  get open() {
    return this._open;
  }
  set open(e2) {
    var t2;
    e2 && this.disabled || e2 !== this.open && ((t2 = this.strategy) != null && t2.activelyOpening && !e2 || (this._open = e2, this.open && (i2.openCount += 1), this.requestUpdate("open", !this.open), this.open && this.requestSlottable()));
  }
  get state() {
    return this._state;
  }
  set state(e2) {
    var o2;
    if (e2 === this.state) return;
    const t2 = this.state;
    this._state = e2, (this.state === "opened" || this.state === "closed") && ((o2 = this.strategy) == null || o2.shouldCompleteOpen()), this.requestUpdate("state", t2);
  }
  get elementResolver() {
    return this._elementResolver || (this._elementResolver = new ElementResolutionController(this)), this._elementResolver;
  }
  get usesDialog() {
    return this.type === "modal" || this.type === "page";
  }
  get popoverValue() {
    if ("popover" in this) switch (this.type) {
      case "modal":
      case "page":
        return;
      case "hint":
        return "manual";
      default:
        return this.type;
    }
  }
  get requiresPosition() {
    return !(this.type === "page" || !this.open || !this.triggerElement || !this.placement && this.type !== "hint");
  }
  managePosition() {
    if (!this.requiresPosition || !this.open) return;
    const e2 = this.offset || 0, t2 = this.triggerElement, o2 = this.placement || "right", s2 = this.tipPadding;
    this.placementController.placeOverlay(this.dialogEl, {
      offset: e2,
      placement: o2,
      tipPadding: s2,
      trigger: t2,
      type: this.type
    });
  }
  managePopoverOpen() {
    return __async(this, null, function* () {
      __superGet(i2.prototype, this, "managePopoverOpen").call(this);
      const e2 = this.open;
      if (this.open !== e2 || (yield this.manageDelay(e2), this.open !== e2) || (yield this.ensureOnDOM(e2), this.open !== e2)) return;
      const t2 = yield this.makeTransition(e2);
      this.open === e2 && (yield this.applyFocus(e2, t2));
    });
  }
  applyFocus(e2, t2) {
    return __async(this, null, function* () {
      if (!(this.receivesFocus === "false" || this.type === "hint")) {
        if (yield nextFrame(), yield nextFrame(), e2 === this.open && !this.open) {
          this.hasNonVirtualTrigger && this.contains(this.getRootNode().activeElement) && this.triggerElement.focus();
          return;
        }
        t2 == null || t2.focus();
      }
    });
  }
  returnFocus() {
    var t2;
    if (this.open || this.type === "hint") return;
    const e2 = () => {
      var l2, m2;
      const o2 = [];
      let s2 = document.activeElement;
      for (; (l2 = s2 == null ? void 0 : s2.shadowRoot) != null && l2.activeElement; ) s2 = s2.shadowRoot.activeElement;
      for (; s2; ) {
        const h3 = s2.assignedSlot || s2.parentElement || ((m2 = s2.getRootNode()) == null ? void 0 : m2.host);
        h3 && o2.push(h3), s2 = h3;
      }
      return o2;
    };
    this.receivesFocus !== "false" && (t2 = this.triggerElement) != null && t2.focus && (this.contains(this.getRootNode().activeElement) || e2().includes(this) || document.activeElement === document.body) && this.triggerElement.focus();
  }
  manageOpen(e2) {
    return __async(this, null, function* () {
      if (!(!this.isConnected && this.open) && (this.hasUpdated || (yield this.updateComplete), this.open ? (overlayStack.add(this), this.willPreventClose && (document.addEventListener("pointerup", () => {
        this.dialogEl.classList.toggle("not-immediately-closable", false), this.willPreventClose = false;
      }, {
        once: true
      }), this.dialogEl.classList.toggle("not-immediately-closable", true))) : (e2 && this.dispose(), overlayStack.remove(this)), this.open && this.state !== "opened" ? this.state = "opening" : !this.open && this.state !== "closed" && (this.state = "closing"), this.usesDialog ? this.manageDialogOpen() : this.managePopoverOpen(), this.type === "auto")) {
        const t2 = this.getRootNode();
        this.open ? t2.addEventListener("focusout", this.closeOnFocusOut, {
          capture: true
        }) : t2.removeEventListener("focusout", this.closeOnFocusOut, {
          capture: true
        });
      }
    });
  }
  bindEvents() {
    var e2;
    (e2 = this.strategy) == null || e2.abort(), this.strategy = void 0, this.hasNonVirtualTrigger && this.triggerInteraction && (this.strategy = new strategies[this.triggerInteraction](this.triggerElement, {
      overlay: this
    }));
  }
  handleBeforetoggle(e2) {
    e2.newState !== "open" && this.handleBrowserClose();
  }
  handleBrowserClose() {
    var e2;
    if (!((e2 = this.strategy) != null && e2.activelyOpening)) {
      this.open = false;
      return;
    }
    this.manuallyKeepOpen();
  }
  manuallyKeepOpen() {
    this.open = true, this.placementController.allowPlacementUpdate = true, this.manageOpen(false);
  }
  handleSlotchange() {
    var e2, t2;
    this.elements.length ? this.hasNonVirtualTrigger && ((t2 = this.strategy) == null || t2.prepareDescription(this.triggerElement)) : (e2 = this.strategy) == null || e2.releaseDescription();
  }
  shouldPreventClose() {
    const e2 = this.willPreventClose;
    return this.willPreventClose = false, e2;
  }
  requestSlottable() {
    this.lastRequestSlottableState !== this.open && (this.dispatchEvent(new SlottableRequestEvent("overlay-content", this.open ? {} : removeSlottableRequest)), this.lastRequestSlottableState = this.open);
  }
  willUpdate(e2) {
    var o2;
    if (this.hasAttribute("id") || this.setAttribute("id", `${this.tagName.toLowerCase()}-${randomID()}`), e2.has("open") && (this.hasUpdated || this.open) && this.manageOpen(e2.get("open")), e2.has("trigger")) {
      const [s2, l2] = ((o2 = this.trigger) == null ? void 0 : o2.split("@")) || [];
      this.elementResolver.selector = s2 ? `#${s2}` : "", this.triggerInteraction = l2;
    }
    let t2 = false;
    e2.has(elementResolverUpdatedSymbol) && (t2 = this.triggerElement, this.triggerElement = this.elementResolver.element), e2.has("triggerElement") && (t2 = e2.get("triggerElement")), t2 !== false && this.bindEvents();
  }
  updated(e2) {
    super.updated(e2), e2.has("placement") && (this.placement ? this.dialogEl.setAttribute("actual-placement", this.placement) : this.dialogEl.removeAttribute("actual-placement"), this.open && typeof e2.get("placement") != "undefined" && this.placementController.resetOverlayPosition()), e2.has("state") && this.state === "closed" && typeof e2.get("state") != "undefined" && this.placementController.clearOverlayPosition();
  }
  renderContent() {
    return x`
            <slot @slotchange=${this.handleSlotchange}></slot>
        `;
  }
  get dialogStyleMap() {
    return {
      "--swc-overlay-open-count": i2.openCount.toString()
    };
  }
  renderDialog() {
    return x`
            <dialog
                class="dialog"
                part="dialog"
                placement=${o$3(this.requiresPosition ? this.placement || "right" : void 0)}
                style=${o$2(this.dialogStyleMap)}
                @close=${this.handleBrowserClose}
                @cancel=${this.handleBrowserClose}
                @beforetoggle=${this.handleBeforetoggle}
                ?is-visible=${this.state !== "closed"}
            >
                ${this.renderContent()}
            </dialog>
        `;
  }
  renderPopover() {
    return x`
            <div
                class="dialog"
                part="dialog"
                placement=${o$3(this.requiresPosition ? this.placement || "right" : void 0)}
                popover=${o$3(this.popoverValue)}
                style=${o$2(this.dialogStyleMap)}
                @beforetoggle=${this.handleBeforetoggle}
                @close=${this.handleBrowserClose}
                ?is-visible=${this.state !== "closed"}
            >
                ${this.renderContent()}
            </div>
        `;
  }
  render() {
    const e2 = this.type === "modal" || this.type === "page";
    return x`
            ${e2 ? this.renderDialog() : this.renderPopover()}
            <slot name="longpress-describedby-descriptor"></slot>
        `;
  }
  connectedCallback() {
    super.connectedCallback(), this.addEventListener("close", () => {
      this.open = false;
    }), this.hasUpdated && this.bindEvents();
  }
  disconnectedCallback() {
    var e2;
    (e2 = this.strategy) == null || e2.releaseDescription(), this.open = false, super.disconnectedCallback();
  }
};
i.styles = [o], i.openCount = 1, r([n$2({
  type: Boolean
})], i.prototype, "delayed", 1), r([e$3(".dialog")], i.prototype, "dialogEl", 2), r([n$2({
  type: Boolean
})], i.prototype, "disabled", 1), r([o$4({
  flatten: true,
  selector: ':not([slot="longpress-describedby-descriptor"], slot)'
})], i.prototype, "elements", 2), r([n$2({
  type: Number
})], i.prototype, "offset", 2), r([n$2({
  type: Boolean,
  reflect: true
})], i.prototype, "open", 1), r([n$2()], i.prototype, "placement", 2), r([n$2({
  attribute: "receives-focus"
})], i.prototype, "receivesFocus", 2), r([e$3("slot")], i.prototype, "slotEl", 2), r([r$1()], i.prototype, "state", 1), r([n$2({
  type: Number,
  attribute: "tip-padding"
})], i.prototype, "tipPadding", 2), r([n$2()], i.prototype, "trigger", 2), r([n$2({
  attribute: false
})], i.prototype, "triggerElement", 2), r([n$2({
  attribute: false
})], i.prototype, "triggerInteraction", 2), r([n$2()], i.prototype, "type", 2);
var Overlay = i;
var Overlay$1 = Object.freeze({
  __proto__: null,
  Overlay,
  LONGPRESS_INSTRUCTIONS
});
var overlayCss = ":host{display:block}";
var BciOverlay = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this._shouldReopen = false;
    this.config = {
      placement: "bottom-start"
    };
    this.host = void 0;
    this.dialog = void 0;
  }
  onConfigChange(newConfig, oldConfig) {
    if (this.close !== void 0 && !!oldConfig && newConfig.placement !== oldConfig.placement) {
      this._shouldReopen = true;
      this.hide();
    }
  }
  componentDidRender() {
    if (this._shouldReopen) {
      this._shouldReopen = false;
      setTimeout(() => this.show(), 250);
    }
  }
  /**
   * try to show the overlay and return the current visibility state
   */
  show() {
    return __async(this, null, function* () {
      if (this.close === void 0 && this.host && this.dialog) {
        this.close = yield Overlay.open(this.dialog, Object.assign(Object.assign({}, this.config), {
          trigger: this.host,
          type: "manual"
        }));
        document.body.append(this.close);
        return true;
      }
      return false;
    });
  }
  /**
   * update the overlay, e.g. to fix positioning
   */
  update() {
    return __async(this, null, function* () {
      Overlay.update();
    });
  }
  /**
   * try to hide the overlay and return the current visibility state
   */
  hide() {
    return __async(this, null, function* () {
      var _a;
      if (this.close !== void 0) {
        (_a = this.close) === null || _a === void 0 ? void 0 : _a.dispose();
        this.close = void 0;
      }
      return false;
    });
  }
  render() {
    return h(Host, null, h("template", null, h("slot", null)));
  }
  static get watchers() {
    return {
      "config": ["onConfigChange"]
    };
  }
};
BciOverlay.style = overlayCss;

export {
  Overlay,
  BciOverlay
};
/*! Bundled license information:

@bci-web-core/web-components/dist/esm/bci-overlay-2df7d606.js:
  (**
   * @license
   * Copyright 2019 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
  (**
   * @license
   * Copyright 2021 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
  (**
   * @license
   * Copyright 2018 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
*/
//# sourceMappingURL=chunk-YFB3VAW5.js.map
