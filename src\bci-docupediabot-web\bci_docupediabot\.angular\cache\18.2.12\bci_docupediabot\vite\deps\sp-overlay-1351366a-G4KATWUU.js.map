{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/sp-overlay-1351366a.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { O as Overlay } from './bci-overlay-2df7d606.js';\nimport './index-93dc8059.js';\nfunction defineElement(e, n) {\n  customElements.define(e, n);\n}\ndefineElement(\"sp-overlay\", Overlay);\n\n"], "mappings": ";;;;;;;AAGA,SAAS,cAAc,GAAG,GAAG;AAC3B,iBAAe,OAAO,GAAG,CAAC;AAC5B;AACA,cAAc,cAAc,OAAO;", "names": []}