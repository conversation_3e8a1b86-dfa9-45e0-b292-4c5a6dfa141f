import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { AuthService } from '@shared/services/auth.service';
import { SysUserService } from '@shared/services/system/sys-user.service';
import { SysUserUpdate } from '@shared/models/system.model';

@Component({
  selector: 'app-token-setup-guide-dialog',
  templateUrl: './token-setup-guide-dialog.component.html',
  styleUrls: ['./token-setup-guide-dialog.component.scss']
})
export class TokenSetupGuideDialogComponent {
  tokenForm: FormGroup;
  isLoading = false;
  saveSuccess = false;
  saveError = '';

  constructor(
    private fb: FormBuilder,
    private _matDialogRef: MatDialogRef<TokenSetupGuideDialogComponent>,
    private authService: AuthService,
    private sysUserService: SysUserService
  ) {
    const currentUser = this.authService.getCurrentUser();
    this.tokenForm = this.fb.group({
      docupediaToken: [currentUser?.docupediaToken ?? '', [Validators.required, this.tokenValidator]]
    });
  }


  private tokenValidator(control: any) {
    if (!control.value) return null;

    const token = control.value.trim();


    if (token.length < 10) {
      return { tokenTooShort: true };
    }


    if (!/^[A-Za-z0-9+/=]+$/.test(token)) {
      return { invalidTokenFormat: true };
    }

    return null;
  }

  saveToken(): void {
    if (this.tokenForm.invalid || this.isLoading) return;

    this.isLoading = true;
    this.saveError = '';
    this.saveSuccess = false;

    const token = this.tokenForm.get('docupediaToken')?.value?.trim();
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser) {
      this.saveError = 'Unable to get current user information';
      this.isLoading = false;
      return;
    }

    if (token === currentUser.docupediaToken) {
      this.saveSuccess = true;
      this.isLoading = false;
      return;
    }

    const updatedUser: SysUserUpdate = {
      id: currentUser.id,
      userNTAccount: currentUser.userNTAccount,
      userName: currentUser.userName,
      givenName: currentUser.givenName,
      sn: currentUser.sn,
      mail: currentUser.mail,
      department: currentUser.department,
      favCollecitonId: currentUser.favCollecitonId,
      docupediaToken: token,
      status: currentUser.status
    };

    this.sysUserService.updateUser(updatedUser).subscribe({
      next: () => {

        const updatedUserDTO = {
          ...currentUser,
          docupediaToken: token
        };
        this.authService.updateCurrentUser(updatedUserDTO);

        this.saveSuccess = true;
        this.isLoading = false;


        setTimeout(() => {
          this._matDialogRef.close('saved');
        }, 2000);
      },
      error: (error) => {
        console.error('Failed to save token to user profile', error);
        this.saveError = 'Failed to save token. Please try again.';
        this.isLoading = false;
      }
    });
  }

  close(): void {
    this._matDialogRef.close();
  }
}
