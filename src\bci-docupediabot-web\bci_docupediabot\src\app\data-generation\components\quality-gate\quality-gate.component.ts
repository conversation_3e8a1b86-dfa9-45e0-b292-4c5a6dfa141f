import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '@bci-web-core/core';
import { QualityGateService } from '../../../shared/services/data-generation/quality-gate.service';

@Component({
  selector: 'app-quality-gate',
  templateUrl: './quality-gate.component.html',
  styleUrls: ['./quality-gate.component.scss']
})
export class QualityGateComponent implements OnInit {
  formGroup: FormGroup;
  isLoading = false;
  copyResult: any = null;

  constructor(
    private formBuilder: FormBuilder,
    private qualityGateService: QualityGateService,
    private notificationService: NotificationService
  ) {
    this.formGroup = this.formBuilder.group({
      projectTitle: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  ngOnInit(): void {
  }

  onCopyPages(): void {
    if (this.formGroup.invalid) {
      this.notificationService.warning('Please enter a valid project title');
      return;
    }

    const projectTitle = this.formGroup.get('projectTitle')?.value;
    this.isLoading = true;
    this.copyResult = null;

    this.qualityGateService.copyQualityGatePages(projectTitle).subscribe({
      next: (result) => {
        this.isLoading = false;
        this.copyResult = result;
        if (result.success) {
          this.notificationService.success('Pages copied successfully!');
        } else {
          this.notificationService.error(result.message || 'Copy failed');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Copy pages error:', error);
        this.notificationService.error('An error occurred during the copy process');
      }
    });
  }

  get projectTitle() {
    return this.formGroup.get('projectTitle');
  }
}
