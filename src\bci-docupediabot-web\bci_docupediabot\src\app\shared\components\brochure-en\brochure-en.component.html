<bci-page-content>
  <bci-master-view [isPadded]="true">
    <!-- Language Switcher -->
    <div class="language-switcher">
      <button type="button" mat-raised-button color="primary" (click)="switchToChinese()">
        切换到中文
      </button>
    </div>

    <!-- System Overview -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🌟 System Overview</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>
          <strong>BCI GenAI Platform</strong> is an enterprise-level intelligent knowledge management platform developed by Bosch RBAC BCI, specifically designed for the complex knowledge ecosystem of large manufacturing enterprises.
          The system adopts innovative <strong>PointId differential update mechanism</strong> and <strong>comprehensive chunking technology</strong>, combined with Qdrant vector database's semantic retrieval capabilities,
          achieving intelligent Q&A with rich text and images and accuracy up to 95%. Through DDD multi-tenant architecture, LDAP identity authentication and fine-grained permission control,
          it provides secure, efficient, and intelligent knowledge services for global teams.
        </p>

        <div class="unique-features">
          <h4>🎯 Innovative and Practical Design</h4>
          <ul>
            <li><strong>Deep Docupedia Integration</strong>: Deeply optimized for Docupedia, supporting over 14,000 Spaces in V1 and V2 versions</li>
            <li><strong>PointId Differential Updates</strong>: Avoid full rebuilds, implement incremental updates, saving time while significantly reducing Token costs</li>
            <li><strong>Multimodal RAG Fusion</strong>: Unified retrieval and structured output for text + images + tables, document traceability, excellent user experience</li>
            <li><strong>Semantic Vector Caching</strong>: Through partial caching mechanism, your questions always have context, even when the system is closed</li>
            <li><strong>Hybrid AI Architecture</strong>: Azure OpenAI + Ollama local deployment, compatible with multiple LLMs through configuration</li>
          </ul>
        </div>

        <!-- Core Data -->
        <div class="stats-row">
          <div class="stat-item">
            <strong>{{platformStats.users}}</strong>
            <span>Active Users</span>
          </div>
          <div class="stat-item">
            <strong>{{platformStats.space}}</strong>
            <span>Docupedia Space</span>
          </div>
          <div class="stat-item">
            <strong>{{platformStats.queries}}</strong>
            <span>Query Count</span>
          </div>
          <div class="stat-item">
            <strong>{{platformStats.accuracy}}</strong>
            <span>Accuracy Rate</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Core Features -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🚀 Core Features</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="features-grid">
          <div *ngFor="let feature of coreFeatures" class="feature-item">
            <div class="feature-header">
              <span class="feature-icon">{{feature.icon}}</span>
              <h4>{{feature.title}}</h4>
            </div>
            <p>{{feature.description}}</p>
            <div class="tech-details">
              <small>{{feature.techDetails}}</small>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Technical Architecture -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🏗️ Technical Architecture</mat-card-title>
        <mat-card-subtitle>Multi-layered architecture design based on DDD, supporting enterprise-level SAAS deployment</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="tech-stack-table">
          <table mat-table [dataSource]="techStack" class="tech-table">
            <ng-container matColumnDef="layer">
              <th mat-header-cell *matHeaderCellDef>Architecture Layer</th>
              <td mat-cell *matCellDef="let element">
                <strong>{{element.layer}}</strong>
                <div class="layer-description">{{element.description}}</div>
              </td>
            </ng-container>

            <ng-container matColumnDef="technologies">
              <th mat-header-cell *matHeaderCellDef>Core Technologies & Features</th>
              <td mat-cell *matCellDef="let element">
                <span class="tech-tags">
                  <span *ngFor="let tech of element.technologies" class="tech-chip">{{tech}}</span>
                </span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['layer', 'technologies']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['layer', 'technologies'];"></tr>
          </table>
        </div>

        <div class="architecture-highlights">
          <h4>🏗️ Architecture Design Highlights</h4>
          <div class="highlights-grid">
            <div class="highlight-item">
              <strong>DDD Domain-Driven</strong>
              <p>Clear business boundaries, high cohesion and low coupling microservice architecture</p>
            </div>
            <div class="highlight-item">
              <strong>Multi-tenant Isolation</strong>
              <p>Database-level isolation ensuring enterprise data security and performance</p>
            </div>
            <div class="highlight-item">
              <strong>Vector Retrieval Optimization</strong>
              <p>PointId mechanism enables incremental updates, avoiding full rebuild overhead</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Platform Integration Ecosystem -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🔗 Platform Integration Ecosystem</mat-card-title>
        <mat-card-subtitle>Open platform architecture with deep integration of external tools and enterprise systems</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="integrations-compact">
          <div *ngFor="let integration of integrations" class="integration-compact-item">
            <div class="integration-compact-header">
              <span class="integration-icon" [style.color]="integration.color">{{integration.icon}}</span>
              <div class="integration-compact-info">
                <h4>{{integration.name}}</h4>
                <span class="status-badge" [style.background-color]="integration.color">
                  {{integration.status}}
                </span>
              </div>
            </div>
            <p class="integration-description">{{integration.description}}</p>
            <div class="feature-tags">
              <span *ngFor="let feature of integration.features" class="feature-tag">
                ✓ {{feature}}
              </span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Real Application Scenarios -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🎯 Real Application Scenarios</mat-card-title>
        <mat-card-subtitle>Intelligent knowledge management applications in real business scenarios</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="scenarios-grid">
          <div class="scenario-category">
            <h4>🏭 Industrial Manufacturing Scenarios</h4>
            <div class="scenario-examples">
              <div class="scenario-item">
                <h5>Production Process Standard Queries</h5>
                <p><strong>Scenario</strong>: Process engineers need to quickly find production process parameters for specific products</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "What are the temperature and time requirements for motor stator winding insulation treatment process?"</div>
                  <div class="system-answer">🤖 According to "Motor Manufacturing Process Specification" Section 4.3, insulation treatment process:<br>
                  • Pre-baking temperature: 80-100°C, duration 2 hours<br>
                  • Impregnation temperature: 40-60°C, vacuum degree ≥0.08MPa<br>
                  • Curing temperature: 130-150°C, duration 8 hours<br>
                  • Cooling requirements: Natural cooling to room temperature, avoid rapid cooling</div>
                </div>
              </div>

              <div class="scenario-item">
                <h5>Equipment Fault Diagnosis Support</h5>
                <p><strong>Scenario</strong>: Maintenance technicians encounter equipment anomalies and need to quickly locate fault causes</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "CNC machine tool X-axis positioning accuracy exceeds tolerance, possible causes and solutions?"</div>
                  <div class="system-answer">🤖 According to "CNC Equipment Fault Diagnosis Manual", common causes of X-axis accuracy deviation:<br>
                  <strong>Mechanical causes</strong>: Ball screw wear, guide rail clearance too large<br>
                  <strong>Electrical causes</strong>: Encoder failure, servo parameter drift<br>
                  <strong>Inspection steps</strong>: 1.Measure backlash 2.Check encoder signal 3.Calibrate servo parameters<br>
                  <strong>Preventive measures</strong>: Regular lubrication, precision compensation, parameter backup</div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-category">
            <h4>🌱 ESG Compliance Scenarios</h4>
            <div class="scenario-examples">
              <div class="scenario-item">
                <h5>Corporate Due Diligence</h5>
                <p><strong>Scenario</strong>: Compliance teams need to quickly obtain ESG performance data of suppliers</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "How to assess new suppliers' carbon emissions and environmental compliance?"</div>
                  <div class="system-answer">🤖 According to "Supplier ESG Due Diligence Standards" V2.1, assessment framework includes:<br>
                  <strong>Environmental indicators</strong>: Carbon emission intensity, energy efficiency, waste treatment compliance<br>
                  <strong>Social indicators</strong>: Employee safety records, labor rights protection, community impact<br>
                  <strong>Governance indicators</strong>: Anti-corruption policies, transparency reports, risk management systems<br>
                  <strong>Certification requirements</strong>: ISO 14001, OHSAS 18001, SA 8000</div>
                </div>
              </div>

              <div class="scenario-item">
                <h5>Benchmark Report Analysis</h5>
                <p><strong>Scenario</strong>: Strategic planning teams need to compare industry ESG best practices</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "What are the best practices of leading companies in the automotive industry for carbon neutrality?"</div>
                  <div class="system-answer">🤖 Based on "Automotive Industry ESG Benchmark Report" 2024 analysis:<br>
                  <strong>Volvo</strong>: Full electrification by 2030, supply chain carbon neutrality commitment<br>
                  <strong>BMW</strong>: Circular economy model, 95% material recyclability<br>
                  <strong>Tesla</strong>: Vertical integration, self-built battery recycling system<br>
                  <strong>Key indicators</strong>: 30% reduction in Scope 3 emissions, 80%+ renewable energy usage</div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-category">
            <h4>🎯 Quality Management Scenarios</h4>
            <div class="scenario-examples">
              <div class="scenario-item">
                <h5>Quality Gate Document Auto-generation</h5>
                <p><strong>Scenario</strong>: Project teams need to quickly generate various QG documents that comply with development standards</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "Help me generate FMEA document template and checklist for motor project QG3 phase"</div>
                  <div class="system-answer">🤖 Based on "QG Standard Template Library" and project information, auto-generate:<br>
                  <strong>FMEA Document</strong>: Including functional analysis, failure modes, impact analysis, RPN assessment<br>
                  <strong>Checklist</strong>: Design review points, test verification items, document completeness check<br>
                  <strong>Risk Assessment</strong>: Key risk identification, mitigation measures, responsibility assignment<br>
                  <strong>Compliance Check</strong>: Auto-compare with development standards, mark missing items</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Technical Implementation Process -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>⚙️ Technical Implementation Process</mat-card-title>
        <mat-card-subtitle>Deep analysis of how the system achieves high-accuracy intelligent Q&A</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="tech-flow-container">
          <!-- Document Processing Flow -->
          <div class="flow-section">
            <h4>📄 Document Processing & Vectorization Flow</h4>
            <div class="flow-steps-horizontal">
              <div class="flow-step-compact">
                <div class="step-number">1</div>
                <h5>Smart Chunking</h5>
                <p>Semantic boundary chunking, overlapping windows, dynamic adjustment</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">2</div>
                <h5>Multimodal Extraction</h5>
                <p>OCR recognition, table parsing, image description</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">3</div>
                <h5>Batch Vectorization</h5>
                <p>Dynamic batching, quality control, anomaly detection</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">4</div>
                <h5>PointId Updates</h5>
                <p>Hash comparison, incremental updates, version management</p>
              </div>
            </div>
          </div>

          <!-- Retrieval & Recall Flow -->
          <div class="flow-section">
            <h4>🔍 Intelligent Retrieval & Recall Flow</h4>
            <div class="flow-steps-horizontal">
              <div class="flow-step-compact">
                <div class="step-number">1</div>
                <h5>Query Optimization</h5>
                <p>Intent recognition, keyword expansion, multilingual standardization</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">2</div>
                <h5>Multi-strategy Recall</h5>
                <p>Semantic retrieval, BM25, metadata filtering</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">3</div>
                <h5>Intelligent Reranking</h5>
                <p>Similarity normalization, timeliness weighting, feedback learning</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">4</div>
                <h5>Context Construction</h5>
                <p>Fragment aggregation, length control, deduplication merging</p>
              </div>
            </div>
          </div>

          <!-- Prompt Engineering & Generation -->
          <div class="flow-section">
            <h4>🎯 Prompt Engineering & Answer Generation</h4>
            <div class="flow-steps-horizontal">
              <div class="flow-step-compact">
                <div class="step-number">1</div>
                <h5>Prompt Construction</h5>
                <p>Domain templates, role definition, Few-shot examples</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">2</div>
                <h5>Hybrid AI Inference</h5>
                <p>Cloud + local, load balancing, cost optimization</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">3</div>
                <h5>Quality Control</h5>
                <p>Fact verification, relevance scoring, safety filtering</p>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Special Features Showcase -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>✨ Special Features Showcase</mat-card-title>
        <mat-card-subtitle>System's unique intelligent analysis and management capabilities</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="special-features">
          <div class="feature-showcase">
            <h4>📊 Collection Intelligent Summary</h4>
            <div class="feature-demo">
              <div class="demo-description">
                <p>The system can automatically analyze all documents in a Collection and generate intelligent summary reports</p>
                <ul>
                  <li><strong>Content Overview</strong>: Document type distribution, topic analysis, keyword cloud</li>
                  <li><strong>Quality Assessment</strong>: Document completeness, information density, update frequency</li>
                  <li><strong>Usage Statistics</strong>: Popular queries, user preferences, access trends</li>
                  <li><strong>Optimization Suggestions</strong>: Content supplement recommendations, structure optimization plans</li>
                </ul>
              </div>
              <div class="demo-example">
                <div class="summary-card">
                  <h5>📋 Collection "Automotive Manufacturing Process" Analysis Report</h5>
                  <div class="summary-stats">
                    <span class="stat">📄 Total Documents: 156</span>
                    <span class="stat">🔥 Hot Topics: Welding Process(23%), Painting Process(18%)</span>
                    <span class="stat">⚠️ Duplicates Found: 12 similar documents</span>
                    <span class="stat">📈 Query Growth: +15% (this month)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="feature-showcase">
            <h4>🔍 Intelligent Duplicate Content Detection</h4>
            <div class="feature-demo">
              <div class="demo-description">
                <p>Semantic similarity-based duplicate content detection to help optimize knowledge base quality</p>
                <ul>
                  <li><strong>Semantic Deduplication</strong>: Identify content with different expressions but same meaning</li>
                  <li><strong>Version Tracking</strong>: Detect different versions of the same document</li>
                  <li><strong>Similarity Scoring</strong>: Quantify content duplication degree</li>
                  <li><strong>Merge Suggestions</strong>: Provide content integration solutions</li>
                </ul>
              </div>
              <div class="demo-example">
                <div class="duplicate-detection">
                  <h5>🚨 Duplicate Content Detected</h5>
                  <div class="duplicate-item">
                    <div class="similarity-score">Similarity: 87%</div>
                    <div class="doc-pair">
                      <span>📄 "Welding Safety Operating Procedures_V1.2.pdf"</span>
                      <span>📄 "Electric Welding Safety Standards_2024.docx"</span>
                    </div>
                    <div class="suggestion">💡 Suggestion: Merge into unified safety operating standards</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="feature-showcase">
            <h4>📈 Knowledge Graph Relationship Analysis</h4>
            <div class="feature-demo">
              <div class="demo-description">
                <p>Automatically build knowledge relationship networks to discover hidden knowledge connections</p>
                <ul>
                  <li><strong>Entity Recognition</strong>: Automatically extract key concepts and entities</li>
                  <li><strong>Relationship Mining</strong>: Discover associative relationships between concepts</li>
                  <li><strong>Knowledge Recommendation</strong>: Recommend related content based on associations</li>
                  <li><strong>Gap Discovery</strong>: Identify blank spots in knowledge systems</li>
                </ul>
              </div>
              <div class="demo-example">
                <div class="knowledge-graph">
                  <h5>🕸️ "Motor Manufacturing" Knowledge Relationship Graph</h5>
                  <div class="graph-nodes">
                    <span class="node primary">Motor Manufacturing</span>
                    <span class="node">→ Winding Process</span>
                    <span class="node">→ Quality Testing</span>
                    <span class="node">→ Safety Standards</span>
                    <span class="node missing">❓ Environmental Requirements (Missing)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

  </bci-master-view>
</bci-page-content>
