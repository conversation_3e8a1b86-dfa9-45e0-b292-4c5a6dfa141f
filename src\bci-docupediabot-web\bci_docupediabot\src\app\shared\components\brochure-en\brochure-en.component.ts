import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-brochure-en',
  templateUrl: './brochure-en.component.html',
  styleUrls: ['./brochure-en.component.scss']
})
export class BrochureEnComponent implements OnInit {

  // Platform core data
  platformStats = {
    users: '1000 +',
    space: '2000 +',
    queries: '10000 +',
    accuracy: '95% +'
  };

  // Core features
  coreFeatures = [
    {
      icon: '🔒',
      title: 'Enterprise Security',
      description: 'SSO Single Sign-On + JWT Token + Multi-tenant Isolation',
      techDetails: 'Role-based access control (RBAC), supports AD domain integration, database-level tenant isolation',
      color: 'primary'
    },
    {
      icon: '🧠',
      title: 'Intelligent Retrieval',
      description: 'PointId Differential Updates + Semantic Understanding',
      techDetails: 'Qdrant vector database, supports incremental/batch updates, avoids full rebuilds, 95%+ retrieval accuracy',
      color: 'accent'
    },
    {
      icon: '🔗',
      title: 'Platform Integration',
      description: 'RAGFlow + n8n + Docupedia + Community',
      techDetails: 'Open API architecture, supports multimodal, Webhook, workflow, extensible third-party tool integration',
      color: 'warn'
    },
    {
      icon: '⚡',
      title: 'High Performance',
      description: 'PostgreSQL + Qdrant + Intelligent Caching',
      techDetails: 'Distributed architecture, Redis caching, supports 100+ concurrent users',
      color: 'primary'
    },
    {
      icon: '🌐',
      title: 'Multilingual',
      description: 'Semantic retrieval in 20+ common foreign languages',
      techDetails: 'Multilingual vector models, cross-language semantic understanding, supports mixed-language queries',
      color: 'accent'
    },
    {
      icon: '☁️',
      title: 'Hybrid AI',
      description: 'Azure OpenAI + Ollama Local',
      techDetails: 'Cloud + local hybrid deployment, secure and controllable data, cost optimization, offline availability',
      color: 'warn'
    },
    {
      icon: '🏢',
      title: 'SAAS Architecture',
      description: 'Multi-tenant + DDD + Microservices',
      techDetails: 'Domain-Driven Design, clear business boundaries, high cohesion and low coupling',
      color: 'primary'
    },
    {
      icon: '📊',
      title: 'Intelligent Insights',
      description: 'Content Analysis + Duplicate Detection + Knowledge Graph',
      techDetails: 'NLP content analysis, automatic deduplication, knowledge relationship discovery, usage trend analysis',
      color: 'accent'
    }
  ];

  // Technical architecture layers
  techStack = [
    {
      layer: 'Frontend Layer',
      description: 'Modern web interface, responsive design, PWA support',
      technologies: ['Angular 17', 'Material Design', 'TypeScript', 'PWA Offline Support'],
      color: '#e3f2fd'
    },
    {
      layer: 'API Layer',
      description: 'RESTful API design, JWT security authentication, complete API documentation',
      technologies: ['.NET 8.0', 'ASP.NET Core', 'JWT Authentication', 'Swagger Documentation'],
      color: '#f3e5f5'
    },
    {
      layer: 'Business Layer',
      description: 'Domain-driven design, clear business boundaries, high cohesion and low coupling',
      technologies: ['DDD Architecture', 'Domain Services', 'Application Services', 'Dependency Injection'],
      color: '#e8f5e8'
    },
    {
      layer: 'Data Layer',
      description: 'Enterprise-grade database, multi-tenant architecture, high-performance query optimization',
      technologies: ['PostgreSQL', 'Entity Framework', 'Multi-tenant Isolation', 'Index Optimization'],
      color: '#fff3e0'
    },
    {
      layer: 'Vector Layer',
      description: 'High-performance vector retrieval, incremental update mechanism, semantic search',
      technologies: ['Qdrant Vector DB', 'gRPC Communication', 'PointId Differential Updates', 'Metadata Indexing'],
      color: '#fce4ec'
    },
    {
      layer: 'AI Layer',
      description: 'Hybrid AI architecture, cloud + local deployment, multi-model support',
      technologies: ['Azure LLM', 'Ollama Local', 'Multi-model Switching'],
      color: '#e0f2f1'
    }
  ];

  // Platform integration ecosystem
  integrations = [
    {
      name: 'Docupedia V1/V2',
      description: 'Enterprise knowledge management system',
      features: ['Deep API Integration', 'Permission Sync', 'URL Smart Parsing', 'Real-time Updates'],
      icon: '📚',
      status: 'Integrated',
      color: '#4caf50'
    },
    {
      name: 'Community / Wikis',
      description: 'Bosch community knowledge base',
      features: ['Deep API Integration', 'Permission Sync', 'URL Smart Parsing', 'Real-time Updates'],
      icon: '📚',
      status: 'Planned',
      color: '#ff9800'
    },
    {
      name: 'RAGFlow',
      description: 'Multimodal document processing platform',
      features: ['PDF Smart Parsing', 'Image Content Extraction', 'Video Transcription Analysis', 'Table Data Structuring'],
      icon: '📄',
      status: 'Planned',
      color: '#ff9800'
    },
    {
      name: 'n8n',
      description: 'Automated workflow engine',
      features: ['Web Content Crawling', 'Scheduled Task Management', 'Smart Notification Push', 'API Interface Orchestration'],
      icon: '🔄',
      status: 'Planned',
      color: '#ff9800'
    }
  ];



  constructor(private router: Router) { }

  ngOnInit(): void {
    // Component initialization logic
  }

  switchToChinese(): void {
    this.router.navigate(['/brochure']);
  }

}
