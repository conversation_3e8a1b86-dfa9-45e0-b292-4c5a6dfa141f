import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ApiResponse } from '@shared/models/docupedia.model';
import { QualityGateResponse } from '@shared/models/data-generation';
export interface QualityGateRequest {
  projectTitle: string;
}

@Injectable({
  providedIn: 'root'
})
export class QualityGateService {
  private apiUrl = `${environment.baseUrl}/api/qualitygate`;

  constructor(private http: HttpClient) { }

  copyQualityGatePages(projectTitle: string): Observable<QualityGateResponse> {
    const request: QualityGateRequest = {
      projectTitle
    };

    return this.http.post<ApiResponse<QualityGateResponse>>(`${this.apiUrl}`, request).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }
}
