<bci-page-content>
  <bci-master-view [isPadded]="false">
    <bci-commandbar
      [itemCount]="itemCount"
      [selectedItemCount]="selectedGroups.length"
      [showSearch]="true"
      [showAdd]="true"
      [showDelete]="selectedGroups.length > 0"
      class="commandbar-padding"
      (clickAdd)="addGroup()"
      (clickDelete)="deleteSelectedGroups()"
      (search)="onSearchChange($event)">
    </bci-commandbar>

    <h6>Group Management: Manage system groups and their user assignments.</h6>

    <mat-table [dataSource]="filteredGroups" class="group-table">
      <!-- Selection Column -->
      <ng-container matColumnDef="select">
        <mat-header-cell *matHeaderCellDef>
          <mat-checkbox
            (change)="$event ? masterToggle() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </mat-header-cell>
        <mat-cell *matCellDef="let group">
          <mat-checkbox
            (click)="$event.stopPropagation()"
            (change)="$event ? selection.toggle(group) : null"
            [checked]="selection.isSelected(group)">
          </mat-checkbox>
        </mat-cell>
      </ng-container>

      <!-- Group Name Column -->
      <ng-container matColumnDef="name">
        <mat-header-cell *matHeaderCellDef>Group Name</mat-header-cell>
        <mat-cell *matCellDef="let group">{{ group.name }}</mat-cell>
      </ng-container>

      <!-- Size Column -->
      <ng-container matColumnDef="size">
        <mat-header-cell *matHeaderCellDef class="text-center">Max Size</mat-header-cell>
        <mat-cell *matCellDef="let group" class="text-center">{{ group.size }}</mat-cell>
      </ng-container>

      <!-- Current Users Count Column -->
      <ng-container matColumnDef="currentUsers">
        <mat-header-cell *matHeaderCellDef class="text-center">Current Users</mat-header-cell>
        <mat-cell *matCellDef="let group" class="text-center">
          <span class="user-count" [class.full]="group.users.length >= group.size">
            {{ group.users.length }}
          </span>
        </mat-cell>
      </ng-container>

      <!-- Users Column -->
      <ng-container matColumnDef="users">
        <mat-header-cell *matHeaderCellDef>Users</mat-header-cell>
        <mat-cell *matCellDef="let group">
          <div class="users-container">
            <span *ngFor="let user of group.users.slice(0, 3); let last = last" class="user-tag">
              {{ user.userName }}<span *ngIf="!last">, </span>
            </span>
            <span *ngIf="group.users.length > 3" class="more-users">
              +{{ group.users.length - 3 }} more
            </span>
          </div>
        </mat-cell>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <mat-header-cell *matHeaderCellDef class="actions-header">Actions</mat-header-cell>
        <mat-cell *matCellDef="let group" class="actions-cell">
          <div class="action-buttons">
            <button mat-icon-button class="action-btn view" title="View Users" (click)="viewGroupUsers(group); $event.stopPropagation()">
              <mat-icon fontIcon="bosch-ic-group"></mat-icon>
            </button>
            <button mat-icon-button class="action-btn edit" title="Edit Group" (click)="editGroup(group); $event.stopPropagation()">
              <mat-icon fontIcon="bosch-ic-settings-editor"></mat-icon>
            </button>
            <button mat-icon-button class="action-btn delete" title="Delete Group" (click)="deleteGroup(group); $event.stopPropagation()">
              <mat-icon fontIcon="Bosch-Ic-delete"></mat-icon>
            </button>
          </div>
        </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;" (click)="viewGroupDetails(row)"></mat-row>
    </mat-table>

    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div *ngIf="!loading && filteredGroups.length === 0" class="no-data-container">
      <p>No groups found.</p>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" *ngIf="itemCount > 0">
      <bci-paginator [showPageSizeSelector]="true" [length]="itemCount || 0" [pageIndex]="pageNumber || 0"
        [pageSize]="pageSize || 1" [pageSizeOptions]="pageSizeOptions" (page)="onPageChange($any($event).detail)">
      </bci-paginator>
    </div>
  </bci-master-view>
</bci-page-content>
