import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewContainerRef } from '@angular/core';
import { SelectionModel } from '@angular/cdk/collections';
import { SysGroupResponseDTO } from '@shared/models/system.model';
import { SysGroupService } from '@shared/services/system/sys-group.service';
import { ModalWindowService, AlertService, AlertBoxOptions, AlertType, PrimaryButton } from '@bci-web-core/core';
import { SysGroupFormDialogComponent } from '../../dialogs/sys-group-form-dialog/sys-group-form-dialog.component';
import { DataRefreshService } from '@shared/services/data-refresh.service';
import { forkJoin, Observable, Subscription, Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';


export interface PageEvent {
  pageIndex: number;
  pageSize: number;
}

@Component({
  selector: 'app-sys-group-list',
  templateUrl: './sys-group-list.component.html',
  styleUrls: ['./sys-group-list.component.scss']
})
export class SysGroupListComponent implements OnInit, OnDestroy {
  groups: SysGroupResponseDTO[] = [];
  filteredGroups: SysGroupResponseDTO[] = [];
  loading = false;
  searchTerm = '';
  private refreshSubscription: Subscription;


  pageNumber: number = 0;
  pageSize: number = 10;
  itemCount: number = 0;
  pageSizeOptions: number[] = [5, 10, 20, 50];


  private searchSubject = new Subject<string>();
  private subscription = new Subscription();

  displayedColumns: string[] = [
    'select',
    'name',
    'size',
    'currentUsers',
    'users',
    'actions'
  ];

  selection = new SelectionModel<SysGroupResponseDTO>(true, []);

  constructor(
    private sysGroupService: SysGroupService,
    private modalWindowService: ModalWindowService,
    private dataRefreshService: DataRefreshService,
    private alertService: AlertService,
    private viewContainerRef: ViewContainerRef
  ) {}

  ngOnInit(): void {

    this.subscription.add(this.searchSubject.pipe(debounceTime(1000)).subscribe((searchTerm) => {
      this.searchTerm = searchTerm;
      this.pageNumber = 0;
      this.loadGroups();
    }));

    this.loadGroups();


    this.refreshSubscription = this.dataRefreshService.groupDataRefresh$.subscribe(() => {
      this.loadGroups();
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  loadGroups(): void {
    this.loading = true;
    this.sysGroupService.getGroups().subscribe({
      next: (groups) => {
        this.groups = groups;
        this.applyFilterAndPagination();
        this.loading = false;

        this.selection.clear();
      },
      error: (error) => {
        console.error('Error loading groups:', error);
        this.loading = false;
      }
    });
  }


  onSearchChange(searchTerm: string): void {
    this.searchSubject.next(searchTerm);
  }

  applyFilterAndPagination(): void {

    let filteredData = this.groups;
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filteredData = this.groups.filter(group =>
        group.name.toLowerCase().includes(term) ||
        group.users.some(user =>
          user.userNTAccount.toLowerCase().includes(term) ||
          user.userName.toLowerCase().includes(term) ||
          user.givenName.toLowerCase().includes(term)
        )
      );
    }


    this.itemCount = filteredData.length;


    const startIndex = this.pageNumber * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredGroups = filteredData.slice(startIndex, endIndex);

    this.selection.clear();
  }

  get selectedGroups(): SysGroupResponseDTO[] {
    return this.selection.selected;
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.filteredGroups.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected() ?
      this.selection.clear() :
      this.filteredGroups.forEach(row => this.selection.select(row));
  }

  addGroup(): void {
    const dialogRef = this.modalWindowService.openDialogWithComponent(
      SysGroupFormDialogComponent,
      {
        data: { mode: 'add' }
      }
    );

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadGroups();
      }
    });
  }

  editGroup(group: SysGroupResponseDTO): void {
    const dialogRef = this.modalWindowService.openDialogWithComponent(
      SysGroupFormDialogComponent,
      {
        data: { mode: 'edit', group: group }
      }
    );

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadGroups();
      }
    });
  }

  deleteGroup(group: SysGroupResponseDTO): void {
    const alertBoxOptions: AlertBoxOptions = {
      title: 'Delete Group',
      description: `Are you sure you want to delete group "${group.name}"?`,
      infoText: 'This action cannot be undone.',
      type: AlertType.Warning,
      primaryButton: PrimaryButton.RightButton,
      leftButtonTitle: 'Delete',
      rightButtonTitle: 'Cancel',
      outerLeftButtonDisabled: false,
      leftButtonDisabled: false,
      rightButtonDisabled: false,
    };

    const ref = this.alertService.openAlertBox(alertBoxOptions, this.viewContainerRef, '500px');
    ref.afterClosed().subscribe((res) => {
      if (res === "left") {
        this.sysGroupService.deleteGroup(group.id).subscribe({
          next: () => {
            this.dataRefreshService.refreshAllData();
          },
          error: (error) => {
            console.error('Error deleting group:', error);
          }
        });
      }
    });
  }

  deleteSelectedGroups(): void {
    if (this.selectedGroups.length === 0) return;

    const groupNames = this.selectedGroups.map(g => g.name).join(', ');
    const alertBoxOptions: AlertBoxOptions = {
      title: 'Delete Groups',
      description: `Are you sure you want to delete ${this.selectedGroups.length} group(s): ${groupNames}?`,
      infoText: 'This action cannot be undone.',
      type: AlertType.Warning,
      primaryButton: PrimaryButton.RightButton,
      leftButtonTitle: 'Delete',
      rightButtonTitle: 'Cancel',
      outerLeftButtonDisabled: false,
      leftButtonDisabled: false,
      rightButtonDisabled: false,
    };

    const ref = this.alertService.openAlertBox(alertBoxOptions, this.viewContainerRef, '500px');
    ref.afterClosed().subscribe((res) => {
      if (res === "left") {
        const deleteObservables = this.selectedGroups.map(group =>
          this.sysGroupService.deleteGroup(group.id)
        );

        const requests: { [key: string]: Observable<any> } = {};
        deleteObservables.forEach((obs, index) => {
          requests[`delete_${index}`] = obs;
        });

        forkJoin(requests).subscribe({
          next: () => {
            this.dataRefreshService.refreshAllData();
          },
          error: (error) => {
            console.error('Error deleting groups:', error);
          }
        });
      }
    });
  }

  viewGroupUsers(group: SysGroupResponseDTO): void {

    console.log('View users for group:', group.name, group.users);
  }

  viewGroupDetails(group: SysGroupResponseDTO): void {

    this.editGroup(group);
  }


  onPageChange(pageEvent: PageEvent): void {

    if (pageEvent.pageIndex === this.pageNumber && pageEvent.pageSize === this.pageSize) {
      return;
    }
    this.pageNumber = pageEvent.pageIndex;
    this.pageSize = pageEvent.pageSize;
    this.applyFilterAndPagination();
  }
}
