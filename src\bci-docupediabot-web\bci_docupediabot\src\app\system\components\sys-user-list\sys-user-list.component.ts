import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { SelectionModel } from '@angular/cdk/collections';
import { SysUserResponseDTO } from '@shared/models/system.model';
import { SysUserService } from '@shared/services/system/sys-user.service';
import { ModalWindowService } from '@bci-web-core/core';
import { UserGroupAssignDialogComponent } from '../../dialogs/user-group-assign-dialog/user-group-assign-dialog.component';
import { DataRefreshService } from '@shared/services/data-refresh.service';
import { Subscription, Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';


export interface PageEvent {
  pageIndex: number;
  pageSize: number;
}

@Component({
  selector: 'app-sys-user-list',
  templateUrl: './sys-user-list.component.html',
  styleUrls: ['./sys-user-list.component.scss']
})
export class SysUserListComponent implements OnInit, OnDestroy {
  users: SysUserResponseDTO[] = [];
  filteredUsers: SysUserResponseDTO[] = [];
  loading = false;
  searchTerm = '';
  private refreshSubscription: Subscription;


  pageNumber: number = 0;
  pageSize: number = 10;
  itemCount: number = 0;
  pageSizeOptions: number[] = [5, 10, 20, 50];


  private searchSubject = new Subject<string>();
  private subscription = new Subscription();

  displayedColumns: string[] = [
    'select',
    'userNTAccount',
    'userName',
    'givenName',
    'department',
    'status',
    'groups'
  ];

  selection = new SelectionModel<SysUserResponseDTO>(true, []);

  constructor(
    private sysUserService: SysUserService,
    private modalWindowService: ModalWindowService,
    private dataRefreshService: DataRefreshService
  ) {}

  ngOnInit(): void {

    this.subscription.add(this.searchSubject.pipe(debounceTime(1000)).subscribe((searchTerm) => {
      this.searchTerm = searchTerm;
      this.pageNumber = 0;
      this.loadUsers();
    }));

    this.loadUsers();


    this.refreshSubscription = this.dataRefreshService.userDataRefresh$.subscribe(() => {
      this.loadUsers();
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  loadUsers(): void {
    this.loading = true;
    this.sysUserService.getUsers().subscribe({
      next: (users) => {
        this.users = users;
        this.applyFilterAndPagination();
        this.loading = false;

        this.selection.clear();
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.loading = false;
      }
    });
  }


  onSearchChange(searchTerm: string): void {
    this.searchSubject.next(searchTerm);
  }

  applyFilterAndPagination(): void {

    let filteredData = this.users;
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filteredData = this.users.filter(user =>
        user.userNTAccount.toLowerCase().includes(term) ||
        user.userName.toLowerCase().includes(term) ||
        user.givenName.toLowerCase().includes(term) ||
        user.department.toLowerCase().includes(term) ||
        (user.status === 1 ? 'active' : 'inactive').includes(term)
      );
    }


    this.itemCount = filteredData.length;


    const startIndex = this.pageNumber * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredUsers = filteredData.slice(startIndex, endIndex);

    this.selection.clear();
  }

  get selectedUsers(): SysUserResponseDTO[] {
    return this.selection.selected;
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.filteredUsers.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected() ?
      this.selection.clear() :
      this.filteredUsers.forEach(row => this.selection.select(row));
  }

  assignGroupsToSelected(): void {
    if (this.selectedUsers.length === 0) return;

    const dialogRef = this.modalWindowService.openDialogWithComponent(
      UserGroupAssignDialogComponent,
      {
        data: { users: this.selectedUsers, mode: 'batch' }
      }
    );

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUsers();
        this.selection.clear();
      }
    });
  }

  assignGroups(user: SysUserResponseDTO): void {
    const dialogRef = this.modalWindowService.openDialogWithComponent(
      UserGroupAssignDialogComponent,
      {
        data: { users: [user], mode: 'single' }
      }
    );

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUsers();
      }
    });
  }


  onPageChange(pageEvent: PageEvent): void {

    if (pageEvent.pageIndex === this.pageNumber && pageEvent.pageSize === this.pageSize) {
      return;
    }
    this.pageNumber = pageEvent.pageIndex;
    this.pageSize = pageEvent.pageSize;
    this.applyFilterAndPagination();
  }
}
