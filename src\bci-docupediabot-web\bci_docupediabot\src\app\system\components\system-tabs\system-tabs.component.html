<nav mat-tab-nav-bar>
  <a
    mat-tab-link
    routerLink="users"
    routerLinkActive="active-link"
    (click)="activeLink = 'users'"
    [class.active]="activeLink === 'users'"
  >
    User Management
  </a>
  <a
    mat-tab-link
    routerLink="groups"
    routerLinkActive="active-link"
    (click)="activeLink = 'groups'"
    [class.active]="activeLink === 'groups'"
  >
    Group Management
  </a>
</nav>
<div class="router-content">
  <router-outlet></router-outlet>
</div>
