import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NavigationEnd, Router, ActivatedRoute } from '@angular/router';
import { filter, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-system-tabs',
  templateUrl: './system-tabs.component.html',
  styleUrls: ['./system-tabs.component.scss']
})
export class SystemTabsComponent implements OnInit, OnDestroy {

  constructor(private router: Router, private readonly activatedRoute: ActivatedRoute) {}

  activeLink: string = '';

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.activeLink = this.activatedRoute.firstChild?.snapshot.url[0]?.path || 'users';
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        this.activeLink = this.activatedRoute.firstChild?.snapshot.url[0]?.path || 'users';
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
