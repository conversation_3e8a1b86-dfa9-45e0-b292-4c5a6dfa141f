<bci-modal-component
  [title]="dialogTitle"
  [closeIcon]="true"
  (closeHandler)="close()">
  <p class="modal-subheading">{{ dialogSubtitle }}</p>
  <form [formGroup]="groupForm" class="modal-form">
    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Group Name *</mat-label>
        <input matInput formControlName="name" placeholder="Enter Group Name" />
        <mat-error *ngIf="groupForm.get('name')?.hasError('required')">
          Group Name is required
        </mat-error>
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Max <PERSON>ze *</mat-label>
        <input matInput formControlName="size" placeholder="Enter Max Size" type="number" min="1" />
        <mat-error *ngIf="groupForm.get('size')?.hasError('required')">
          <PERSON> is required
        </mat-error>
        <mat-error *ngIf="groupForm.get('size')?.hasError('min')">
          <PERSON> must be at least 1
        </mat-error>
      </mat-form-field>
    </div>
  </form>

  <div class="subpage-actions">
    <button bciPrimaryButton
            [disabled]="!groupForm.valid || loading"
            (click)="save()">
      {{ loading ? 'Saving...' : 'Save' }}
    </button>
    <button bciSecondaryButton
            (click)="close()">
      Cancel
    </button>
  </div>
</bci-modal-component>
