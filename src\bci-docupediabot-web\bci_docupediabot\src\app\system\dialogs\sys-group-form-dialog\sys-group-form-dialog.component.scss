@import "@bci-web-core/common-styles/sass/bci/bci-variables";

.modal-subheading {
  font-size: 16px;
  color: #666;
  margin-bottom: $grid-size * 2;
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: $grid-size;
  padding: $grid-size * 2 $grid-size * 3;
}

.form-row {
  display: flex;
  gap: $grid-size * 2;
  width: 100%;
}

.half-width {
  width: 50%;
}

mat-form-field {
  margin-bottom: 0;
  .mdc-text-field--filled {
    background-color: #f5f5f5 !important;
  }
}

.subpage-actions {
  display: flex;
  justify-content: flex-end;
  gap: $grid-size * 2;
  padding: $grid-size * 2 $grid-size 0 0;
}

button[bciPrimaryButton],
button[bciSecondaryButton] {
  min-width: 80px;
  height: 40px;
}
