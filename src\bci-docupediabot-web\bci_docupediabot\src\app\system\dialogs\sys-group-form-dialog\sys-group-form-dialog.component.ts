import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SysGroupResponseDTO, SysGroupAdd, SysGroupUpdate } from '@shared/models/system.model';
import { SysGroupService } from '@shared/services/system/sys-group.service';
import { DataRefreshService } from '@shared/services/data-refresh.service';

interface DialogData {
  mode: 'add' | 'edit';
  group?: SysGroupResponseDTO;
}

@Component({
  selector: 'app-sys-group-form-dialog',
  templateUrl: './sys-group-form-dialog.component.html',
  styleUrls: ['./sys-group-form-dialog.component.scss']
})
export class SysGroupFormDialogComponent implements OnInit {
  groupForm: FormGroup;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private sysGroupService: SysGroupService,
    private dataRefreshService: DataRefreshService,
    private dialogRef: MatDialogRef<SysGroupFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.groupForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.data.mode === 'edit' && this.data.group) {
      this.populateForm(this.data.group);
    }
  }

  get dialogTitle(): string {
    return this.data.mode === 'add' ? 'Add New Group' : 'Edit Group';
  }

  get dialogSubtitle(): string {
    return this.data.mode === 'add'
      ? 'Enter the group information below.'
      : 'Update the group information below.';
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required]],
      size: [1, [Validators.required, Validators.min(1)]]
    });
  }

  private populateForm(group: SysGroupResponseDTO): void {
    this.groupForm.patchValue({
      name: group.name,
      size: group.size
    });
  }

  save(): void {
    if (this.groupForm.valid) {
      this.loading = true;

      if (this.data.mode === 'add') {
        this.addGroup();
      } else {
        this.updateGroup();
      }
    }
  }

  private addGroup(): void {
    const groupData: SysGroupAdd = this.groupForm.value;

    this.sysGroupService.addGroup(groupData).subscribe({
      next: (result) => {
        this.loading = false;

        this.dataRefreshService.refreshGroupData();
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Error adding group:', error);
        this.loading = false;
      }
    });
  }

  private updateGroup(): void {
    if (!this.data.group) return;

    const groupData: SysGroupUpdate = {
      id: this.data.group.id,
      ...this.groupForm.value
    };

    this.sysGroupService.updateGroup(groupData).subscribe({
      next: (result) => {
        this.loading = false;

        this.dataRefreshService.refreshGroupData();
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Error updating group:', error);
        this.loading = false;
      }
    });
  }

  close(): void {
    this.dialogRef.close(false);
  }
}
