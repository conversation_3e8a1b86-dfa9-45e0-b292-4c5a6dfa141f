<bci-modal-component
  [title]="dialogTitle"
  [closeIcon]="true"
  (closeHandler)="close()">
  <p class="modal-subheading">{{ dialogSubtitle }}</p>
  <form [formGroup]="userForm" class="modal-form">
    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>NT Account *</mat-label>
        <input matInput formControlName="userNTAccount" placeholder="Enter NT Account" />
        <mat-error *ngIf="userForm.get('userNTAccount')?.hasError('required')">
          NT Account is required
        </mat-error>
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>User Name *</mat-label>
        <input matInput formControlName="userName" placeholder="Enter User Name" />
        <mat-error *ngIf="userForm.get('userName')?.hasError('required')">
          User Name is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Given Name</mat-label>
        <input matInput formControlName="givenName" placeholder="Enter Given Name" />
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Surname</mat-label>
        <input matInput formControlName="sn" placeholder="Enter Surname" />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Email *</mat-label>
        <input matInput formControlName="mail" placeholder="Enter Email" type="email" />
        <mat-error *ngIf="userForm.get('mail')?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="userForm.get('mail')?.hasError('email')">
          Please enter a valid email
        </mat-error>
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Department</mat-label>
        <input matInput formControlName="department" placeholder="Enter Department" />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Favorite Collection</mat-label>
        <mat-select formControlName="favCollecitonId">
          <mat-option [value]="null">None</mat-option>
          <mat-option *ngFor="let collection of collections" [value]="collection.id">
            {{ collection.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Docupedia Token</mat-label>
        <input matInput formControlName="docupediaToken" placeholder="Enter Docupedia Token" />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Status</mat-label>
        <mat-select formControlName="status">
          <mat-option [value]="1">Active</mat-option>
          <mat-option [value]="0">Inactive</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </form>

  <div class="subpage-actions">
    <button bciPrimaryButton
            [disabled]="!userForm.valid || loading"
            (click)="save()">
      {{ loading ? 'Saving...' : 'Save' }}
    </button>
    <button bciSecondaryButton
            (click)="close()">
      Cancel
    </button>
  </div>
</bci-modal-component>
