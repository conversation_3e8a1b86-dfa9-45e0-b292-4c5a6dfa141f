@import "@bci-web-core/common-styles/sass/bci/bci-variables";

.modal-subheading {
  font-size: 16px;
  color: #666;
  margin-bottom: $grid-size * 2;
}

.groups-container {
  padding: $grid-size * 2 $grid-size * 3;
  max-height: 400px;
  overflow-y: auto;
}

.groups-list {
  display: flex;
  flex-direction: column;
  gap: $grid-size;
}

.group-item {
  padding: $grid-size;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  
  &:hover {
    background-color: #f5f5f5;
  }
}

.group-info {
  display: flex;
  flex-direction: column;
  margin-left: $grid-size;
}

.group-name {
  font-weight: 500;
  color: #333;
}

.group-details {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $grid-size * 4;
}

.no-groups {
  text-align: center;
  color: #666;
  padding: $grid-size * 4;
}

.subpage-actions {
  display: flex;
  justify-content: flex-end;
  gap: $grid-size * 2;
  padding: $grid-size * 2 $grid-size 0 0;
}

button[bciPrimaryButton],
button[bciSecondaryButton] {
  min-width: 80px;
  height: 40px;
}

h6 {
  margin-bottom: $grid-size;
  color: #333;
  font-weight: 600;
}
